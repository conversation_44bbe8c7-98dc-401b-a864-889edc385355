---
type: 'manual'
---

# OCTI 智能体配置开发规则

## 适用场景

当开发涉及智能体配置、问卷生成、组织评估等核心业务逻辑时使用此规则。

## 智能体配置规范

### 配置文件结构

```typescript
interface OCTIAgentConfig {
  metadata: {
    version: string;
    name: string;
    type: 'question_designer' | 'organization_tutor';
  };

  llm: {
    provider: 'minimax' | 'deepseek';
    model: string;
    temperature: number;
    maxTokens: number;
  };

  octiFramework: {
    dimensions: ['S/F', 'I/T', 'M/V', 'A/D'];
    assessmentType: 'standard' | 'professional';
    questionCount: 60;
  };
}
```

## 关键OCTI约定

1. **60题固定**: 所有评估必须包含60道题目
2. **四维八极**: 严格按照S/F、I/T、M/V、A/D框架
3. **版本差异**: 标准版和专业版功能明确区分
4. **配置热更新**: 支持无重启配置更新

## 配置验证

```typescript
const OCTIConfigSchema = z.object({
  octiFramework: z.object({
    dimensions: z.array(z.string()).length(4),
    questionCount: z.literal(60),
  }),
});
```
