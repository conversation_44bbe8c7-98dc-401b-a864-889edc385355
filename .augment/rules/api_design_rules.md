---
type: 'manual'
---

# OCTI API设计规则

## 适用场景

当开发API路由、数据库操作、外部服务集成时使用此规则。

## RESTful API约定

- **评估管理**: `/api/v1/assessments`
- **问卷生成**: `/api/v1/agents/question-designer`
- **结果分析**: `/api/v1/agents/organization-tutor`
- **配置管理**: `/api/v1/config/{agentType}`

## 统一响应格式

```typescript
interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
    details?: any;
  };
  meta?: {
    timestamp: string;
    requestId: string;
  };
}
```

## 数据库规范

```prisma
model Assessment {
  id           String   @id @default(cuid())
  userId       String
  type         AssessmentType
  responses    Json?
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  @@map("assessments")
}
```

## 安全规范

- API密钥必须在服务端环境变量中管理
- 所有输入必须通过Zod验证
- 实现API限流防止恶意调用
