---
type: 'always_apply'
---

---

type: "manual"---

# OCTI 智能评估系统 - 核心开发规则

## 项目概述

OCTI（Organization Capability Type
Indicator）智能评估系统v4.0是一个基于配置驱动和智能体模块化的组织能力评估平台。

## 核心技术栈

- **前端框架**: Next.js 14+ with TypeScript (App Router)
- **UI 组件库**: Tailwind CSS + Shadcn/ui
- **状态管理**: Zustand
- **数据库**: PostgreSQL + Prisma ORM
- **AI集成**: MiniMax API + DeepSeek API

## 基础开发规范

- 所有响应必须使用中文，代码注释使用JSDoc格式
- 系统图表使用mermaid格式展示
- 新创建的文档和目录，都需要明确指明其文件路径
- 严格使用TypeScript，避免any类型
- 组件文件不超过200行，函数不超过50行

## 命名规范

- 文件名：kebab-case（如：`question-designer.ts`）
- 组件名：PascalCase（如：`QuestionDesigner`）
- 变量名：camelCase（如：`questionData`）
- 常量名：UPPER_SNAKE_CASE（如：`API_BASE_URL`）

## 安全要求

- LLM API调用必须在后端进行
- 使用Zod验证所有输入
- 敏感数据加密存储
