# OCTI智能评估系统 - 环境变量配置模板
# 复制此文件为 .env 并填入实际值

# ============================================================================
# 应用基础配置
# ============================================================================
NODE_ENV=development
PORT=3000
DOMAIN=localhost

# ============================================================================
# 数据库配置
# ============================================================================
# PostgreSQL数据库连接
DATABASE_URL="postgresql://octi_user:octi_password@localhost:5432/octi_db"

# 数据库凭据（Docker使用）
POSTGRES_DB=octi_db
POSTGRES_USER=octi_user
POSTGRES_PASSWORD=octi_password

# ============================================================================
# 缓存配置
# ============================================================================
# Redis连接
REDIS_URL="redis://:redis_password@localhost:6379"
REDIS_PASSWORD=redis_password

# ============================================================================
# 身份认证配置
# ============================================================================
# NextAuth.js配置
NEXT_PUBLIC_APP_URL="http://localhost:3000"
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="your_nextauth_secret_32_chars_minimum"

# JWT密钥
JWT_SECRET="your_jwt_secret_key_here"

# ============================================================================
# AI服务配置
# ============================================================================
# MiniMax API配置
MINIMAX_API_KEY="your_minimax_api_key_here"
MINIMAX_API_URL="https://api.minimax.chat/v1/text/chatcompletion_v2"

# MiniMax 多密钥配置（推荐3-4个密钥）
MINIMAX_API_KEY_1=sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxx1
MINIMAX_API_KEY_2=sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxx2
MINIMAX_API_KEY_3=sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxx3
MINIMAX_API_KEY_4=sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxx4
# DeepSeek API配置
DEEPSEEK_API_KEY="your_deepseek_api_key_here"
DEEPSEEK_API_URL="https://api.deepseek.com/v1/chat/completions"

# ============================================================================
# 应用配置
# ============================================================================
APP_ENV="development"
LOG_LEVEL="debug"

# 配置文件路径
CONFIG_PATH="./configs"
CONFIG_CACHE_TTL="3600"

# ============================================================================
# 安全配置
# ============================================================================
ENCRYPTION_KEY="your_32_char_encryption_key_here"
CORS_ORIGIN="http://localhost:3000"

# ============================================================================
# 监控配置
# ============================================================================
ENABLE_MONITORING="true"
MONITORING_ENDPOINT="http://localhost:9090"
GRAFANA_PASSWORD="admin_password"

# ============================================================================
# 文件存储配置
# ============================================================================
UPLOAD_DIR="./public/uploads"
MAX_FILE_SIZE=10485760

# ============================================================================
# 邮件服务配置（开发环境使用MailHog）
# ============================================================================
SMTP_HOST="localhost"
SMTP_PORT=1025
SMTP_USER=""
SMTP_PASSWORD=""
SMTP_FROM="noreply@localhost"

# ============================================================================
# 功能开关
# ============================================================================
ENABLE_ANALYTICS="true"
ENABLE_RATE_LIMITING="false"
ENABLE_COMPRESSION="true"

# ============================================================================
# Docker相关配置
# ============================================================================
# 用于Docker Compose的域名配置
COMPOSE_PROJECT_NAME=octi