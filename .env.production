# OCTI智能评估系统 - 生产环境配置
# Production Environment Variables
# ⚠️  请在部署前修改所有密码和密钥！

# ============================================================================
# 应用基础配置
# ============================================================================
NODE_ENV=production
PORT=3000
DOMAIN=your-domain.com

# ============================================================================
# 数据库配置
# ============================================================================
POSTGRES_DB=octi_production
POSTGRES_USER=octi_user
POSTGRES_PASSWORD=octi_secure_password_2024
DATABASE_URL=**************************************************************/octi_production

# ============================================================================
# 缓存配置
# ============================================================================
REDIS_PASSWORD=redis_secure_password_2024
REDIS_URL=redis://:redis_secure_password_2024@redis:6379

# ============================================================================
# 身份认证配置
# ============================================================================
NEXTAUTH_URL=https://your-domain.com
NEXTAUTH_SECRET=your_nextauth_secret_32_chars_minimum_change_this

# JWT密钥
JWT_SECRET=your_jwt_secret_key_32_chars_minimum_change_this

# ============================================================================
# AI服务配置
# ============================================================================
# MiniMax API配置
MINIMAX_API_KEY=eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************************************************************************.l35-0aqr03wtDzpi4cyuOXC0vVlBVX1tISLrv66b1qQKg-XkbYxC8ZuEsKtDLnSG2PkbEWgEbPlkrOBPG8_2Fwv8pLohrUNNNCGCcpP6AhS3iVRlJWUk5QUqiynyYhh81o7x3ahhZkAITPQIieBhUhNKfkkPCbsI7T1cKcwqRnbS42LruYIi2bXMoLUFWSEA0fHFnw9CkP1Et81HNj-r2k2-UKOerft6O9qpKVewsYBvj61zuPtjhpUdAXjB0ZcLV7Oi0oNfPKyB2si1rV72Jz2bnOGuqs0Bo81E4grBPUwxwFEC_1vPU42e2VJ1sphqCbG-iUNNRDVIouSgU4DGrg
MINIMAX_API_URL=https://api.minimax.chat/v1/text/chatcompletion_v2


# MiniMax 多密钥配置（推荐3-4个密钥）
MINIMAX_API_KEY_1=***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
MINIMAX_API_KEY_2=***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
MINIMAX_API_KEY_3=***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
# DeepSeek API配置
DEEPSEEK_API_KEY=***********************************
DEEPSEEK_API_URL=https://api.deepseek.com/v1/chat/completions

# ===========================================
# 应用安全配置 (Application Security)
# ===========================================
# NextAuth.js配置
NEXTAUTH_SECRET="your_nextauth_secret_32_chars_min"
NEXTAUTH_URL="https://your-domain.com"

# JWT密钥
JWT_SECRET="your_jwt_secret_32_chars_minimum"

# 数据加密密钥
ENCRYPTION_KEY="your_encryption_key_32_chars_min"

# ===========================================
# 应用配置 (Application Configuration)
# ===========================================
NODE_ENV="production"
APP_ENV="production"
LOG_LEVEL="info"
NEXT_TELEMETRY_DISABLED=1

# 配置文件路径
CONFIG_PATH="/app/configs"
CONFIG_CACHE_TTL="7200"

# ===========================================
# 监控配置 (Monitoring Configuration)
# ===========================================
ENABLE_MONITORING="true"
MONITORING_ENDPOINT="http://prometheus:9090"

# Grafana管理员密码
GRAFANA_ADMIN_PASSWORD="your_secure_grafana_password"

# ===========================================
# 邮件服务配置 (Email Service Configuration)
# ===========================================
SMTP_HOST="smtp.your-provider.com"
SMTP_PORT="587"
SMTP_USER="<EMAIL>"
SMTP_PASSWORD="your_email_password"
SMTP_FROM="OCTI系统 <<EMAIL>>"

# ===========================================
# 文件存储配置 (File Storage Configuration)
# ===========================================
# 本地存储路径
UPLOAD_PATH="/app/uploads"
MAX_FILE_SIZE="10485760"  # 10MB

# 云存储配置 (可选)
# AWS_ACCESS_KEY_ID="your_aws_access_key"
# AWS_SECRET_ACCESS_KEY="your_aws_secret_key"
# AWS_REGION="ap-southeast-1"
# AWS_S3_BUCKET="octi-uploads"

# ===========================================
# 性能配置 (Performance Configuration)
# ===========================================
# API限流配置
RATE_LIMIT_WINDOW="900000"  # 15分钟
RATE_LIMIT_MAX="100"        # 最大请求数

# 缓存配置
CACHE_TTL="3600"           # 1小时
SESSION_TTL="86400"        # 24小时

# ===========================================
# 安全配置 (Security Configuration)
# ===========================================
# CORS配置
CORS_ORIGIN="https://your-domain.com"

# CSP配置
CSP_REPORT_URI="https://your-domain.com/api/csp-report"

# ===========================================
# 第三方服务配置 (Third-party Services)
# ===========================================
# 支付服务 (可选)
# STRIPE_PUBLIC_KEY="pk_live_..."
# STRIPE_SECRET_KEY="sk_live_..."
# STRIPE_WEBHOOK_SECRET="whsec_..."

# 短信服务 (可选)
# SMS_PROVIDER="aliyun"
# SMS_ACCESS_KEY="your_sms_access_key"
# SMS_SECRET_KEY="your_sms_secret_key"

# ===========================================
# 备份配置 (Backup Configuration)
# ===========================================
BACKUP_ENABLED="true"
BACKUP_SCHEDULE="0 2 * * *"  # 每天凌晨2点
BACKUP_RETENTION_DAYS="30"
BACKUP_S3_BUCKET="octi-backups"

# ===========================================
# 日志配置 (Logging Configuration)
# ===========================================
LOG_FORMAT="json"
LOG_MAX_SIZE="100m"
LOG_MAX_FILES="10"
LOG_COMPRESS="true"

# ===========================================
# 健康检查配置 (Health Check Configuration)
# ===========================================
HEALTH_CHECK_INTERVAL="30s"
HEALTH_CHECK_TIMEOUT="10s"
HEALTH_CHECK_RETRIES="3"

# ===========================================
# 部署配置 (Deployment Configuration)
# ===========================================
DEPLOY_ENV="production"
DEPLOY_VERSION="4.0.0"
DEPLOY_TIMESTAMP="$(date -u +%Y%m%d_%H%M%S)"

# ===========================================
# 注意事项 (Important Notes)
# ===========================================
# 1. 请确保所有密码和密钥都是强密码
# 2. 生产环境中请使用HTTPS
# 3. 定期轮换密钥和密码
# 4. 备份重要数据
# 5. 监控系统性能和安全
# 6. 遵循最小权限原则
# 7. 定期更新依赖和镜像