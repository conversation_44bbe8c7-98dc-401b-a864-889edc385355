# OCTI智能评估系统 - Git忽略规则

# ============================================================================
# Node.js 依赖
# ============================================================================
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# ============================================================================
# Next.js 构建输出
# ============================================================================
.next/
out/
build/
dist/

# ============================================================================
# 环境变量和配置
# ============================================================================
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# ============================================================================
# 数据库相关
# ============================================================================
# PostgreSQL
*.sql
*.db
*.sqlite
*.sqlite3

# Prisma
prisma/migrations/dev.db*
prisma/dev.db*

# ============================================================================
# 缓存和临时文件
# ============================================================================
# Next.js缓存
.next/cache/

# TypeScript缓存
*.tsbuildinfo
next-env.d.ts

# ESLint缓存
.eslintcache

# Prettier缓存
.prettiercache

# ============================================================================
# 测试相关
# ============================================================================
# Jest
coverage/
*.lcov

# Playwright
test-results/
playwright-report/
playwright/.cache/

# ============================================================================
# 日志文件
# ============================================================================
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# ============================================================================
# 操作系统文件
# ============================================================================
# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon?
._*
.Spotlight-V100
.Trashes

# Windows
Thumbs.db
Thumbs.db:encryptable
ehthumbs.db
ehthumbs_vista.db
*.stackdump
[Dd]esktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# ============================================================================
# IDE和编辑器
# ============================================================================
# VSCode
.vscode/
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json

# WebStorm/IntelliJ
.idea/
*.swp
*.swo
*~

# Sublime Text
*.sublime-workspace
*.sublime-project

# Vim
*.swp
*.swo
*.tmp

# ============================================================================
# 部署和运维
# ============================================================================
# Docker
.dockerignore
docker-compose.override.yml
docker-compose.*.override.yml

# Docker数据卷挂载点
postgres-data/
redis-data/
app-uploads/
nginx-logs/
ssl-certs/
prometheus-data/
grafana-data/

# 部署日志和备份
logs/
backups/
deployment-verification-*.txt

# Kubernetes
*.kubeconfig

# Terraform
*.tfstate
*.tfstate.*
.terraform/

# ============================================================================
# 开发工具
# ============================================================================
# Bundle分析
.bundle-analyzer/

# Storybook
storybook-static/

# ============================================================================
# 项目特定文件
# ============================================================================
# 临时文件
temp/
tmp/
*.tmp

# 备份文件
*.bak
*.backup
*.old

# 上传文件
uploads/
public/uploads/

# 生成的文档
docs/generated/

# 配置备份
configs/*.backup
configs/*.bak

# 测试数据
test-data/
mock-data/

# 性能分析
*.cpuprofile
*.heapprofile

# ============================================================================
# AI和机器学习相关
# ============================================================================
# 模型文件
*.model
*.pkl
*.h5
*.pb

# 训练数据
training-data/
datasets/

# ============================================================================
# 安全相关
# ============================================================================
# 密钥文件
*.key
*.pem
*.p12
*.pfx
*.crt
*.cer
*.der

# 配置中的敏感信息
secrets/
.secrets/

# ============================================================================
# 监控和分析
# ============================================================================
# 性能监控
newrelic_agent.log
.nyc_output/

# 错误追踪
.sentry-cli/

# ============================================================================
# 其他
# ============================================================================
# 压缩文件
*.zip
*.tar.gz
*.rar
*.7z

# 图片缓存
.image-cache/

# 字体缓存
.font-cache/

# 本地开发服务器
.local-server/

# 实验性功能
experimental/
sandbox/
