# OCTI 智能评估系统 - 项目开发规则

## 项目概述

OCTI（Organization Capability Type
Indicator）智能评估系统v4.0是一个基于配置驱动和智能体模块化的组织能力评估平台。项目采用 Next.js +
TypeScript 技术栈，遵循现代化的工程实践和设计模式。

## 核心技术栈

- **前端框架**: Next.js 14+ with TypeScript (App Router)
- **UI 组件库**: Tailwind CSS + Shadcn/ui
- **状态管理**: Zustand
- **表单处理**: React Hook Form + Zod
- **HTTP 客户端**: Axios
- **数据库**: PostgreSQL + Prisma ORM
- **缓存**: Redis
- **AI集成**: MiniMax API + DeepSeek API
- **测试**: Jest + React Testing Library
- **代码规范**: ESLint + Prettier

## 文件结构规范（Next.js App Router）

```
src/
├── app/                 # Next.js App Router页面
│   ├── assessment/      # 评估相关页面
│   ├── reports/         # 报告页面
│   ├── api/            # API路由
│   └── globals.css     # 全局样式
├── components/          # 可复用组件
│   ├── ui/             # 基础UI组件（shadcn/ui）
│   ├── forms/          # 表单组件
│   ├── charts/         # 图表组件
│   └── layout/         # 布局组件
├── services/           # 业务逻辑服务
│   ├── api/           # API调用服务
│   ├── config/        # 配置管理
│   ├── agents/        # 智能体服务
│   └── validation/    # 数据验证
├── lib/               # 工具库和配置
├── hooks/             # 自定义Hooks
├── types/             # TypeScript类型定义
├── utils/             # 工具函数
└── constants/         # 常量定义
```

## 智能体配置规范

### 1. 配置文件结构

```typescript
interface OCTIAgentConfig {
  metadata: {
    version: string;
    name: string;
    type: 'question_designer' | 'organization_tutor';
    description: string;
    author: string;
    createdAt: string;
    updatedAt: string;
  };

  llm: {
    provider: 'minimax' | 'deepseek';
    model: string;
    temperature: number;
    maxTokens: number;
    systemPrompt: string;
  };

  octiFramework: {
    dimensions: ['S/F', 'I/T', 'M/V', 'A/D'];
    assessmentType: 'standard' | 'professional';
    questionCount: 60;
  };

  outputFormat: {
    type: 'json';
    schema: object;
  };
}
```

### 2. 配置验证

```typescript
import { z } from 'zod';

const OCTIConfigSchema = z.object({
  metadata: z.object({
    version: z.string(),
    name: z.string().min(1),
    type: z.enum(['question_designer', 'organization_tutor']),
  }),
  llm: z.object({
    provider: z.enum(['minimax', 'deepseek']),
    model: z.string(),
    temperature: z.number().min(0).max(2),
    maxTokens: z.number().positive(),
  }),
  octiFramework: z.object({
    dimensions: z.array(z.string()).length(4),
    assessmentType: z.enum(['standard', 'professional']),
    questionCount: z.literal(60),
  }),
});
```

## API设计规范

### 1. RESTful API约定

- **评估管理**: `/api/v1/assessments`
- **问卷生成**: `/api/v1/agents/question-designer`
- **结果分析**: `/api/v1/agents/organization-tutor`
- **配置管理**: `/api/v1/config/{agentType}`
- **报告生成**: `/api/v1/reports`

### 2. 响应格式标准

```typescript
interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
    details?: any;
  };
  meta?: {
    timestamp: string;
    requestId: string;
  };
}
```

## 数据库规范

### 1. Prisma Schema约定

```prisma
model Assessment {
  id           String   @id @default(cuid())
  userId       String
  organizationId String?
  type         AssessmentType
  status       AssessmentStatus
  responses    Json?
  report       Json?
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt
  deletedAt    DateTime?

  user         User     @relation(fields: [userId], references: [id])
  organization Organization? @relation(fields: [organizationId], references: [id])

  @@map("assessments")
}
```

## 安全规范

### 1. LLM API安全

```typescript
// ✅ 后端API路由中调用LLM
export async function POST(request: Request) {
  const apiKey = process.env.MINIMAX_API_KEY; // 服务端环境变量

  if (!apiKey) {
    throw new Error('LLM API key not configured');
  }

  // 调用LLM API逻辑
}

// ❌ 避免：前端直接调用LLM API
// const response = await fetch('https://api.minimax.chat/v1/text/chatcompletion', {
//   headers: { 'Authorization': `Bearer ${API_KEY}` } // 暴露API密钥
// });
```

### 2. 数据验证

```typescript
// 使用Zod验证所有输入
const CreateAssessmentSchema = z.object({
  organizationName: z.string().min(1).max(100),
  assessmentType: z.enum(['standard', 'professional']),
  responses: z
    .array(
      z.object({
        questionId: z.string(),
        answer: z.number().min(1).max(5),
      })
    )
    .min(60)
    .max(60), // OCTI固定60题
});
```

## 性能优化规范

### 1. Next.js优化

```typescript
// ✅ 使用Next.js Image组件
import Image from 'next/image';

// ✅ 动态导入大型组件
const ReportChart = dynamic(() => import('./ReportChart'), {
  loading: () => <ChartSkeleton />,
  ssr: false, // 图表组件客户端渲染
});

// ✅ 使用Next.js缓存
export const revalidate = 3600; // 1小时缓存
```

### 2. 智能体调用优化

```typescript
// ✅ 实现请求缓存
const getCachedQuestions = cache(async (configHash: string) => {
  const cached = await redis.get(`questions:${configHash}`);
  if (cached) return JSON.parse(cached);

  const questions = await questionAgent.generate();
  await redis.setex(`questions:${configHash}`, 3600, JSON.stringify(questions));
  return questions;
});
```

## 环境配置

### 1. 环境变量管理

```bash
# 数据库
DATABASE_URL="postgresql://..."
REDIS_URL="redis://..."

# LLM API
MINIMAX_API_KEY="your_minimax_key"
DEEPSEEK_API_KEY="your_deepseek_key"

# Next.js
NEXT_PUBLIC_APP_URL="https://octi.example.com"
NEXTAUTH_SECRET="your_auth_secret"
```

### 2. 配置验证

```typescript
const envSchema = z.object({
  DATABASE_URL: z.string().url(),
  MINIMAX_API_KEY: z.string().min(1),
  NEXT_PUBLIC_APP_URL: z.string().url(),
});

export const env = envSchema.parse(process.env);
```

## 关键OCTI约定

1. **60题固定**: 所有评估必须包含60道题目
2. **四维八极**: 严格按照S/F、I/T、M/V、A/D框架
3. **版本差异**: 标准版(¥99)和专业版(¥399)功能明确区分
4. **配置热更新**: 支持无重启配置更新
5. **数据安全**: 组织敏感信息加密存储
6. **API限流**: 防止恶意调用LLM接口
