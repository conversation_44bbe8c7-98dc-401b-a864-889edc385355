# OCTI智能评估系统 - Makefile
# 简化Docker部署和开发操作

.PHONY: help dev dev-init dev-stop dev-restart dev-logs dev-shell dev-clean
.PHONY: prod prod-stop prod-restart prod-logs prod-status prod-clean
.PHONY: build test lint format db-migrate db-reset
.PHONY: backup restore health

# 默认目标
.DEFAULT_GOAL := help

# 颜色定义
BLUE := \033[36m
GREEN := \033[32m
YELLOW := \033[33m
RED := \033[31m
NC := \033[0m

# 帮助信息
help: ## 显示帮助信息
	@echo "$(BLUE)OCTI智能评估系统 - Docker部署命令$(NC)"
	@echo ""
	@echo "$(GREEN)开发环境命令:$(NC)"
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## .*dev/ {printf "  $(YELLOW)%-15s$(NC) %s\n", $$1, $$2}' $(MAKEFILE_LIST)
	@echo ""
	@echo "$(GREEN)生产环境命令:$(NC)"
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## .*prod/ {printf "  $(YELLOW)%-15s$(NC) %s\n", $$1, $$2}' $(MAKEFILE_LIST)
	@echo ""
	@echo "$(GREEN)通用命令:$(NC)"
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / && !/dev|prod/ {printf "  $(YELLOW)%-15s$(NC) %s\n", $$1, $$2}' $(MAKEFILE_LIST)

# ============================================================================
# 开发环境命令
# ============================================================================

dev: ## 启动开发环境 (dev)
	@echo "$(BLUE)启动开发环境...$(NC)"
	@./scripts/docker-dev.sh start

dev-init: ## 首次启动开发环境并初始化 (dev)
	@echo "$(BLUE)首次启动开发环境...$(NC)"
	@./scripts/docker-dev.sh start --init

dev-stop: ## 停止开发环境 (dev)
	@echo "$(BLUE)停止开发环境...$(NC)"
	@./scripts/docker-dev.sh stop

dev-restart: ## 重启开发环境 (dev)
	@echo "$(BLUE)重启开发环境...$(NC)"
	@./scripts/docker-dev.sh restart

dev-logs: ## 查看开发环境日志 (dev)
	@./scripts/docker-dev.sh logs

dev-shell: ## 进入开发环境应用容器 (dev)
	@./scripts/docker-dev.sh shell

dev-clean: ## 清理开发环境数据 (dev)
	@echo "$(RED)清理开发环境数据...$(NC)"
	@./scripts/docker-dev.sh clean

# ============================================================================
# 生产环境命令
# ============================================================================

prod: ## 部署生产环境 (prod)
	@echo "$(BLUE)部署生产环境...$(NC)"
	@./scripts/docker-deploy.sh deploy

prod-stop: ## 停止生产环境 (prod)
	@echo "$(BLUE)停止生产环境...$(NC)"
	@./scripts/docker-deploy.sh stop

prod-restart: ## 重启生产环境 (prod)
	@echo "$(BLUE)重启生产环境...$(NC)"
	@./scripts/docker-deploy.sh restart

prod-logs: ## 查看生产环境日志 (prod)
	@./scripts/docker-deploy.sh logs

prod-status: ## 查看生产环境状态 (prod)
	@./scripts/docker-deploy.sh status

prod-clean: ## 清理生产环境数据 (prod)
	@echo "$(RED)清理生产环境数据...$(NC)"
	@./scripts/docker-deploy.sh clean

# ============================================================================
# 构建和测试命令
# ============================================================================

build: ## 构建Docker镜像
	@echo "$(BLUE)构建Docker镜像...$(NC)"
	@docker-compose build --no-cache

test: ## 运行测试
	@echo "$(BLUE)运行测试...$(NC)"
	@docker-compose -f docker-compose.dev.yml exec app npm test

lint: ## 代码检查
	@echo "$(BLUE)运行代码检查...$(NC)"
	@docker-compose -f docker-compose.dev.yml exec app npm run lint

format: ## 代码格式化
	@echo "$(BLUE)格式化代码...$(NC)"
	@docker-compose -f docker-compose.dev.yml exec app npm run format

# ============================================================================
# 数据库命令
# ============================================================================

db-migrate: ## 运行数据库迁移
	@echo "$(BLUE)运行数据库迁移...$(NC)"
	@docker-compose -f docker-compose.dev.yml exec app npx prisma migrate dev

db-reset: ## 重置数据库
	@echo "$(RED)重置数据库...$(NC)"
	@./scripts/docker-dev.sh db-reset

db-studio: ## 打开Prisma Studio
	@echo "$(BLUE)启动Prisma Studio...$(NC)"
	@docker-compose -f docker-compose.dev.yml exec app npx prisma studio

# ============================================================================
# 备份和恢复命令
# ============================================================================

backup: ## 备份数据
	@echo "$(BLUE)备份数据...$(NC)"
	@mkdir -p backups
	@docker-compose exec postgres pg_dump -U octi_user octi_db > backups/backup-$(shell date +%Y%m%d-%H%M%S).sql
	@echo "$(GREEN)数据库备份完成$(NC)"

restore: ## 恢复数据 (需要指定备份文件: make restore FILE=backup.sql)
	@echo "$(BLUE)恢复数据...$(NC)"
	@if [ -z "$(FILE)" ]; then echo "$(RED)请指定备份文件: make restore FILE=backup.sql$(NC)"; exit 1; fi
	@docker-compose exec -T postgres psql -U octi_user octi_db < $(FILE)
	@echo "$(GREEN)数据恢复完成$(NC)"

# ============================================================================
# 监控和健康检查命令
# ============================================================================

health: ## 检查系统健康状态
	@echo "$(BLUE)检查系统健康状态...$(NC)"
	@curl -s http://localhost:3000/api/health | jq '.' || echo "$(RED)健康检查失败$(NC)"

status: ## 查看所有容器状态
	@echo "$(BLUE)容器状态:$(NC)"
	@docker-compose ps

logs: ## 查看所有服务日志
	@docker-compose logs -f

# ============================================================================
# 清理命令
# ============================================================================

clean-images: ## 清理未使用的Docker镜像
	@echo "$(BLUE)清理Docker镜像...$(NC)"
	@docker image prune -f

clean-volumes: ## 清理未使用的Docker数据卷
	@echo "$(BLUE)清理Docker数据卷...$(NC)"
	@docker volume prune -f

clean-all: ## 清理所有未使用的Docker资源
	@echo "$(BLUE)清理所有Docker资源...$(NC)"
	@docker system prune -af

# ============================================================================
# 快捷命令
# ============================================================================

up: dev ## 快捷启动开发环境
down: dev-stop ## 快捷停止开发环境
restart: dev-restart ## 快捷重启开发环境
shell: dev-shell ## 快捷进入容器

# ============================================================================
# 环境检查
# ============================================================================

check-env: ## 检查环境配置
	@echo "$(BLUE)检查环境配置...$(NC)"
	@if [ ! -f .env ]; then echo "$(RED).env文件不存在$(NC)"; exit 1; fi
	@echo "$(GREEN)环境配置检查通过$(NC)"

check-deps: ## 检查依赖
	@echo "$(BLUE)检查系统依赖...$(NC)"
	@command -v docker >/dev/null 2>&1 || { echo "$(RED)Docker未安装$(NC)"; exit 1; }
	@command -v docker-compose >/dev/null 2>&1 || { echo "$(RED)Docker Compose未安装$(NC)"; exit 1; }
	@echo "$(GREEN)依赖检查通过$(NC)"

# ============================================================================
# 初始化命令
# ============================================================================

init: check-deps check-env ## 初始化项目环境
	@echo "$(BLUE)初始化项目环境...$(NC)"
	@if [ ! -f .env ]; then cp .env.example .env; echo "$(YELLOW)已创建.env文件，请编辑配置$(NC)"; fi
	@echo "$(GREEN)项目初始化完成$(NC)"

# ============================================================================
# 文档命令
# ============================================================================

docs: ## 查看部署文档
	@echo "$(BLUE)Docker部署文档位置:$(NC)"
	@echo "  docs/12_docker_deployment.md"
	@echo ""
	@echo "$(BLUE)快速开始:$(NC)"
	@echo "  1. make init          # 初始化环境"
	@echo "  2. make dev-init      # 首次启动开发环境"
	@echo "  3. make health        # 检查系统状态"
