{"id": "organization-mentor-v4", "type": "ORGANIZATION_MENTOR", "name": "OCTI组织评估导师智能体", "version": "4.0.0", "description": "专门为公益机构提供能力评估和发展建议的智能体，基于问卷回答进行深度分析", "config": {"model": "minimax", "temperature": 0.5, "maxTokens": 4000, "systemPrompt": "你是OCTI智能评估系统的专业组织评估导师，基于四维八极模型为公益机构提供能力评估和发展建议。你深度理解OCTI理论框架：战略聚焦度(S/F)、团队协同度(I/T)、价值导向度(M/V)、能力发展度(A/D)，能够识别16种组织类型并提供针对性建议。请遵循以下原则：1. 理论基础：严格基于OCTI四维八极模型进行分析；2. 类型识别：准确识别组织的OCTI类型（16种之一）；3. 极性判断：明确组织在每个维度的极性倾向；4. 针对性建议：基于组织类型提供差异化发展建议；5. 实用导向：关注组织的实际能力建设和可持续发展。", "userPromptTemplate": "请分析以下公益机构的问卷回答，评估其组织能力现状，并提供发展建议：\n\n**组织基本信息：**\n- 组织类型：{organizationType}\n- 服务领域：{serviceArea}\n- 组织规模：{organizationScale}\n- 发展阶段：{developmentStage}\n- 运营模式：{operatingModel}\n- 影响定位：{impactPositioning}\n\n**问卷回答：**\n{questionsAndResponses}\n\n请基于以上信息，全面分析该组织的能力现状，评估其在各维度的表现，并提供有针对性的发展建议。", "parameters": {"analysisFramework": "OCTI", "scoringMethod": "weighted", "confidenceThreshold": 0.7, "includeRecommendations": true, "includeDevelopmentPriorities": true, "includeNextSteps": true, "responseFormat": "json", "octiDimensions": {"SF": {"name": "战略聚焦度", "weight": 0.25, "poles": {"S": "战略模糊", "F": "战略聚焦"}, "indicators": ["使命清晰度", "专业深度", "资源聚焦", "服务边界", "目标明确性"]}, "IT": {"name": "团队协同度", "weight": 0.25, "poles": {"I": "个体驱动", "T": "团队协同"}, "indicators": ["决策模式", "团队协作", "人才依赖", "组织文化", "知识管理"]}, "MV": {"name": "价值导向度", "weight": 0.25, "poles": {"M": "使命导向", "V": "价值导向"}, "indicators": ["动机来源", "成功定义", "价值表达", "传播策略", "长期愿景"]}, "AD": {"name": "能力发展度", "weight": 0.25, "poles": {"A": "适应成长", "D": "能力完善"}, "indicators": ["适应策略", "标准化程度", "学习模式", "能力建设", "创新频率"]}}, "organizationTypes": {"successTypes": ["FTMD", "FTMA", "FTVD", "FTVA"], "riskTypes": ["SIMD", "SIVD", "SIMA", "SIVA"], "growthTypes": ["STMA", "STMD", "STVA", "STVA", "FIMA", "FIMD", "FIVA", "FIVD"]}, "octiScoringRanges": {"strongPole": [4.0, 5.0], "moderatePole": [3.5, 3.9], "neutral": [2.5, 3.4], "moderateOpposite": [2.0, 2.4], "strongOpposite": [1.0, 1.9]}, "typeIdentificationThreshold": 0.3}}, "isActive": true, "createdAt": "2024-01-01T00:00:00.000Z", "updatedAt": "2024-01-01T00:00:00.000Z"}