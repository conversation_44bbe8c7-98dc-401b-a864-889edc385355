# OCTI智能评估系统 - 开发环境配置
# 开发环境，包含热重载和调试工具

services:
  # Next.js应用服务 (开发模式)
  app:
    build:
      context: .
      dockerfile: Dockerfile
      target: development
    container_name: octi-app-dev
    restart: unless-stopped
    environment:
      - NODE_ENV=development
      - DATABASE_URL=postgresql://${POSTGRES_USER:-octi_user}:${POSTGRES_PASSWORD:-octi_password}@postgres:5432/${POSTGRES_DB:-octi_db}
      - REDIS_URL=redis://:${REDIS_PASSWORD:-redis_password}@redis:6379
      - NEXTAUTH_URL=http://localhost:3000
      - NEXTAUTH_SECRET=${NEXTAUTH_SECRET:-dev_secret_key_change_in_production}
      # MiniMax 多API密钥支持
      - MINIMAX_API_KEY=${MINIMAX_API_KEY}
      - MINIMAX_API_KEY_1=${MINIMAX_API_KEY_1}
      - MINIMAX_API_KEY_2=${MINIMAX_API_KEY_2}
      - MINIMAX_API_KEY_3=${MINIMAX_API_KEY_3}
      - MINIMAX_API_KEY_4=${MINIMAX_API_KEY_4}
      - DEEPSEEK_API_KEY=${DEEPSEEK_API_KEY}
    ports:
      - '3000:3000'
    volumes:
      # 挂载源代码以支持热重载
      - .:/app
      - /app/node_modules
      - /app/.next
      - app-uploads-dev:/app/public/uploads
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - octi-dev-network

  # PostgreSQL数据库服务 (开发环境)
  postgres:
    image: postgres:15-alpine
    container_name: octi-postgres-dev
    restart: unless-stopped
    environment:
      - POSTGRES_DB=${POSTGRES_DB:-octi_db}
      - POSTGRES_USER=${POSTGRES_USER:-octi_user}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-octi_password}
    ports:
      - '5432:5432'
    volumes:
      - postgres-data-dev:/var/lib/postgresql/data
      - ./docker/postgres/init:/docker-entrypoint-initdb.d
    networks:
      - octi-dev-network
    healthcheck:
      test: ['CMD-SHELL', 'pg_isready -U ${POSTGRES_USER:-octi_user} -d ${POSTGRES_DB:-octi_db}']
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis缓存服务 (开发环境)
  redis:
    image: redis:7-alpine
    container_name: octi-redis-dev
    restart: unless-stopped
    environment:
      - REDIS_PASSWORD=${REDIS_PASSWORD:-redis_password}
    ports:
      - '6379:6379'
    volumes:
      - redis-data-dev:/data
    networks:
      - octi-dev-network
    healthcheck:
      test: ['CMD', 'redis-cli', 'ping']
      interval: 10s
      timeout: 3s
      retries: 5
    command: redis-server --requirepass ${REDIS_PASSWORD:-redis_password}

  # ============================================================================
  # 数据库管理工具 (可选)
  adminer:
    image: adminer:latest
    container_name: octi-adminer-dev
    restart: unless-stopped
    ports:
      - '8080:8080'
    environment:
      - ADMINER_DEFAULT_SERVER=postgres
    depends_on:
      - postgres
    networks:
      - octi-dev-network

# 网络配置
networks:
  octi-dev-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# 数据卷配置
volumes:
  postgres-data-dev:
    driver: local
  redis-data-dev:
    driver: local
  app-uploads-dev:
    driver: local
