services:
  app:
    build:
      context: .
      target: production
    image: octi-production:latest
    container_name: octi-app
    ports:
      - "3000:3000"
    volumes:
      - ./configs:/app/configs:ro
      - app_logs:/app/logs
    environment:
      - NODE_ENV=production
      - DATABASE_URL=**********************************************/octi
      - REDIS_URL=redis://redis:6379
      - NEXTAUTH_SECRET=octi-super-secret-key-for-production-2024
      - NEXTAUTH_URL=http://localhost:3000
      - PRISMA_CLI_BINARY_TARGETS=debian-openssl-3.0.x
      - NEXT_TELEMETRY_DISABLED=1
      - MINIMAX_API_KEY=${MINIMAX_API_KEY:-}
      - MINIMAX_API_KEY_1=${MINIMAX_API_KEY_1:-}
      - MINIMAX_API_KEY_2=${MINIMAX_API_KEY_2:-}
      - MINIMAX_API_KEY_3=${MINIMAX_API_KEY_3:-}
      - DEEPSEEK_API_KEY=${DEEPSEEK_API_KEY:-}
      - LOG_LEVEL=info
      - CONFIG_PATH=/app/configs
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_started
    networks:
      - octi-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
      interval: 60s
      timeout: 30s
      retries: 5
      start_period: 120s

  postgres:
    image: mirror.ccs.tencentyun.com/library/postgres:15-alpine
    container_name: octi-postgres
    environment:
      - POSTGRES_DB=octi
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=octi123456
      - POSTGRES_INITDB_ARGS=--encoding=UTF-8 --lc-collate=C --lc-ctype=C
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-db.sql:/docker-entrypoint-initdb.d/init-db.sql:ro
      - ./configs:/app/configs:ro
    networks:
      - octi-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d octi"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s

  redis:
    image: mirror.ccs.tencentyun.com/library/redis:7-alpine
    container_name: octi-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - octi-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 3
      start_period: 10s
    command: redis-server --appendonly yes --maxmemory 256mb --maxmemory-policy allkeys-lru

  # ============================================================================
  # 数据库管理工具 (可选)
  adminer:
    image: mirror.ccs.tencentyun.com/library/adminer:latest
    container_name: octi-adminer
    restart: unless-stopped
    ports:
      - '8080:8080'
    environment:
      - ADMINER_DEFAULT_SERVER=postgres
    depends_on:
      - postgres
    networks:
      - octi-network

networks:
  octi-network:
    driver: bridge
    name: octi-network

volumes:
  postgres_data:
    name: octi_postgres_data
  redis_data:
    name: octi_redis_data
  app_logs:
    name: octi_app_logs
