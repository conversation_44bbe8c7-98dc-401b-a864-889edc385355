# OCTI智能评估系统 - Grafana数据源配置
# 自动配置Prometheus数据源

apiVersion: 1

datasources:
  # Prometheus主数据源
  - name: Prometheus
    type: prometheus
    access: proxy
    url: http://prometheus:9090
    isDefault: true
    editable: true
    jsonData:
      httpMethod: POST
      manageAlerts: true
      prometheusType: Prometheus
      prometheusVersion: 2.40.0
      cacheLevel: 'High'
      disableRecordingRules: false
      incrementalQueryOverlapWindow: 10m
      queryTimeout: 60s
      timeInterval: 30s
    secureJsonData: {}
    version: 1

  # 应用指标数据源
  - name: OCTI-Metrics
    type: prometheus
    access: proxy
    url: http://prometheus:9090
    isDefault: false
    editable: true
    jsonData:
      httpMethod: POST
      exemplarTraceIdDestinations:
        - name: trace_id
          datasourceUid: jaeger
      prometheusType: Prometheus
      prometheusVersion: 2.40.0
      timeInterval: 15s
    secureJsonData: {}
    version: 1

  # 系统指标数据源
  - name: System-Metrics
    type: prometheus
    access: proxy
    url: http://prometheus:9090
    isDefault: false
    editable: true
    jsonData:
      httpMethod: GET
      prometheusType: Prometheus
      prometheusVersion: 2.40.0
      timeInterval: 30s
    secureJsonData: {}
    version: 1
