# OCTI智能评估系统 - PostgreSQL生产环境配置
# 基于PostgreSQL 15的性能优化配置

# ============================================================================
# 连接和认证配置
# ============================================================================
listen_addresses = '*'
port = 5432
max_connections = 300
superuser_reserved_connections = 3

# ============================================================================
# 内存配置
# ============================================================================
shared_buffers = 512MB                    # 共享缓冲区
effective_cache_size = 2GB                # 有效缓存大小
work_mem = 8MB                            # 工作内存
maintenance_work_mem = 128MB              # 维护工作内存
dynamic_shared_memory_type = posix

# ============================================================================
# WAL配置
# ============================================================================
wal_buffers = 16MB                        # WAL缓冲区
checkpoint_completion_target = 0.9        # 检查点完成目标
checkpoint_timeout = 15min                # 检查点超时
max_wal_size = 2GB                        # 最大WAL大小
min_wal_size = 1GB                        # 最小WAL大小

# ============================================================================
# 查询规划器配置
# ============================================================================
default_statistics_target = 100           # 默认统计目标
random_page_cost = 1.1                    # 随机页面成本
effective_io_concurrency = 200            # 有效IO并发

# ============================================================================
# 日志配置
# ============================================================================
logging_collector = on                    # 启用日志收集器
log_destination = 'stderr'                # 日志目标
log_directory = '/var/log/postgresql'     # 日志目录
log_filename = 'postgresql-%Y-%m-%d_%H%M%S.log'  # 日志文件名
log_rotation_age = 1d                     # 日志轮转时间
log_rotation_size = 100MB                 # 日志轮转大小
log_min_duration_statement = 1000         # 记录慢查询（1秒）
log_line_prefix = '%t [%p]: [%l-1] user=%u,db=%d,app=%a,client=%h '
log_checkpoints = on                      # 记录检查点
log_connections = on                      # 记录连接
log_disconnections = on                   # 记录断开连接
log_lock_waits = on                       # 记录锁等待
log_temp_files = 0                        # 记录临时文件

# ============================================================================
# 性能监控配置
# ============================================================================
shared_preload_libraries = 'pg_stat_statements'  # 预加载统计扩展
track_activities = on                     # 跟踪活动
track_counts = on                         # 跟踪计数
track_io_timing = on                      # 跟踪IO时间
track_functions = all                     # 跟踪函数

# ============================================================================
# 自动清理配置
# ============================================================================
autovacuum = on                           # 启用自动清理
autovacuum_max_workers = 3                # 自动清理最大工作进程
autovacuum_naptime = 1min                 # 自动清理休眠时间
autovacuum_vacuum_threshold = 50          # 自动清理阈值
autovacuum_analyze_threshold = 50         # 自动分析阈值
autovacuum_vacuum_scale_factor = 0.2      # 自动清理比例因子
autovacuum_analyze_scale_factor = 0.1     # 自动分析比例因子

# ============================================================================
# 安全配置
# ============================================================================
ssl = off                                 # SSL（在容器环境中由代理处理）
password_encryption = scram-sha-256       # 密码加密方式

# ============================================================================
# 其他配置
# ============================================================================
timezone = 'UTC'                          # 时区
lc_messages = 'en_US.UTF-8'              # 消息语言
lc_monetary = 'en_US.UTF-8'              # 货币格式
lc_numeric = 'en_US.UTF-8'               # 数字格式
lc_time = 'en_US.UTF-8'                  # 时间格式
default_text_search_config = 'pg_catalog.english'  # 默认文本搜索配置

# ============================================================================
# 扩展配置
# ============================================================================
# pg_stat_statements配置
pg_stat_statements.max = 10000            # 最大语句数
pg_stat_statements.track = all            # 跟踪所有语句
pg_stat_statements.track_utility = off    # 不跟踪工具语句
pg_stat_statements.save = on              # 保存统计信息
