# OCTI智能评估系统 - Docker部署指南

**版本**: v4.0  
**更新日期**: 2025年8月1日  
**部署类型**: Docker容器化部署

---

## 📋 部署概览

OCTI智能评估系统提供完整的Docker容器化部署方案，支持开发环境和生产环境的快速部署。

### 部署架构

```mermaid
graph TB
    subgraph "Docker容器"
        A[Nginx反向代理] --> B[Next.js应用]
        B --> C[PostgreSQL数据库]
        B --> D[Redis缓存]
        E[Prometheus监控] --> B
        F[Grafana可视化] --> E
    end

    subgraph "外部服务"
        G[MiniMax API]
        H[DeepSeek API]
    end

    B --> G
    B --> H
```

---

## 🚀 快速开始

### 环境要求

- **Docker**: >= 20.10.0
- **Docker Compose**: >= 2.0.0
- **系统内存**: >= 4GB
- **磁盘空间**: >= 10GB

### 一键部署（生产环境）

```bash
# 1. 克隆项目
git clone <repository-url>
cd octi_test

# 2. 配置环境变量
cp .env.example .env
# 编辑 .env 文件，填入正确的配置

# 3. 一键部署
chmod +x scripts/docker-deploy.sh
./scripts/docker-deploy.sh deploy
```

### 开发环境部署

```bash
# 启动开发环境（首次运行）
chmod +x scripts/docker-dev.sh
./scripts/docker-dev.sh start --init

# 日常开发启动
./scripts/docker-dev.sh start
```

---

## 🔧 配置说明

### 环境变量配置

复制 `.env.example` 为 `.env` 并配置以下关键参数：

```bash
# 数据库配置
POSTGRES_DB=octi_db
POSTGRES_USER=octi_user
POSTGRES_PASSWORD=your_strong_password

# Redis配置
REDIS_PASSWORD=your_redis_password

# AI服务配置
MINIMAX_API_KEY=your_minimax_api_key
DEEPSEEK_API_KEY=your_deepseek_api_key

# 应用配置
NEXTAUTH_SECRET=your_32_char_secret_key
DOMAIN=your-domain.com
```

### Docker Compose配置

项目提供两套Docker Compose配置：

1. **生产环境**: `docker-compose.yml`
   - 包含完整的监控和安全配置
   - 使用Nginx反向代理
   - 包含SSL证书配置

2. **开发环境**: `docker-compose.dev.yml`
   - 支持热重载开发
   - 包含开发工具（Adminer、Redis Commander、MailHog）
   - 简化的配置便于调试

---

## 📦 容器服务说明

### 应用容器 (app)

- **镜像**: 基于Node.js 18 Alpine
- **端口**: 3000
- **功能**: Next.js应用主服务
- **健康检查**: `/api/health`

### 数据库容器 (postgres)

- **镜像**: PostgreSQL 15 Alpine
- **端口**: 5432
- **数据卷**: `postgres-data`
- **扩展**: uuid-ossp, pg_stat_statements, pg_trgm

### 缓存容器 (redis)

- **镜像**: Redis 7 Alpine
- **端口**: 6379
- **数据卷**: `redis-data`
- **配置**: 自定义redis.conf

### 反向代理 (nginx)

- **镜像**: Nginx Alpine
- **端口**: 80, 443
- **功能**: 负载均衡、SSL终止、静态文件服务

### 监控服务

- **Prometheus**: 指标收集 (端口: 9090)
- **Grafana**: 可视化监控 (端口: 3001)

---

## 🛠️ 部署命令

### 生产环境命令

```bash
# 部署系统
./scripts/docker-deploy.sh deploy

# 停止服务
./scripts/docker-deploy.sh stop

# 重启服务
./scripts/docker-deploy.sh restart

# 查看日志
./scripts/docker-deploy.sh logs

# 查看状态
./scripts/docker-deploy.sh status

# 清理系统
./scripts/docker-deploy.sh clean
```

### 开发环境命令

```bash
# 启动开发环境
./scripts/docker-dev.sh start

# 首次初始化
./scripts/docker-dev.sh start --init

# 停止开发环境
./scripts/docker-dev.sh stop

# 查看日志
./scripts/docker-dev.sh logs

# 进入应用容器
./scripts/docker-dev.sh shell

# 重置数据库
./scripts/docker-dev.sh db-reset

# 清理开发数据
./scripts/docker-dev.sh clean
```

---

## 🔍 监控和维护

### 健康检查

系统提供多层健康检查：

1. **容器级健康检查**: Docker内置健康检查
2. **应用级健康检查**: `/api/health` 端点
3. **服务级监控**: Prometheus + Grafana

### 访问地址

**生产环境**:

- 应用: `https://your-domain.com`
- 监控: `https://your-domain.com:3001` (Grafana)

**开发环境**:

- 应用: `http://localhost:3000`
- 数据库管理: `http://localhost:8080` (Adminer)
- Redis管理: `http://localhost:8081` (Redis Commander)
- 邮件测试: `http://localhost:8025` (MailHog)

### 日志管理

```bash
# 查看所有服务日志
docker-compose logs -f

# 查看特定服务日志
docker-compose logs -f app
docker-compose logs -f postgres
docker-compose logs -f redis

# 查看最近100行日志
docker-compose logs --tail=100 app
```

---

## 🔒 安全配置

### SSL证书

生产环境需要配置SSL证书：

```bash
# 将证书文件放置到ssl目录
mkdir -p ssl
cp your-cert.pem ssl/cert.pem
cp your-key.pem ssl/key.pem
```

### 防火墙配置

```bash
# 开放必要端口
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp

# 限制数据库端口（仅内网访问）
sudo ufw deny 5432/tcp
sudo ufw deny 6379/tcp
```

---

## 📊 性能优化

### 资源限制

在生产环境中，建议设置容器资源限制：

```yaml
services:
  app:
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 2G
        reservations:
          cpus: '1.0'
          memory: 1G
```

### 数据库优化

PostgreSQL容器已包含性能优化配置：

- `shared_buffers`: 256MB
- `effective_cache_size`: 1GB
- `maintenance_work_mem`: 64MB
- `max_connections`: 200

---

## 🔄 备份和恢复

### 数据备份

```bash
# 数据库备份
docker-compose exec postgres pg_dump -U octi_user octi_db > backup.sql

# Redis备份
docker-compose exec redis redis-cli --rdb /data/backup.rdb

# 文件备份
docker cp octi-app:/app/public/uploads ./uploads-backup
```

### 数据恢复

```bash
# 数据库恢复
docker-compose exec -T postgres psql -U octi_user octi_db < backup.sql

# Redis恢复
docker cp backup.rdb octi-redis:/data/dump.rdb
docker-compose restart redis
```

---

## 🚨 故障排除

### 常见问题

1. **容器启动失败**

   ```bash
   # 查看容器状态
   docker-compose ps

   # 查看错误日志
   docker-compose logs <service-name>
   ```

2. **数据库连接失败**

   ```bash
   # 检查数据库状态
   docker-compose exec postgres pg_isready -U octi_user

   # 检查网络连接
   docker-compose exec app ping postgres
   ```

3. **内存不足**

   ```bash
   # 查看容器资源使用
   docker stats

   # 清理未使用的镜像
   docker system prune -f
   ```

### 调试模式

开发环境支持调试模式：

```bash
# 进入应用容器
./scripts/docker-dev.sh shell

# 查看应用日志
npm run dev

# 运行测试
npm test
```

---

## 📈 扩展部署

### 多实例部署

生产环境可以通过Docker Swarm或Kubernetes进行扩展：

```bash
# Docker Swarm示例
docker swarm init
docker stack deploy -c docker-compose.yml octi-stack
```

### 负载均衡

Nginx配置已支持上游服务器负载均衡，可以轻松扩展到多个应用实例。

---

**部署支持**: 如有部署问题，请查看项目文档或提交Issue。
