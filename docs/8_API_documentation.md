# OCTI智能评估系统 - API文档 v4.0

**版本**: 4.0 - 智能体配置架构  
**日期**: 2025年7月27日  
**项目**: 组织能力OCTI评估系统 (Organizational Capacity Type Indicator)  
**架构类型**: 配置驱动 + 智能体模块化

---

## 📋 API变更记录

| 版本 | 日期       | API变更                   | 影响范围           |
| ---- | ---------- | ------------------------- | ------------------ |
| v3.0 | 2025-07-27 | 统一问卷API，优化数据结构 | 问卷生成、评估分析 |
| v4.0 | 2025-07-27 | **智能体配置化API重构**   | 全部API接口升级    |

---

## 1. API概述

### 1.1 基础信息

- **Base URL**: `https://api.octi.example.com/api/v4`
- **认证方式**: JWT <PERSON>
- **数据格式**: JSON
- **字符编码**: UTF-8
- **API风格**: RESTful

### 1.2 通用响应格式

```typescript
interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
    details?: any;
  };
  meta?: {
    timestamp: string;
    version: string;
    requestId: string;
  };
}
```

### 1.3 HTTP状态码

| 状态码 | 说明       | 使用场景           |
| ------ | ---------- | ------------------ |
| 200    | 成功       | 请求成功处理       |
| 201    | 创建成功   | 资源创建成功       |
| 400    | 请求错误   | 参数错误、格式错误 |
| 401    | 未授权     | 认证失败           |
| 403    | 禁止访问   | 权限不足           |
| 404    | 资源不存在 | 请求的资源不存在   |
| 429    | 请求过多   | 触发限流           |
| 500    | 服务器错误 | 内部服务器错误     |

---

## 2. 认证与授权

### 2.1 用户注册

**POST** `/auth/register`

```typescript
// 请求体
interface RegisterRequest {
  email: string;
  password: string;
  name: string;
  organizationName?: string;
}

// 响应体
interface RegisterResponse {
  user: {
    id: string;
    email: string;
    name: string;
    role: 'USER' | 'ADMIN';
  };
  token: string;
  expiresIn: number;
}
```

### 2.2 用户登录

**POST** `/auth/login`

```typescript
// 请求体
interface LoginRequest {
  email: string;
  password: string;
}

// 响应体
interface LoginResponse {
  user: {
    id: string;
    email: string;
    name: string;
    role: 'USER' | 'ADMIN';
  };
  token: string;
  expiresIn: number;
}
```

### 2.3 刷新Token

**POST** `/auth/refresh`

```typescript
// 请求头
Authorization: Bearer<refresh_token>;

// 响应体
interface RefreshResponse {
  token: string;
  expiresIn: number;
}
```

---

## 3. 组织管理API

### 3.1 创建组织

**POST** `/organizations`

```typescript
// 请求体
interface CreateOrganizationRequest {
  name: string;
  industry: string;
  size: 'SMALL' | 'MEDIUM' | 'LARGE';
  description?: string;
}

// 响应体
interface Organization {
  id: string;
  name: string;
  industry: string;
  size: string;
  userId: string;
  createdAt: string;
  updatedAt: string;
}
```

### 3.2 获取组织列表

**GET** `/organizations`

```typescript
// 查询参数
interface GetOrganizationsQuery {
  page?: number;
  limit?: number;
  search?: string;
}

// 响应体
interface GetOrganizationsResponse {
  organizations: Organization[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}
```

### 3.3 获取组织详情

**GET** `/organizations/{organizationId}`

```typescript
// 响应体
interface OrganizationDetail extends Organization {
  nonprofitProfile?: NonprofitProfile;
  assessments?: Assessment[];
}
```

### 3.4 更新组织信息

**PUT** `/organizations/{organizationId}`

```typescript
// 请求体
interface UpdateOrganizationRequest {
  name?: string;
  industry?: string;
  size?: 'SMALL' | 'MEDIUM' | 'LARGE';
  description?: string;
}
```

---

## 4. 公益组织画像API

### 4.1 创建公益组织画像

**POST** `/organizations/{organizationId}/nonprofit-profile`

```typescript
// 请求体
interface CreateNonprofitProfileRequest {
  organizationType: string;
  serviceArea: string[];
  organizationScale: string;
  developmentStage: string;
  operatingModel: string;
  impactPositioning: string;
  organizationalCulture: string;
  missionVision: {
    mission: string;
    vision: string;
    values: string[];
  };
  governance: {
    boardStructure: string;
    decisionMaking: string;
    accountability: string;
  };
  resourceProfile: {
    fundingSources: string[];
    humanResources: string;
    technicalCapacity: string;
  };
  impactMeasurement: {
    evaluationMethods: string[];
    keyIndicators: string[];
    reportingFrequency: string;
  };
  challenges: string[];
  goals: string[];
  region: string;
  foundedYear: number;
  keyMetrics: Record<string, any>;
}

// 响应体
interface NonprofitProfile {
  id: string;
  organizationId: string;
  organizationType: string;
  serviceArea: string[];
  organizationScale: string;
  developmentStage: string;
  operatingModel: string;
  impactPositioning: string;
  organizationalCulture: string;
  missionVision: any;
  governance: any;
  resourceProfile: any;
  impactMeasurement: any;
  challenges: string[];
  goals: string[];
  region: string;
  foundedYear: number;
  keyMetrics: any;
  createdAt: string;
  updatedAt: string;
}
```

### 4.2 获取公益组织画像

**GET** `/organizations/{organizationId}/nonprofit-profile`

### 4.3 更新公益组织画像

**PUT** `/organizations/{organizationId}/nonprofit-profile`

---

## 5. 多轮对话API

### 5.1 创建对话会话

**POST** `/organizations/{organizationId}/dialogue-sessions`

```typescript
// 请求体
interface CreateDialogueSessionRequest {
  nonprofitProfileId: string;
  totalRounds?: number; // 默认3轮
}

// 响应体
interface DialogueSession {
  id: string;
  organizationId: string;
  nonprofitProfileId: string;
  status: 'ACTIVE' | 'COMPLETED' | 'PAUSED';
  currentRound: number;
  totalRounds: number;
  conversationHistory: any[];
  extractedInsights: any;
  createdAt: string;
  completedAt?: string;
}
```

### 5.2 发送对话消息

**POST** `/dialogue-sessions/{sessionId}/messages`

```typescript
// 请求体
interface SendMessageRequest {
  content: string;
  type: 'USER';
  metadata?: Record<string, any>;
}

// 响应体
interface DialogueMessage {
  id: string;
  sessionId: string;
  type: 'SYSTEM' | 'USER' | 'ASSISTANT';
  content: string;
  metadata?: any;
  roundNumber: number;
  timestamp: string;
}
```

### 5.3 获取对话历史

**GET** `/dialogue-sessions/{sessionId}/messages`

```typescript
// 查询参数
interface GetMessagesQuery {
  roundNumber?: number;
  type?: 'SYSTEM' | 'USER' | 'ASSISTANT';
  limit?: number;
  offset?: number;
}

// 响应体
interface GetMessagesResponse {
  messages: DialogueMessage[];
  session: DialogueSession;
}
```

### 5.4 完成对话会话

**POST** `/dialogue-sessions/{sessionId}/complete`

---

## 6. 智能问卷API

### 6.1 生成混合问卷

**POST** `/organizations/{organizationId}/questionnaires/generate`

```typescript
// 请求体
interface GenerateQuestionnaireRequest {
  nonprofitProfileId: string;
  sessionId?: string;
  version: 'STANDARD' | 'PROFESSIONAL';
  presetQuestionIds?: string[]; // 可选择特定预设题目
}

// 响应体
interface IntelligentQuestionnaire {
  id: string;
  organizationId: string;
  nonprofitProfileId: string;
  sessionId?: string;
  presetQuestions: Question[]; // 32道预设题目
  generatedQuestions: Question[]; // 28道智能生成题目
  questionnaireStructure: {
    SF: Question[]; // 15题 (8预设+7生成)
    IT: Question[]; // 15题 (8预设+7生成)
    MV: Question[]; // 15题 (8预设+7生成)
    AD: Question[]; // 15题 (8预设+7生成)
  };
  generationContext: {
    organizationProfile: any;
    configVersion: string;
    generationTimestamp: string;
  };
  status: 'GENERATED' | 'IN_PROGRESS' | 'COMPLETED';
  presetCount: number;
  generatedCount: number;
  createdAt: string;
  updatedAt: string;
}

interface Question {
  id: string;
  dimension: 'SF' | 'IT' | 'MV' | 'AD';
  subDimension: string;
  questionType: 'CHOICE' | 'SCENARIO' | 'RANKING' | 'SCALE';
  questionContent: {
    title: string;
    description?: string;
    options?: string[];
    scaleRange?: { min: number; max: number; labels: string[] };
  };
  displayOrder: number;
  isPreset: boolean; // 区分预设题目和生成题目
  version?: string;
}
```

### 6.2 获取问卷详情

**GET** `/questionnaires/{questionnaireId}`

### 6.3 获取预设题目库

**GET** `/questionnaires/preset-questions`

```typescript
// 查询参数
interface GetPresetQuestionsQuery {
  dimension?: 'SF' | 'IT' | 'MV' | 'AD';
  subDimension?: string;
  version?: string;
  isActive?: boolean;
}

// 响应体
interface GetPresetQuestionsResponse {
  questions: PresetQuestion[];
  total: number;
  dimensions: {
    SF: number;
    IT: number;
    MV: number;
    AD: number;
  };
}

interface PresetQuestion {
  id: string;
  dimension: string;
  subDimension: string;
  questionType: string;
  questionContent: any;
  displayOrder: number;
  version: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}
```

---

## 7. 评估分析API

### 7.1 创建评估

**POST** `/organizations/{organizationId}/assessments`

```typescript
// 请求体
interface CreateAssessmentRequest {
  nonprofitProfileId: string;
  questionnaireId: string;
  version: 'STANDARD' | 'PROFESSIONAL';
  answersData: {
    [questionId: string]: {
      answer: any;
      timestamp: string;
      confidence?: number;
    };
  };
  contextualFactors?: {
    assessmentEnvironment: string;
    timeConstraints: string;
    additionalContext: string;
  };
}

// 响应体
interface Assessment {
  id: string;
  organizationId: string;
  nonprofitProfileId: string;
  questionnaireId: string;
  version: 'STANDARD' | 'PROFESSIONAL';
  status: 'PENDING' | 'ANALYZING' | 'COMPLETED' | 'FAILED';
  questionnaireData: any;
  answersData: any;
  primaryAnalysisResult?: AnalysisResult;
  secondaryAnalysisResult?: AnalysisResult; // 仅专业版
  fusedAnalysisResult?: AnalysisResult; // 仅专业版
  reportData?: AssessmentReport;
  contextualFactors?: any;
  nonprofitSpecificAnalysis?: any;
  performanceMetrics?: {
    completionTime: number;
    analysisAccuracy: number;
    userSatisfaction?: number;
  };
  primaryModel: 'MINIMAX' | 'DEEPSEEK';
  secondaryModel?: 'MINIMAX' | 'DEEPSEEK';
  createdAt: string;
  completedAt?: string;
}

interface AnalysisResult {
  octiType: string;
  dimensions: {
    SF: { score: number; analysis: string };
    IT: { score: number; analysis: string };
    MV: { score: number; analysis: string };
    AD: { score: number; analysis: string };
  };
  overallAnalysis: string;
  recommendations: string[];
  strengths: string[];
  improvementAreas: string[];
  modelUsed: string;
  confidence: number;
  analysisTimestamp: string;
}
```

### 7.2 获取评估详情

**GET** `/assessments/{assessmentId}`

### 7.3 获取评估列表

**GET** `/organizations/{organizationId}/assessments`

```typescript
// 查询参数
interface GetAssessmentsQuery {
  status?: 'PENDING' | 'ANALYZING' | 'COMPLETED' | 'FAILED';
  version?: 'STANDARD' | 'PROFESSIONAL';
  page?: number;
  limit?: number;
  sortBy?: 'createdAt' | 'completedAt' | 'version';
  sortOrder?: 'asc' | 'desc';
}
```

### 7.4 重新分析评估

**POST** `/assessments/{assessmentId}/reanalyze`

```typescript
// 请求体
interface ReanalyzeRequest {
  version?: 'STANDARD' | 'PROFESSIONAL'; // 可升级版本
  forceRegenerate?: boolean;
  analysisOptions?: {
    primaryModel?: 'MINIMAX' | 'DEEPSEEK';
    secondaryModel?: 'MINIMAX' | 'DEEPSEEK';
    analysisDepth?: 'BASIC' | 'DETAILED' | 'COMPREHENSIVE';
  };
}
```

---

## 8. 报告生成API

### 8.1 生成评估报告

**POST** `/assessments/{assessmentId}/reports/generate`

```typescript
// 请求体
interface GenerateReportRequest {
  reportType: 'SUMMARY' | 'DETAILED' | 'EXECUTIVE';
  format: 'JSON' | 'PDF' | 'HTML';
  language?: 'zh-CN' | 'en-US';
  customSections?: string[];
}

// 响应体
interface AssessmentReport {
  id: string;
  assessmentId: string;
  reportType: string;
  format: string;
  language: string;
  content: {
    executiveSummary: string;
    octiTypeAnalysis: {
      primaryType: string;
      typeDescription: string;
      typicalCharacteristics: string[];
    };
    dimensionAnalysis: {
      SF: DimensionReport;
      IT: DimensionReport;
      MV: DimensionReport;
      AD: DimensionReport;
    };
    strengthsAndOpportunities: {
      coreStrengths: string[];
      developmentOpportunities: string[];
      quickWins: string[];
    };
    actionPlan: {
      shortTerm: ActionItem[];
      mediumTerm: ActionItem[];
      longTerm: ActionItem[];
    };
    benchmarkComparison?: {
      industryAverage: any;
      peerComparison: any;
      historicalTrend: any;
    };
    appendices?: {
      methodology: string;
      dataQuality: any;
      limitations: string[];
    };
  };
  generatedAt: string;
  downloadUrl?: string;
}

interface DimensionReport {
  score: number;
  level: 'LOW' | 'MEDIUM' | 'HIGH';
  analysis: string;
  keyFindings: string[];
  recommendations: string[];
  subDimensionScores: Record<string, number>;
}

interface ActionItem {
  title: string;
  description: string;
  priority: 'HIGH' | 'MEDIUM' | 'LOW';
  estimatedEffort: string;
  expectedImpact: string;
  resources: string[];
  timeline: string;
}
```

### 8.2 获取报告详情

**GET** `/reports/{reportId}`

### 8.3 下载报告

**GET** `/reports/{reportId}/download`

```typescript
// 查询参数
interface DownloadReportQuery {
  format?: 'PDF' | 'HTML' | 'JSON';
}

// 响应
// Content-Type: application/pdf | text/html | application/json
// Content-Disposition: attachment; filename="octi-report-{reportId}.{format}"
```

---

## 9. 智能体配置API

### 9.1 获取智能体配置

**GET** `/admin/agent-configs`

```typescript
// 查询参数
interface GetAgentConfigsQuery {
  agentType?: 'QUESTION_DESIGNER' | 'ORGANIZATION_TUTOR';
  version?: string;
  isActive?: boolean;
}

// 响应体
interface AIAgentConfig {
  id: string;
  agentType: 'QUESTION_DESIGNER' | 'ORGANIZATION_TUTOR';
  configName: string;
  promptTemplate: any;
  parameters: any;
  version: string;
  configSchema: any;
  isActive: boolean;
  parentConfigId?: string;
  createdAt: string;
  updatedAt: string;
}
```

### 9.2 更新智能体配置

**PUT** `/admin/agent-configs/{configId}`

```typescript
// 请求体
interface UpdateAgentConfigRequest {
  promptTemplate?: any;
  parameters?: any;
  isActive?: boolean;
  changeLog?: string;
}
```

### 9.3 创建配置版本

**POST** `/admin/agent-configs/{configId}/versions`

```typescript
// 请求体
interface CreateConfigVersionRequest {
  version: string;
  changeLog: string;
  promptTemplate: any;
  parameters: any;
}
```

### 9.4 热更新配置

**POST** `/admin/agent-configs/hot-reload`

```typescript
// 请求体
interface HotReloadRequest {
  configIds?: string[]; // 不指定则重载所有
  validateOnly?: boolean; // 仅验证不应用
}

// 响应体
interface HotReloadResponse {
  success: boolean;
  reloadedConfigs: string[];
  validationErrors?: {
    configId: string;
    errors: string[];
  }[];
  timestamp: string;
}
```

---

## 10. 系统监控API

### 10.1 获取系统状态

**GET** `/system/health`

```typescript
// 响应体
interface SystemHealth {
  status: 'HEALTHY' | 'DEGRADED' | 'DOWN';
  timestamp: string;
  services: {
    database: { status: string; responseTime: number };
    redis: { status: string; responseTime: number };
    llmServices: {
      minimax: { status: string; responseTime: number; quota: any };
      deepseek: { status: string; responseTime: number; quota: any };
    };
  };
  metrics: {
    activeUsers: number;
    activeAssessments: number;
    apiRequestsPerMinute: number;
    averageResponseTime: number;
  };
}
```

### 10.2 获取使用统计

**GET** `/admin/usage-stats`

```typescript
// 查询参数
interface GetUsageStatsQuery {
  startDate: string;
  endDate: string;
  granularity: 'HOUR' | 'DAY' | 'WEEK' | 'MONTH';
  metrics?: string[]; // 指定要获取的指标
}

// 响应体
interface UsageStats {
  period: { start: string; end: string };
  metrics: {
    totalAssessments: number;
    standardVersionUsage: number;
    professionalVersionUsage: number;
    averageCompletionTime: number;
    userSatisfactionScore: number;
    llmApiCalls: {
      minimax: number;
      deepseek: number;
    };
    configurationChanges: number;
  };
  trends: {
    assessmentGrowth: number;
    userEngagement: number;
    systemPerformance: number;
  };
}
```

---

## 11. 错误处理

### 11.1 错误代码定义

| 错误代码         | HTTP状态码 | 说明             | 解决方案                 |
| ---------------- | ---------- | ---------------- | ------------------------ |
| `AUTH_001`       | 401        | Token无效或过期  | 重新登录获取新Token      |
| `AUTH_002`       | 403        | 权限不足         | 联系管理员获取权限       |
| `VALIDATION_001` | 400        | 请求参数验证失败 | 检查请求参数格式         |
| `RESOURCE_001`   | 404        | 资源不存在       | 确认资源ID是否正确       |
| `BUSINESS_001`   | 400        | 业务逻辑错误     | 根据错误信息调整业务流程 |
| `LLM_001`        | 503        | LLM服务不可用    | 稍后重试或联系技术支持   |
| `CONFIG_001`     | 500        | 配置文件错误     | 联系管理员检查配置       |
| `RATE_LIMIT_001` | 429        | 请求频率超限     | 降低请求频率             |

### 11.2 错误响应示例

```typescript
// 验证错误
{
  "success": false,
  "error": {
    "code": "VALIDATION_001",
    "message": "请求参数验证失败",
    "details": {
      "field": "email",
      "reason": "邮箱格式不正确"
    }
  },
  "meta": {
    "timestamp": "2025-07-27T10:30:00Z",
    "version": "4.0",
    "requestId": "req_123456789"
  }
}

// LLM服务错误
{
  "success": false,
  "error": {
    "code": "LLM_001",
    "message": "LLM服务暂时不可用",
    "details": {
      "service": "minimax",
      "retryAfter": 30,
      "fallbackAvailable": true
    }
  }
}
```

---

## 12. 限流与配额

### 12.1 API限流规则

| 用户类型 | 限流规则       | 说明        |
| -------- | -------------- | ----------- |
| 免费用户 | 100请求/小时   | 基础API访问 |
| 标准用户 | 1000请求/小时  | 标准版功能  |
| 专业用户 | 5000请求/小时  | 专业版功能  |
| 管理员   | 10000请求/小时 | 管理功能    |

### 12.2 特殊限制

- **问卷生成**: 每个组织每天最多生成10份问卷
- **评估分析**: 标准版每月5次，专业版每月50次
- **报告下载**: 每个报告24小时内最多下载10次
- **配置更新**: 管理员每小时最多更新5次配置

### 12.3 限流响应头

```
X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 999
X-RateLimit-Reset: 1627834800
X-RateLimit-Type: user
```

---

## 13. 版本兼容性

### 13.1 API版本策略

- **主版本**: 不兼容的重大变更 (v3 → v4)
- **次版本**: 向后兼容的功能增加 (v4.0 → v4.1)
- **修订版本**: 向后兼容的问题修复 (v4.0.0 → v4.0.1)

### 13.2 弃用策略

- 新版本发布后，旧版本继续支持6个月
- 弃用功能提前3个月通知
- 关键安全更新会向后移植到支持的版本

### 13.3 迁移指南

从v3.0迁移到v4.0的主要变更：

1. **智能体配置化**: 新增配置管理API
2. **混合问卷模式**: 问卷生成API结构变更
3. **双模型协作**: 评估分析API增加模型选择
4. **增强报告**: 报告生成API功能扩展

---

## 14. SDK与示例

### 14.1 JavaScript/TypeScript SDK

```typescript
import { OCTIClient } from '@octi/sdk';

const client = new OCTIClient({
  baseURL: 'https://api.octi.example.com/api/v4',
  apiKey: 'your-api-key',
});

// 创建组织
const organization = await client.organizations.create({
  name: '示例公益组织',
  industry: '教育',
  size: 'MEDIUM',
});

// 生成问卷
const questionnaire = await client.questionnaires.generate({
  organizationId: organization.id,
  nonprofitProfileId: 'profile-id',
  version: 'PROFESSIONAL',
});

// 创建评估
const assessment = await client.assessments.create({
  organizationId: organization.id,
  nonprofitProfileId: 'profile-id',
  questionnaireId: questionnaire.id,
  version: 'PROFESSIONAL',
  answersData: {
    'question-1': { answer: 'A', timestamp: new Date().toISOString() },
  },
});
```

### 14.2 Python SDK示例

```python
from octi_sdk import OCTIClient

client = OCTIClient(
    base_url='https://api.octi.example.com/api/v4',
    api_key='your-api-key'
)

# 创建组织
organization = client.organizations.create({
    'name': '示例公益组织',
    'industry': '教育',
    'size': 'MEDIUM'
})

# 生成问卷
questionnaire = client.questionnaires.generate(
    organization_id=organization['id'],
    nonprofit_profile_id='profile-id',
    version='PROFESSIONAL'
)
```

---

## 15. 测试环境

### 15.1 测试环境信息

- **测试Base URL**: `https://api-test.octi.example.com/api/v4`
- **测试账号**: <EMAIL> / test123456
- **测试组织**: 已预置测试数据
- **限流**: 测试环境限流较宽松

### 15.2 测试数据

测试环境提供以下预置数据：

- 3个测试组织（不同规模和类型）
- 完整的公益组织画像
- 示例问卷和评估结果
- 各种配置版本

### 15.3 Postman集合

提供完整的Postman API集合，包含：

- 所有API端点的示例请求
- 环境变量配置
- 自动化测试脚本
- 数据驱动测试

下载地址：`https://api.octi.example.com/docs/postman-collection.json`

---

## 16. 支持与联系

### 16.1 技术支持

- **文档**: https://docs.octi.example.com
- **API状态**: https://status.octi.example.com
- **技术支持**: <EMAIL>
- **社区论坛**: https://community.octi.example.com

### 16.2 更新通知

- **更新日志**: https://docs.octi.example.com/changelog
- **邮件通知**: 订阅API更新通知
- **Webhook**: 配置系统状态变更通知

---

**文档版本**: v4.0  
**最后更新**: 2025年7月27日  
**下次更新**: 根据功能迭代计划
