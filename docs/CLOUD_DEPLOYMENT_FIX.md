# OCTI智能评估系统 - 云端部署修复指南

## 🚨 问题概述

云端部署遇到的主要问题：

1. **SSL库缺失**：`libssl.so.1.1: No such file or directory`
2. **数据库连接失败**：Prisma客户端无法连接数据库
3. **API参数解析错误**：`Cannot read properties of undefined (reading 'organizationType')`
4. **健康检查超时**：容器状态显示 `unhealthy`

## 🔧 修复内容

### 1. Dockerfile修复

**文件路径**: `Dockerfile`

- 将 `libssl1.1` 改为 `libssl3`（适配 Debian Bookworm）
- 移除了不存在的 `libssl-dev` 依赖
- 确保SSL兼容性支持

### 2. Prisma配置修复

**文件路径**: `prisma/schema.prisma`

- 使用 `debian-openssl-3.0.x` 二进制目标
- 移除了不兼容的 `debian-openssl-1.1.x`

### 3. API参数验证增强

**文件路径**: `src/app/api/questionnaire/generate-intelligent/route.ts`

- 添加了详细的参数验证
- 增强了错误处理和日志记录
- 防止 `undefined` 参数导致的错误

### 4. 健康检查优化

**文件路径**: `docker-compose.yml`

- 增加了健康检查超时时间
- 延长了启动等待时间
- 提高了重试次数

## 🚀 部署步骤

### 方法1：使用云端部署脚本（推荐）

```bash
# 1. 上传修复后的代码到云服务器

# 2. 执行云端部署脚本
chmod +x scripts/deploy-tencent-cloud-octi.sh
./scripts/deploy-tencent-cloud-octi.sh --mode simple --force-rebuild
```

### 方法2：使用本机部署脚本

```bash
# 1. 本机测试部署
chmod +x scripts/deploy-production.sh
./scripts/deploy-production.sh

# 2. 验证无问题后上传到云端
```

### 方法3：手动部署

```bash
# 1. 停止现有服务
docker-compose down

# 2. 清理旧镜像
docker rmi octi-production:latest
docker system prune -f

# 3. 重新构建
docker-compose build --no-cache

# 4. 启动服务
docker-compose up -d

# 5. 等待启动完成
sleep 120

# 6. 检查状态
docker-compose ps
curl http://localhost:3000/api/health
```

## 🧪 测试验证

### 使用测试脚本

```bash
# 赋予执行权限
chmod +x scripts/test-intelligent-generation.sh

# 运行测试
./scripts/test-intelligent-generation.sh
```

### 手动测试

```bash
# 1. 检查应用健康
curl http://localhost:3000/api/health

# 2. 测试智能生成
curl -X POST http://localhost:3000/api/questionnaire/generate-intelligent \
  -H "Content-Type: application/json" \
  -d '{
    "dimension": "SF",
    "profile": {
      "organizationType": "民间组织",
      "serviceArea": ["医疗健康"],
      "developmentStage": "成长期",
      "organizationScale": "中型"
    },
    "count": 2
  }'
```

## 📊 监控和诊断

### 检查容器状态

```bash
# 查看所有容器状态
docker-compose ps

# 查看应用日志
docker-compose logs app

# 查看数据库日志
docker-compose logs postgres
```

### 健康检查

```bash
# 应用健康检查
curl http://localhost:3000/api/health

# 数据库连接检查
docker-compose exec postgres psql -U postgres -d octi -c "SELECT 1;"

# Redis连接检查
docker-compose exec redis redis-cli ping
```

## 🔍 故障排除

### 如果应用仍然 unhealthy

1. **检查SSL库**：
```bash
docker-compose exec app ldd /app/node_modules/.prisma/client/libquery_engine-linux-musl.so.node
```

2. **重新生成Prisma客户端**：
```bash
docker-compose exec app npx prisma generate
```

3. **检查环境变量**：
```bash
docker-compose exec app env | grep -E "(DATABASE|MINIMAX|DEEPSEEK)"
```

### 如果智能生成失败

1. **检查API密钥**：
```bash
docker-compose exec app env | grep MINIMAX_API_KEY
```

2. **查看详细日志**：
```bash
docker-compose logs app | grep -i "intelligent\|generate\|error"
```

## 📝 注意事项

1. **首次启动**：应用完全启动可能需要2-3分钟
2. **API密钥**：确保在 `.env` 文件中配置了有效的 MiniMax 和 DeepSeek API密钥
3. **内存要求**：建议服务器至少有2GB可用内存
4. **网络连接**：确保服务器可以访问外部AI服务API

## ✅ 验证成功标志

- 所有容器状态为 `Up` 且应用显示 `healthy`
- API健康检查返回 `{"status":"healthy"}`
- 智能生成API能正常返回问题数据
- 前端页面能正常加载和使用

修复完成后，您的OCTI系统应该能够正常运行智能问卷生成功能！
