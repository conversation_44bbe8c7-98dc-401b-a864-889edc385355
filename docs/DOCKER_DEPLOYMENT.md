# OCTI智能评估系统 - Docker部署指南

## 📋 概述

OCTI智能评估系统采用简化的Docker部署方案，包含核心服务：

## 🏗️ 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Next.js App   │    │   PostgreSQL    │    │     Redis       │
│     :3000       │◄──►│     :5432       │    │     :6379       │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

**核心服务：**
- **Next.js应用** - 主应用服务，包含前端和API
- **PostgreSQL** - 主数据库，存储用户数据和评估结果
- **Redis** - 缓存服务，提升性能和会话管理

## 📁 配置文件说明

| 文件 | 用途 | 适用场景 |
|------|------|----------|
| `docker-compose.yml` | 生产环境 | ✅ 生产部署、功能测试 |
| `docker-compose.dev.yml` | 开发环境 | 🔧 本地开发、调试 |
| `Dockerfile` | 多阶段构建配置 | 📦 镜像构建 |

## 🚀 快速开始

### 1. 环境准备

```bash
# 检查Docker环境
docker --version
docker-compose --version

# 复制环境变量文件
cp .env.example .env

# 编辑环境变量 (添加你的API密钥)
nano .env
```

### 2. 启动生产环境 (推荐)

```bash
# 方式1: 使用docker-compose
docker-compose up -d

# 方式2: 使用部署脚本
./scripts/docker-deploy.sh prod

# 检查服务状态
docker-compose ps
```

### 3. 访问应用

- **应用主页**: http://localhost:3000
- **健康检查**: http://localhost:3000/api/health

## 🔧 详细部署选项

### 开发环境

```bash
# 启动开发环境 (包含调试工具)
docker-compose -f docker-compose.dev.yml up -d

# 访问地址
# - 应用: http://localhost:3000
# - 数据库管理: http://localhost:8080 (Adminer)
```

## 🛠️ 管理命令

### 使用部署脚本 (推荐)

```bash
# 查看帮助
./scripts/docker-deploy.sh help

# 启动不同环境
./scripts/docker-deploy.sh dev        # 开发环境
./scripts/docker-deploy.sh prod       # 生产环境

# 管理服务
./scripts/docker-deploy.sh stop       # 停止服务
./scripts/docker-deploy.sh restart    # 重启服务
./scripts/docker-deploy.sh logs       # 查看日志
./scripts/docker-deploy.sh status     # 查看状态
./scripts/docker-deploy.sh health     # 健康检查

# 维护操作
./scripts/docker-deploy.sh build      # 构建镜像
./scripts/docker-deploy.sh clean      # 清理资源
```

### 使用原生Docker命令

```bash
# 构建镜像
docker build -t octi-production:latest .

# 启动服务
docker-compose up -d

# 查看日志
docker-compose logs -f app

# 停止服务
docker-compose down

# 重启单个服务
docker-compose restart app

# 进入容器
docker-compose exec app bash
```

## 📊 监控和日志

### 健康检查

```bash
# 检查应用健康状态
curl http://localhost:3000/api/health

# 查看详细健康信息
curl http://localhost:3000/api/health | jq .
```

### 日志查看

```bash
# 查看所有服务日志
docker-compose logs -f

# 查看特定服务日志
docker-compose logs -f app
docker-compose logs -f postgres
docker-compose logs -f redis

# 查看最近100行日志
docker-compose logs --tail=100 app
```

### 性能监控

```bash
# 查看容器资源使用情况
docker stats

# 查看容器详细信息
docker-compose ps
docker inspect <container_name>
```

## 🔒 安全配置

### 环境变量安全

```bash
# 生产环境必须修改的配置
NEXTAUTH_SECRET=your_secure_secret_32_chars_minimum
JWT_SECRET=your_jwt_secret_32_chars_minimum
POSTGRES_PASSWORD=your_secure_database_password
REDIS_PASSWORD=your_secure_redis_password
```

### 网络安全

- 生产环境仅暴露必要端口
- 使用内部网络进行服务通信
- 配置防火墙规则

## 🚨 故障排除

### 常见问题

1. **Redis连接错误**
   ```bash
   # 检查Redis服务状态
   docker-compose exec redis redis-cli ping
   
   # 重启Redis服务
   docker-compose restart redis
   ```

2. **数据库连接错误**
   ```bash
   # 检查数据库状态
   docker-compose exec postgres pg_isready
   
   # 查看数据库日志
   docker-compose logs postgres
   ```

3. **应用启动失败**
   ```bash
   # 查看应用日志
   docker-compose logs app
   
   # 重新构建镜像
   docker-compose build --no-cache app
   ```

### 数据备份与恢复

```bash
# 备份数据库
docker-compose exec postgres pg_dump -U postgres octi > backup.sql

# 恢复数据库
docker-compose exec -T postgres psql -U postgres octi < backup.sql

# 备份Redis数据
docker-compose exec redis redis-cli BGSAVE
```

## 📈 性能优化

### 资源限制

在生产环境中，建议为每个服务设置资源限制：

```yaml
deploy:
  resources:
    limits:
      cpus: '1.0'
      memory: 1G
    reservations:
      cpus: '0.5'
      memory: 512M
```

### 缓存优化

- Redis内存限制: 建议设置为可用内存的50-70%
- PostgreSQL缓存: 根据数据量调整shared_buffers
- Next.js构建缓存: 使用.dockerignore优化构建

## 🔄 更新部署

```bash
# 拉取最新代码
git pull origin main

# 重新构建并部署
docker-compose up -d --build

# 或使用脚本
./scripts/docker-deploy.sh restart prod
```

## 📞 支持

如果遇到问题，请：

1. 查看日志: `docker-compose logs`
2. 检查健康状态: `curl http://localhost:3000/api/health`
3. 查看容器状态: `docker-compose ps`
4. 提交Issue到项目仓库
