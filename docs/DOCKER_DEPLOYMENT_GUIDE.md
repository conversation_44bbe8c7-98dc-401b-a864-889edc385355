# OCTI Docker 高效部署指南

## 🚀 方案1：本地构建验证 + Docker部署分离

这个方案可以在几秒钟内发现问题，避免漫长的Docker构建等待。

### 📋 工作流程

```bash
# 1. 快速本地验证（30秒内完成）
./scripts/quick-check.sh

# 2. 验证通过后，安全Docker构建
./scripts/docker-build-safe.sh
```

## 🛠️ 脚本说明

### 1. `scripts/quick-check.sh` - 快速验证脚本

**功能：** 在本地快速验证代码，避免Docker构建时才发现错误

**检查项目：**
- ✅ Node.js环境检查
- ✅ 项目依赖验证
- ✅ TypeScript类型检查
- ✅ ESLint代码检查（警告不阻止）
- ✅ Prisma模式验证
- ✅ Next.js构建测试

**使用方法：**
```bash
# 完整验证
./scripts/quick-check.sh

# 只检查TypeScript
./scripts/quick-check.sh typescript

# 只测试构建
./scripts/quick-check.sh build

# 查看帮助
./scripts/quick-check.sh help
```

### 2. `scripts/docker-build-safe.sh` - 安全Docker构建脚本

**功能：** 先执行本地验证，验证通过后再进行Docker构建

**特性：**
- 🔍 自动调用快速验证
- 🧹 清理旧Docker资源
- 📦 构建Docker镜像
- 🧪 测试镜像可用性
- ⏱️ 显示构建耗时

**使用方法：**
```bash
# 标准构建流程
./scripts/docker-build-safe.sh

# 跳过验证直接构建（不推荐）
./scripts/docker-build-safe.sh --skip-check

# 指定镜像名称和标签
./scripts/docker-build-safe.sh --image-name my-octi --tag v1.0.0

# 查看帮助
./scripts/docker-build-safe.sh --help
```

## 🎯 推荐工作流程

### 开发阶段
```bash
# 1. 代码修改后，快速验证
./scripts/quick-check.sh

# 2. 修复问题，重新验证
./scripts/quick-check.sh

# 3. 验证通过后，进行Docker构建
./scripts/docker-build-safe.sh
```

### 生产部署
```bash
# 1. 完整验证和构建
./scripts/docker-build-safe.sh --tag production

# 2. 使用现有的生产部署脚本
./scripts/deploy-production.sh
```

## ⚡ 效率对比

### 传统方式：
```
代码修改 → Docker构建(10-15分钟) → 发现错误 → 修复 → 重新构建(10-15分钟)
总耗时：20-30分钟
```

### 新方案：
```
代码修改 → 快速验证(30秒) → 发现错误 → 修复 → 重新验证(30秒) → Docker构建(10-15分钟)
总耗时：11-16分钟，节省50%以上时间
```

## 🔧 故障排除

### 常见问题

**1. 快速验证失败**
```bash
# 查看详细错误信息
./scripts/quick-check.sh typescript

# 安装缺失依赖
npm install

# 修复TypeScript错误后重试
./scripts/quick-check.sh
```

**2. Docker构建失败**
```bash
# 检查Docker环境
docker info

# 清理Docker资源
docker system prune -f

# 重新构建
./scripts/docker-build-safe.sh
```

**3. 跳过验证强制构建**
```bash
# 仅在紧急情况下使用
./scripts/docker-build-safe.sh --skip-check
```

## 📊 性能优化建议

### 1. 本地开发优化
- 使用 `npm run dev` 进行热重载开发
- 定期运行 `./scripts/quick-check.sh` 检查代码质量
- 提交前必须通过完整验证

### 2. Docker构建优化
- 利用 `.dockerignore` 减少构建上下文
- 使用多阶段构建缓存依赖层
- 定期清理无用的Docker资源

### 3. CI/CD集成
```yaml
# GitHub Actions 示例
- name: Quick Check
  run: ./scripts/quick-check.sh

- name: Docker Build
  run: ./scripts/docker-build-safe.sh --tag ${{ github.sha }}
```

## 🎉 总结

通过**本地构建验证 + Docker部署分离**的方案：

✅ **快速反馈** - 30秒内发现问题  
✅ **节省时间** - 避免重复的长时间构建  
✅ **提高成功率** - 验证通过后构建成功率接近100%  
✅ **保持兼容** - 与现有脚本完全兼容  
✅ **易于使用** - 简单的命令行接口  

现在您可以高效地进行Docker部署，不再需要为小错误等待漫长的构建时间！
