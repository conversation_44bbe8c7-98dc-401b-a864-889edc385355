# OCTI智能评估系统 - Docker快速开始指南

🚀 **5分钟快速部署OCTI智能评估系统**

---

## 📋 前置要求

- Docker >= 20.10.0
- Docker Compose >= 2.0.0
- 系统内存 >= 4GB
- 磁盘空间 >= 10GB

---

## ⚡ 快速开始

### 1️⃣ 克隆项目

```bash
git clone <repository-url>
cd octi_test
```

### 2️⃣ 初始化环境

```bash
# 使用Makefile（推荐）
make init

# 或手动操作
cp .env.example .env
```

### 3️⃣ 启动开发环境

```bash
# 首次启动（包含数据库初始化）
make dev-init

# 或使用脚本
./scripts/docker-dev.sh start --init
```

### 4️⃣ 验证部署

```bash
# 检查系统健康状态
make health

# 或使用验证脚本
./scripts/verify-deployment.sh dev
```

---

## 🌐 访问地址

部署成功后，您可以访问以下地址：

| 服务           | 地址                             | 说明               |
| -------------- | -------------------------------- | ------------------ |
| **主应用**     | http://localhost:3000            | OCTI评估系统主界面 |
| **API文档**    | http://localhost:3000/api        | API接口            |
| **健康检查**   | http://localhost:3000/api/health | 系统健康状态       |
| **数据库管理** | http://localhost:8080            | Adminer数据库管理  |
| **Redis管理**  | http://localhost:8081            | Redis Commander    |
| **邮件测试**   | http://localhost:8025            | MailHog邮件测试    |

---

## 🛠️ 常用命令

### 开发环境管理

```bash
# 启动开发环境
make dev

# 查看实时日志
make dev-logs

# 进入应用容器
make dev-shell

# 停止开发环境
make dev-stop

# 重启开发环境
make dev-restart

# 清理开发数据
make dev-clean
```

### 数据库操作

```bash
# 运行数据库迁移
make db-migrate

# 重置数据库
make db-reset

# 打开Prisma Studio
make db-studio

# 备份数据库
make backup
```

### 系统监控

```bash
# 查看容器状态
make status

# 检查系统健康
make health

# 查看所有日志
make logs
```

---

## 🔧 配置说明

### 环境变量配置

编辑 `.env` 文件，配置以下关键参数：

```bash
# 数据库配置
POSTGRES_DB=octi_db
POSTGRES_USER=octi_user
POSTGRES_PASSWORD=your_password

# Redis配置
REDIS_PASSWORD=your_redis_password

# AI服务配置（必需）
MINIMAX_API_KEY=your_minimax_api_key
DEEPSEEK_API_KEY=your_deepseek_api_key

# 应用配置
NEXTAUTH_SECRET=your_32_char_secret_key
```

### 数据库连接信息

开发环境数据库连接信息：

- **主机**: localhost
- **端口**: 5432
- **数据库**: octi_db
- **用户**: octi_user
- **密码**: octi_password

---

## 🚨 故障排除

### 常见问题

1. **端口冲突**

   ```bash
   # 检查端口占用
   lsof -i :3000
   lsof -i :5432
   lsof -i :6379

   # 停止冲突服务或修改端口配置
   ```

2. **容器启动失败**

   ```bash
   # 查看容器状态
   make status

   # 查看错误日志
   make dev-logs

   # 重新构建镜像
   make build
   ```

3. **数据库连接失败**

   ```bash
   # 检查数据库状态
   docker-compose -f docker-compose.dev.yml exec postgres pg_isready

   # 重启数据库
   docker-compose -f docker-compose.dev.yml restart postgres
   ```

4. **内存不足**

   ```bash
   # 查看资源使用
   docker stats

   # 清理未使用资源
   make clean-all
   ```

### 重置环境

如果遇到严重问题，可以完全重置环境：

```bash
# 停止所有服务
make dev-stop

# 清理所有数据
make dev-clean

# 重新初始化
make dev-init
```

---

## 📈 生产环境部署

### 快速部署到生产环境

```bash
# 1. 配置生产环境变量
cp .env.example .env
# 编辑 .env 文件，设置生产环境配置

# 2. 部署生产环境
make prod

# 3. 验证部署
./scripts/verify-deployment.sh prod
```

### 生产环境访问

- **应用**: https://your-domain.com
- **监控**: https://your-domain.com:3001 (Grafana)

---

## 📚 更多文档

- [完整部署指南](docs/12_docker_deployment.md)
- [项目架构文档](docs/3_project_architecture.md)
- [API文档](docs/8_API_documentation.md)

---

## 🆘 获取帮助

```bash
# 查看所有可用命令
make help

# 查看部署文档
make docs

# 验证部署状态
./scripts/verify-deployment.sh dev
```

---

## ✅ 部署检查清单

- [ ] Docker和Docker Compose已安装
- [ ] 项目已克隆到本地
- [ ] 环境变量已配置（.env文件）
- [ ] AI服务API密钥已设置
- [ ] 开发环境已启动
- [ ] 健康检查通过
- [ ] 可以访问主应用界面
- [ ] 数据库连接正常

---

**🎉 恭喜！您已成功部署OCTI智能评估系统！**

如有问题，请查看详细文档或提交Issue。
