# OCTI 环境变量配置指南

## 📁 环境文件结构

经过整理，现在项目只保留3个环境文件，用途明确：

```
├── .env.example      # 📋 模板文件 - 包含所有配置项的示例和说明
├── .env              # 🔧 默认环境文件 - 用于本地开发和Docker部署
└── .env.production   # 🚀 生产环境文件 - 用于正式部署
```

## 🎯 使用场景

### 1. 本地开发
```bash
# 使用 .env 文件
npm run dev
```

### 2. Docker本地部署
```bash
# 使用 .env 文件（默认）
docker-compose up -d
```

### 3. 生产环境部署
```bash
# 使用 .env.production 文件
./scripts/deploy-production.sh
```

### 4. 腾讯云部署
```bash
# 添加执行权限（如果需要）
chmod +x scripts/*.sh

# 使用 .env.production 文件
./scripts/deploy-tencent-cloud.sh
```

## ⚙️ 配置步骤

### 首次设置

1. **复制模板文件**
   ```bash
   # 创建本地开发环境配置
   cp .env.example .env
   
   # 创建生产环境配置（如果不存在）
   cp .env.example .env.production
   ```

2. **编辑配置文件**
   ```bash
   # 编辑本地开发配置
   nano .env
   
   # 编辑生产环境配置
   nano .env.production
   ```

### 重要配置项

#### 🔐 必须修改的安全配置
```bash
# 身份认证密钥（32字符以上）
NEXTAUTH_SECRET=your_nextauth_secret_32_chars_minimum_change_this
JWT_SECRET=your_jwt_secret_key_32_chars_minimum_change_this
ENCRYPTION_KEY=your_32_char_encryption_key_here_change_this

# 数据库密码
POSTGRES_PASSWORD=your_secure_postgres_password_here

# Redis密码
REDIS_PASSWORD=your_secure_redis_password_here

# Grafana管理员密码
GRAFANA_PASSWORD=your_secure_grafana_password_here
```

#### 🤖 AI服务配置 - 多API Key负载均衡

**⭐ 推荐配置多个MiniMax API Key以获得最佳性能！**

```bash
# MiniMax API 主密钥（备用）
MINIMAX_API_KEY=your_minimax_api_key_here

# 🚀 MiniMax 多密钥配置（推荐3-4个密钥）
MINIMAX_API_KEY_1=your_minimax_api_key_1_here
MINIMAX_API_KEY_2=your_minimax_api_key_2_here  
MINIMAX_API_KEY_3=your_minimax_api_key_3_here
MINIMAX_API_KEY_4=your_minimax_api_key_4_here

MINIMAX_API_URL=https://api.minimax.chat/v1/text/chatcompletion_v2

# DeepSeek API（备用LLM服务商）
DEEPSEEK_API_KEY=your_deepseek_api_key_here
DEEPSEEK_API_URL=https://api.deepseek.com/v1/chat/completions
```

**多API Key优势：**
- ✅ 避免单密钥速率限制
- ✅ 提高并发处理能力  
- ✅ 减少504超时错误
- ✅ 自动故障转移
- ✅ 智能负载均衡

#### 🌐 域名配置
```bash
# 本地开发
DOMAIN=localhost
NEXTAUTH_URL=http://localhost:3000
CORS_ORIGIN=http://localhost:3000

# 生产环境
DOMAIN=your-domain.com
NEXTAUTH_URL=https://your-domain.com
CORS_ORIGIN=https://your-domain.com
```

## 🔄 环境文件差异

### .env (本地开发/Docker)
- `NODE_ENV=production`
- `DOMAIN=localhost`
- `NEXTAUTH_URL=http://localhost:3000`
- 包含开发用的API密钥
- 使用本地数据库连接

### .env.production (生产环境)
- `NODE_ENV=production`
- `DOMAIN=your-domain.com`
- `NEXTAUTH_URL=https://your-domain.com`
- 使用生产级密码和密钥
- 使用生产数据库连接

## 🚀 部署命令对应关系

| 部署方式 | 使用的环境文件 | 命令 |
|---------|---------------|------|
| 本地开发 | `.env` | `npm run dev` |
| Docker本地 | `.env` | `docker-compose up -d` |
| 本地生产部署 | `.env.production` | `./scripts/deploy-production.sh` |
| 腾讯云部署 | `.env.production` | `chmod +x scripts/*.sh && ./scripts/deploy-tencent-cloud.sh` |

## ⚠️ 安全注意事项

1. **永远不要提交包含真实密钥的环境文件到Git**
   ```bash
   # .gitignore 已包含
   .env
   .env.production
   .env.local
   ```

2. **生产环境部署前必须修改所有默认密码**
   - 数据库密码
   - Redis密码
   - JWT密钥
   - 加密密钥
   - Grafana密码

3. **使用强密码**
   - 至少32个字符
   - 包含大小写字母、数字、特殊字符
   - 避免使用字典词汇

## 🔑 多API Key配置详解

### 智能题目生成优化策略

系统已内置**多API Key轮询机制**来解决504超时问题：

```bash
# 在 .env 文件中配置多个API密钥
MINIMAX_API_KEY_1=sk-xxxxxx1
MINIMAX_API_KEY_2=sk-xxxxxx2  
MINIMAX_API_KEY_3=sk-xxxxxx3
MINIMAX_API_KEY_4=sk-xxxxxx4
```

**工作原理：**
1. 🔍 系统自动检测可用API密钥数量
2. 🔄 智能题目生成时使用轮询分配密钥
3. 📊 4个维度 × 3批次 = 12个并发任务分散到不同API密钥
4. ⚡ 大幅降低单密钥负载，避免超时错误

**配置建议：**
- 💰 **预算充足**：配置4个API密钥（最佳性能）
- 💡 **成本控制**：配置2-3个API密钥（良好性能）
- 🚫 **单密钥**：容易出现504超时错误

**监控效果：**
```bash
# 查看日志验证多密钥生效
docker-compose logs -f app | grep "🔑 发现.*个有效API密钥"
```

## 🛠️ 故障排除

### 常见问题

1. **环境变量未生效**
   ```bash
   # 检查文件是否存在
   ls -la .env*
   
   # 检查文件内容
   cat .env
   ```

2. **Docker部署时环境变量错误**
   ```bash
   # 确保使用正确的环境文件
   docker-compose --env-file .env.production up -d
   ```

3. **生产部署时配置错误**
   ```bash
   # 检查生产环境配置
   cat .env.production
   
   # 验证必要的环境变量
   grep -E "(SECRET|PASSWORD|KEY)" .env.production
   ```

### 验证配置

```bash
# 检查环境文件语法
./scripts/quick-check.sh

# 测试Docker构建
./scripts/docker-build-safe.sh

# 验证健康检查
curl http://localhost/api/health
```

## 📞 支持

如果遇到环境配置问题：

1. 检查环境文件是否存在
2. 验证必要的配置项是否已填写
3. 确认密码和密钥格式正确
4. 查看应用日志排查具体错误

更多帮助请查看项目文档或联系技术支持。
