# OCTI智能评估系统 - 生产环境部署

## 🚀 快速开始

### 前置要求

- Docker >= 20.10
- Docker Compose >= 2.0
- 4GB+ 可用内存
- 10GB+ 可用磁盘空间

### 一键部署

```bash
# 克隆项目
git clone <repository-url>
cd octi_test

# 运行自动化部署脚本
./scripts/deploy-production.sh
```

## 📋 部署步骤

### 1. 环境准备

```bash
# 检查Docker版本
docker --version
docker-compose --version

# 确保Docker服务运行
sudo systemctl start docker  # Linux
# 或启动Docker Desktop    # macOS/Windows
```

### 2. 配置环境变量

```bash
# 复制环境变量模板
cp .env.example .env.production

# 编辑生产环境配置
vim .env.production
```

**关键配置项**:
```env
NODE_ENV=production
DATABASE_URL=********************************************/octi_db
REDIS_URL=redis://redis:6379
NEXTAUTH_SECRET=your-secret-key-here
MINIMAX_API_KEY=your-minimax-api-key
DEEPSEEK_API_KEY=your-deepseek-api-key
```

### 3. 部署服务

```bash
# 构建并启动生产环境
docker-compose -f docker-compose.pro.yml up -d --build

# 检查服务状态
docker-compose -f docker-compose.pro.yml ps
```

### 4. 验证部署

```bash
# 健康检查
curl http://localhost:3000/api/health

# 访问应用
open http://localhost:3000
```

## 🔧 管理命令

### 服务管理

```bash
# 查看服务状态
docker-compose -f docker-compose.pro.yml ps

# 查看实时日志
docker-compose -f docker-compose.pro.yml logs -f

# 重启服务
docker-compose -f docker-compose.pro.yml restart

# 停止服务
docker-compose -f docker-compose.pro.yml down

# 更新服务
docker-compose -f docker-compose.pro.yml up -d --build
```

### 数据库管理

```bash
# 运行数据库迁移
docker-compose -f docker-compose.pro.yml exec app npx prisma migrate deploy

# 查看数据库状态
docker-compose -f docker-compose.pro.yml exec postgres pg_isready

# 连接数据库
docker-compose -f docker-compose.pro.yml exec postgres psql -U postgres -d octi_db
```

### 备份和恢复

```bash
# 数据库备份
docker-compose -f docker-compose.pro.yml exec postgres pg_dump -U postgres octi_db > backup_$(date +%Y%m%d).sql

# 数据库恢复
docker-compose -f docker-compose.pro.yml exec -T postgres psql -U postgres octi_db < backup_file.sql

# Redis备份
docker run --rm -v octi_redis_pro_data:/data -v $(pwd):/backup alpine tar czf /backup/redis_backup.tar.gz -C /data .
```

## 📊 监控和调试

### 健康检查

```bash
# 应用健康状态
curl http://localhost:3000/api/health | jq

# 容器健康状态
docker inspect octi_app_pro --format='{{.State.Health.Status}}'
```

### 性能监控

```bash
# 查看资源使用
docker stats

# 查看磁盘使用
docker system df

# 查看网络
docker network ls
```

### 日志分析

```bash
# 应用日志
docker-compose -f docker-compose.pro.yml logs app

# 数据库日志
docker-compose -f docker-compose.pro.yml logs postgres

# 错误日志
docker-compose -f docker-compose.pro.yml logs app | grep ERROR
```

## 🔒 安全配置

### 网络安全

```bash
# 查看网络配置
docker network inspect octi_pro_network

# 检查端口暴露
docker-compose -f docker-compose.pro.yml ps
```

### 数据安全

- 定期备份数据库
- 更新环境变量中的密钥
- 监控访问日志
- 定期更新依赖包

## 🚨 故障排除

### 常见问题

**应用无法启动**
```bash
# 检查日志
docker-compose -f docker-compose.pro.yml logs app

# 检查端口占用
lsof -i :3000

# 重新构建
docker-compose -f docker-compose.pro.yml build --no-cache
```

**数据库连接失败**
```bash
# 检查数据库状态
docker-compose -f docker-compose.pro.yml exec postgres pg_isready

# 检查网络连接
docker-compose -f docker-compose.pro.yml exec app ping postgres
```

**内存不足**
```bash
# 检查内存使用
docker stats

# 清理未使用的镜像
docker system prune -a
```

### 性能优化

- 调整容器内存限制
- 优化数据库查询
- 配置Redis缓存
- 启用应用层缓存

## 📚 相关文档

- [详细部署指南](docs/deployment/production-deployment-guide.md)
- [部署总结](docs/deployment/deployment-summary.md)
- [API文档](docs/api/README.md)
- [开发指南](docs/development/README.md)

## 🆘 获取帮助

- **技术支持**: <EMAIL>
- **文档**: https://docs.octi.example.com
- **问题反馈**: https://github.com/octi/issues

---

**版本**: v4.0.0  
**更新时间**: 2025-08-14  
**维护团队**: OCTI开发团队
