# OCTI腾讯云部署指南 v2.0

## 🚀 快速开始

### 1. 上传代码到服务器
```bash
# 在本地打包代码
tar -czf octi.tar.gz --exclude=node_modules --exclude=.git .

# 上传到腾讯云服务器
scp octi.tar.gz octi@your-server-ip:/home/<USER>/

# 在服务器上解压
ssh octi@your-server-ip
cd /home/<USER>
tar -xzf octi.tar.gz
```

### 2. 选择部署模式

#### 🚀 简化版部署（推荐新手）
```bash
# 交互式选择（推荐）
./scripts/deploy-tencent-cloud-octi.sh

# 或直接指定简化版
./scripts/deploy-tencent-cloud-octi.sh --mode simple
```

#### 🏭 完整版部署（生产环境）
```bash
# 不带域名的完整版
./scripts/deploy-tencent-cloud-octi.sh --mode full

# 带域名和SSL的完整版
./scripts/deploy-tencent-cloud-octi.sh --mode full --domain your-domain.com --email <EMAIL>
```

## 📋 部署模式详解

### 🚀 简化版 (Simple Mode)

**包含服务：**
- Next.js应用 (端口3000)
- PostgreSQL数据库 (端口5432)
- Redis缓存 (端口6379)

**资源要求：**
- 内存：1GB+
- 磁盘：2GB+
- CPU：1核+

**访问方式：**
```
http://your-server-ip:3000
```

**适用场景：**
- 功能测试和验证
- 开发环境
- 小规模使用
- 学习和实验

### 🏭 完整版 (Full Mode)

**包含服务：**
- Next.js应用 (内部端口3000)
- PostgreSQL数据库 (端口5432)
- Redis缓存 (端口6379)
- Nginx反向代理 (端口80/443)
- Prometheus监控 (端口9090)
- Grafana仪表板 (端口3001)

**资源要求：**
- 内存：2GB+
- 磁盘：5GB+
- CPU：2核+

**访问方式：**
```
# 主应用
http://your-server-ip (通过Nginx)
https://your-domain.com (如果配置了域名)

# 监控系统
http://your-server-ip:9090 (Prometheus)
http://your-server-ip:3001 (Grafana)
```

**适用场景：**
- 生产环境部署
- 需要监控和日志
- 多用户访问
- 性能要求较高

## 🔧 部署后管理

### 查看服务状态
```bash
# 简化版
cd /opt/octi
docker-compose ps

# 完整版
cd /opt/octi
docker-compose -f docker-compose.full.yml ps
```

### 查看服务日志
```bash
# 查看所有服务日志
docker-compose logs

# 查看特定服务日志
docker-compose logs app
docker-compose logs postgres
docker-compose logs redis

# 完整版额外服务
docker-compose -f docker-compose.full.yml logs nginx
docker-compose -f docker-compose.full.yml logs prometheus
docker-compose -f docker-compose.full.yml logs grafana
```

### 重启服务
```bash
# 重启所有服务
docker-compose restart

# 重启特定服务
docker-compose restart app

# 完整版
docker-compose -f docker-compose.full.yml restart
```

### 停止和启动服务
```bash
# 停止服务
docker-compose down

# 启动服务
docker-compose up -d

# 完整版
docker-compose -f docker-compose.full.yml down
docker-compose -f docker-compose.full.yml up -d
```

## 🔍 健康检查

### 自动健康检查
部署脚本会自动执行健康检查，检查以下内容：
- 应用服务响应
- 数据库连接
- Redis连接
- Nginx状态（完整版）

### 手动健康检查
```bash
# 检查应用健康
curl http://localhost:3000/api/health

# 检查数据库
docker-compose exec postgres pg_isready -U postgres

# 检查Redis
docker-compose exec redis redis-cli ping

# 完整版：检查Nginx
curl http://localhost/health
```

## 🛠️ 故障排除

### 常见问题

#### 1. 端口被占用
```bash
# 检查端口占用
sudo netstat -tlnp | grep :3000
sudo netstat -tlnp | grep :5432
sudo netstat -tlnp | grep :6379

# 停止占用端口的服务
sudo kill -9 <PID>
```

#### 2. 内存不足
```bash
# 检查内存使用
free -h
docker stats

# 清理Docker资源
docker system prune -f
```

#### 3. 磁盘空间不足
```bash
# 检查磁盘使用
df -h

# 清理Docker镜像
docker image prune -f
```

#### 4. 权限问题
```bash
# 确保用户在docker组中
sudo usermod -aG docker octi
newgrp docker

# 检查目录权限
ls -la /opt/octi
```

### 重新部署
```bash
# 完全重新部署
cd /opt/octi
docker-compose down
docker system prune -f
./scripts/deploy-tencent-cloud-octi.sh --force-rebuild
```

## 📊 监控和维护

### 完整版监控功能

#### Prometheus监控
- 访问：`http://your-server-ip:9090`
- 监控应用性能、数据库状态、系统资源

#### Grafana仪表板
- 访问：`http://your-server-ip:3001`
- 默认账号：admin
- 默认密码：admin123（首次登录后修改）

### 日志管理
```bash
# 查看应用日志
docker-compose logs -f app

# 查看错误日志
docker-compose logs app | grep ERROR

# 导出日志
docker-compose logs app > app.log
```

### 数据备份
```bash
# 备份数据库
docker-compose exec postgres pg_dump -U postgres octi > backup.sql

# 备份Redis
docker-compose exec redis redis-cli --rdb backup.rdb

# 恢复数据库
docker-compose exec -T postgres psql -U postgres octi < backup.sql
```

## 🔐 安全配置

### 完整版安全特性
- Nginx反向代理隐藏内部端口
- SSL/TLS加密（配置域名时）
- 安全头配置
- 访问日志记录

### 建议的安全措施
1. 修改默认密码
2. 配置防火墙规则
3. 定期更新系统
4. 监控访问日志
5. 设置自动备份

## 📞 技术支持

### 获取帮助
```bash
# 查看脚本帮助
./scripts/deploy-tencent-cloud-octi.sh --help

# 查看系统状态
./scripts/docker-deploy.sh health prod
```

### 联系方式
- 项目文档：查看项目README.md
- 问题反馈：通过项目Issue系统
- 技术交流：项目讨论区
