# Docker部署脚本使用指南

## 📋 脚本概述

**文件位置：** `scripts/docker-deploy.sh`  
**版本：** v2.0  
**功能：** OCTI智能评估系统的完整Docker部署管理工具  

## 🚀 主要功能

### 1. 环境管理
```bash
# 启动开发环境
./scripts/docker-deploy.sh dev

# 启动生产环境  
./scripts/docker-deploy.sh prod

# 停止所有服务
./scripts/docker-deploy.sh stop

# 重启服务
./scripts/docker-deploy.sh restart
```

### 2. 构建和部署
```bash
# 构建镜像（开发环境）
./scripts/docker-deploy.sh build dev

# 构建镜像（生产环境）
./scripts/docker-deploy.sh build prod

# 清理资源
./scripts/docker-deploy.sh clean
```

### 3. 监控和诊断
```bash
# 查看服务状态
./scripts/docker-deploy.sh status

# 查看日志
./scripts/docker-deploy.sh logs

# 健康检查
./scripts/docker-deploy.sh health

# 修复常见问题
./scripts/docker-deploy.sh fix
```

### 4. 测试功能
```bash
# 测试方案C功能
./scripts/docker-deploy.sh test-planc

# 测试服务领域约束
./scripts/docker-deploy.sh test-constraint

# 测试UI修复效果
./scripts/docker-deploy.sh test-ui

# 测试专业版分析超时修复
./scripts/docker-deploy.sh test-timeout

# 测试前端超时修复
./scripts/docker-deploy.sh test-frontend

# 测试前端状态更新修复
./scripts/docker-deploy.sh test-state
```

### 5. 数据管理
```bash
# 备份数据
./scripts/docker-deploy.sh backup
```

## 🔧 解决缓存问题的最佳实践

### 清理Docker缓存后的部署流程

1. **完全清理构建**
```bash
# 使用--no-cache强制重新构建
./scripts/docker-deploy.sh build dev
```

2. **分步部署验证**
```bash
# 1. 构建镜像
./scripts/docker-deploy.sh build dev

# 2. 启动服务
./scripts/docker-deploy.sh dev

# 3. 健康检查
./scripts/docker-deploy.sh health

# 4. 功能测试
./scripts/docker-deploy.sh test-planc
```

3. **问题修复**
```bash
# 如果遇到问题，使用修复功能
./scripts/docker-deploy.sh fix
```

## 📊 脚本优势

### 1. 智能化管理
- ✅ **自动检测环境**：自动选择正确的docker-compose文件
- ✅ **健康检查**：全面的服务健康状态检查
- ✅ **错误处理**：友好的错误提示和修复建议

### 2. 完整的测试套件
- ✅ **功能测试**：方案C、约束测试、UI修复测试
- ✅ **性能测试**：超时修复、前端优化测试
- ✅ **集成测试**：端到端的功能验证

### 3. 运维友好
- ✅ **日志管理**：实时日志查看和分析
- ✅ **数据备份**：自动化数据备份功能
- ✅ **资源清理**：安全的资源清理机制

## 🎯 针对您的情况

### 清理缓存后的推荐流程

1. **确认Docker环境**
```bash
# 检查Docker是否正常
docker --version
docker-compose --version
```

2. **使用脚本重新构建**
```bash
# 强制重新构建（无缓存）
./scripts/docker-deploy.sh build dev
```

3. **启动服务**
```bash
./scripts/docker-deploy.sh dev
```

4. **验证部署**
```bash
# 健康检查
./scripts/docker-deploy.sh health

# TypeScript错误验证
bash scripts/simple-ts-scanner.sh

# 功能测试
./scripts/docker-deploy.sh test-planc
```

## 🔍 脚本特色功能

### 1. 智能健康检查
- 容器状态检查
- API健康检查
- 数据库连接验证
- Redis连接验证

### 2. 方案C功能测试
- SF维度问题生成测试
- 多维度差异化测试
- 独特性评分验证
- 增强组织画像检查

### 3. 约束测试
- 服务领域约束验证
- 中国本土化工具检查
- 关键词过滤测试

### 4. 修复验证
- UI修复效果测试
- 超时修复验证
- 前端状态更新测试

## 💡 使用建议

### 1. 开发环境
```bash
# 日常开发
./scripts/docker-deploy.sh dev
./scripts/docker-deploy.sh logs  # 查看实时日志
```

### 2. 生产部署
```bash
# 生产环境部署
./scripts/docker-deploy.sh build prod
./scripts/docker-deploy.sh prod
./scripts/docker-deploy.sh health
```

### 3. 问题排查
```bash
# 遇到问题时
./scripts/docker-deploy.sh status   # 查看状态
./scripts/docker-deploy.sh health   # 健康检查
./scripts/docker-deploy.sh fix      # 自动修复
```

### 4. 定期维护
```bash
# 定期清理
./scripts/docker-deploy.sh clean

# 数据备份
./scripts/docker-deploy.sh backup
```

## 🎉 总结

这个Docker部署脚本是一个非常完善的部署管理工具，具有：

- 🔧 **完整的生命周期管理**：构建、部署、监控、维护
- 🧪 **全面的测试套件**：功能、性能、集成测试
- 🛡️ **智能的错误处理**：自动检测和修复常见问题
- 📊 **详细的监控功能**：健康检查、日志分析、状态监控

**推荐使用这个脚本进行部署，它比手动Docker命令更安全、更智能、更全面！**

清理缓存后，直接使用：
```bash
./scripts/docker-deploy.sh build dev
./scripts/docker-deploy.sh dev
```

这样可以确保完全重新构建，解决所有缓存相关的问题。
