# OCTI智能评估系统 - 生产环境部署指南

## 概述

本文档提供OCTI智能评估系统v4.0的生产环境部署指南，包括Docker容器化部署、环境配置和运维管理。

## 系统架构

```mermaid
graph TB
    subgraph "生产环境"
        LB[负载均衡器]
        
        subgraph "应用层"
            APP1[OCTI App 1]
            APP2[OCTI App 2]
        end
        
        subgraph "数据层"
            PG[(PostgreSQL)]
            REDIS[(Redis)]
        end
        
        subgraph "外部服务"
            MINIMAX[MiniMax API]
            DEEPSEEK[DeepSeek API]
        end
    end
    
    LB --> APP1
    LB --> APP2
    APP1 --> PG
    APP1 --> REDIS
    APP1 --> MINIMAX
    APP1 --> DEEPSEEK
    APP2 --> PG
    APP2 --> REDIS
    APP2 --> MINIMAX
    APP2 --> DEEPSEEK
```

## 部署文件

### 1. 生产环境Docker Compose配置

**文件路径**: `docker-compose.pro.yml`

主要特性：
- 多阶段Docker构建优化
- 健康检查配置
- 资源限制和安全配置
- 数据持久化
- 网络隔离

### 2. 生产环境测试配置

**文件路径**: `docker-compose.pro-test.yml`

用于本地测试生产环境构建，使用不同端口避免冲突：
- 应用端口：3001
- PostgreSQL端口：5434
- Redis端口：6380

## 部署步骤

### 1. 环境准备

```bash
# 克隆项目
git clone <repository-url>
cd octi_test

# 安装Docker和Docker Compose
# 确保Docker版本 >= 20.10
# 确保Docker Compose版本 >= 2.0
```

### 2. 环境变量配置

创建生产环境配置文件：

```bash
# 复制环境变量模板
cp .env.example .env.production

# 编辑生产环境配置
vim .env.production
```

关键环境变量：
- `NODE_ENV=production`
- `DATABASE_URL`: PostgreSQL连接字符串
- `REDIS_URL`: Redis连接字符串
- `NEXTAUTH_SECRET`: 认证密钥
- `MINIMAX_API_KEY`: MiniMax API密钥
- `DEEPSEEK_API_KEY`: DeepSeek API密钥

### 3. 构建和部署

```bash
# 构建生产环境镜像
docker-compose -f docker-compose.pro.yml build

# 启动生产环境服务
docker-compose -f docker-compose.pro.yml up -d

# 检查服务状态
docker-compose -f docker-compose.pro.yml ps
```

### 4. 数据库初始化

```bash
# 运行数据库迁移
docker-compose -f docker-compose.pro.yml exec app npx prisma migrate deploy

# 生成Prisma客户端
docker-compose -f docker-compose.pro.yml exec app npx prisma generate
```

## 健康检查

系统提供多层健康检查：

### 1. 应用健康检查

```bash
curl http://localhost:3000/api/health
```

响应示例：
```json
{
  "status": "healthy",
  "timestamp": "2025-08-14T07:20:09.392Z",
  "version": "4.0.0",
  "services": {
    "database": {"status": "connected", "responseTime": 6},
    "redis": {"status": "connected", "responseTime": 7},
    "ai_services": {"minimax": "available", "deepseek": "available"}
  },
  "system": {
    "uptime": 20.270811092,
    "memory": {"used": 25277160, "total": 26234880, "percentage": 96},
    "node_version": "v20.19.4"
  },
  "response_time": 8
}
```

### 2. 容器健康检查

```bash
# 检查所有容器状态
docker ps --filter "name=octi"

# 查看健康检查日志
docker inspect octi_app_pro --format='{{.State.Health.Status}}'
```

## 监控和日志

### 1. 日志管理

```bash
# 查看应用日志
docker-compose -f docker-compose.pro.yml logs app

# 查看数据库日志
docker-compose -f docker-compose.pro.yml logs postgres

# 实时跟踪日志
docker-compose -f docker-compose.pro.yml logs -f
```

### 2. 性能监控

```bash
# 查看容器资源使用情况
docker stats

# 查看系统资源
docker system df
```

## 备份和恢复

### 1. 数据库备份

```bash
# 创建数据库备份
docker-compose -f docker-compose.pro.yml exec postgres pg_dump -U postgres octi_db > backup_$(date +%Y%m%d_%H%M%S).sql

# 恢复数据库
docker-compose -f docker-compose.pro.yml exec -T postgres psql -U postgres octi_db < backup_file.sql
```

### 2. Redis备份

```bash
# Redis数据自动持久化到数据卷
# 备份Redis数据卷
docker run --rm -v octi_redis_pro_data:/data -v $(pwd):/backup alpine tar czf /backup/redis_backup_$(date +%Y%m%d_%H%M%S).tar.gz -C /data .
```

## 安全配置

### 1. 网络安全

- 使用内部网络隔离服务
- 仅暴露必要端口
- 配置防火墙规则

### 2. 数据安全

- 数据库连接加密
- API密钥安全存储
- 定期更新依赖包

### 3. 访问控制

- 配置反向代理（Nginx/Apache）
- 启用HTTPS
- 设置访问限制

## 故障排除

### 1. 常见问题

**应用无法启动**
```bash
# 检查日志
docker-compose -f docker-compose.pro.yml logs app

# 检查环境变量
docker-compose -f docker-compose.pro.yml exec app env
```

**数据库连接失败**
```bash
# 检查数据库状态
docker-compose -f docker-compose.pro.yml exec postgres pg_isready

# 测试连接
docker-compose -f docker-compose.pro.yml exec app npx prisma db push
```

**内存不足**
```bash
# 检查内存使用
docker stats

# 调整内存限制
# 编辑docker-compose.pro.yml中的deploy.resources配置
```

### 2. 性能优化

- 调整容器资源限制
- 优化数据库查询
- 配置Redis缓存策略
- 启用应用层缓存

## 更新和维护

### 1. 应用更新

```bash
# 拉取最新代码
git pull origin main

# 重新构建镜像
docker-compose -f docker-compose.pro.yml build --no-cache

# 滚动更新
docker-compose -f docker-compose.pro.yml up -d
```

### 2. 数据库迁移

```bash
# 运行新的迁移
docker-compose -f docker-compose.pro.yml exec app npx prisma migrate deploy

# 检查迁移状态
docker-compose -f docker-compose.pro.yml exec app npx prisma migrate status
```

## 联系支持

如需技术支持，请联系：
- 邮箱：<EMAIL>
- 文档：https://docs.octi.example.com
- 问题反馈：https://github.com/octi/issues

---

*最后更新：2025-08-14*
*版本：v4.0.0*
