# OCTI智能评估系统 - 生产环境部署指南

## 📋 目录

- [系统要求](#系统要求)
- [部署前准备](#部署前准备)
- [快速部署](#快速部署)
- [手动部署](#手动部署)
- [配置说明](#配置说明)
- [监控与维护](#监控与维护)
- [故障排查](#故障排查)
- [安全加固](#安全加固)
- [备份与恢复](#备份与恢复)
- [性能优化](#性能优化)

## 🖥️ 系统要求

### 硬件要求

| 组件 | 最低配置  | 推荐配置   |
| ---- | --------- | ---------- |
| CPU  | 4核心     | 8核心+     |
| 内存 | 8GB       | 16GB+      |
| 存储 | 100GB SSD | 500GB+ SSD |
| 网络 | 100Mbps   | 1Gbps+     |

### 软件要求

- **操作系统**: Ubuntu 20.04+ / CentOS 8+ / RHEL 8+
- **Docker**: 20.10+
- **Docker Compose**: 2.0+
- **Git**: 2.25+
- **Curl**: 7.68+
- **JQ**: 1.6+

### 端口要求

| 端口 | 服务        | 访问范围 |
| ---- | ----------- | -------- |
| 80   | Nginx HTTP  | 公网     |
| 443  | Nginx HTTPS | 公网     |
| 3000 | Next.js应用 | 内网     |
| 5432 | PostgreSQL  | 本地     |
| 6379 | Redis       | 本地     |
| 9090 | Prometheus  | 本地     |
| 3001 | Grafana     | 本地     |

## 🚀 部署前准备

### 1. 服务器初始化

```bash
# 更新系统
sudo apt update && sudo apt upgrade -y

# 安装基础工具
sudo apt install -y curl wget git jq htop vim ufw

# 配置防火墙
sudo ufw allow ssh
sudo ufw allow 80
sudo ufw allow 443
sudo ufw --force enable
```

### 2. 安装Docker

```bash
# 安装Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh

# 添加用户到docker组
sudo usermod -aG docker $USER

# 安装Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# 重新登录以应用组权限
newgrp docker
```

### 3. 克隆项目

```bash
# 克隆项目到服务器
git clone https://github.com/AlataChen/octi-test.git /opt/octi
cd /opt/octi

# 设置权限
sudo chown -R $USER:$USER /opt/octi
chmod +x scripts/*.sh
```

### 4. 配置环境变量

```bash
# 复制生产环境配置模板
cp .env.production .env.production.local

# 编辑配置文件
vim .env.production.local
```

**重要配置项**:

```bash
# 数据库密码 (必须修改)
POSTGRES_PASSWORD="your_secure_postgres_password_here"

# Redis密码 (必须修改)
REDIS_PASSWORD="your_secure_redis_password_here"

# 应用密钥 (必须修改)
NEXTAUTH_SECRET="your_nextauth_secret_32_chars_min"
JWT_SECRET="your_jwt_secret_32_chars_minimum"
ENCRYPTION_KEY="your_encryption_key_32_chars_min"

# 域名配置
NEXTAUTH_URL="https://your-domain.com"
CORS_ORIGIN="https://your-domain.com"

# AI服务密钥
MINIMAX_API_KEY_1="your_minimax_api_key_1"
DEEPSEEK_API_KEY="your_deepseek_api_key"

# 监控密码
GRAFANA_ADMIN_PASSWORD="your_secure_grafana_password"
```

## ⚡ 快速部署

### 一键部署脚本

```bash
# 进入项目目录
cd /opt/octi

# 运行部署脚本
./scripts/deploy-production.sh
```

### 部署选项

```bash
# 跳过数据备份
./scripts/deploy-production.sh --skip-backup

# 跳过镜像构建
./scripts/deploy-production.sh --skip-build

# 查看帮助
./scripts/deploy-production.sh --help
```

## 🔧 手动部署

### 1. 创建系统目录

```bash
# 创建数据目录
sudo mkdir -p /opt/octi/data/{postgres,redis,prometheus,grafana}
sudo mkdir -p /opt/octi/{backups,logs,ssl}

# 设置权限
sudo chown -R $USER:$USER /opt/octi
```

### 2. 构建和启动服务

```bash
# 拉取基础镜像
docker-compose -f docker-compose.pro.yml pull

# 构建应用镜像
docker-compose -f docker-compose.pro.yml build

# 启动所有服务
docker-compose -f docker-compose.pro.yml --env-file .env.production.local up -d
```

### 3. 运行数据库迁移

```bash
# 等待数据库启动
sleep 30

# 运行迁移
docker-compose -f docker-compose.pro.yml exec app npx prisma migrate deploy

# 生成Prisma客户端
docker-compose -f docker-compose.pro.yml exec app npx prisma generate
```

### 4. 验证部署

```bash
# 检查服务状态
docker-compose -f docker-compose.pro.yml ps

# 检查服务日志
docker-compose -f docker-compose.pro.yml logs

# 测试应用响应
curl -f http://localhost:3000/api/health
```

## ⚙️ 配置说明

### Docker Compose配置

<mcfile name="docker-compose.pro.yml" path="/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/docker-compose.pro.yml"></mcfile>
包含以下服务:

- **app**: Next.js应用服务
- **postgres**: PostgreSQL数据库
- **redis**: Redis缓存
- **nginx**: 反向代理服务器
- **prometheus**: 监控数据收集
- **grafana**: 监控数据可视化
- **node_exporter**: 系统指标收集

### 环境变量配置

<mcfile name=".env.production" path="/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/.env.production"></mcfile>
包含所有必要的环境变量配置。

### Nginx配置

```nginx
# /opt/octi/docker/nginx/nginx.conf
server {
    listen 80;
    server_name your-domain.com;

    # 重定向到HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com;

    # SSL配置
    ssl_certificate /etc/nginx/ssl/cert.pem;
    ssl_certificate_key /etc/nginx/ssl/key.pem;

    # 反向代理到应用
    location / {
        proxy_pass http://app:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

## 📊 监控与维护

### Grafana仪表板

访问 `http://localhost:3001` 查看监控仪表板:

- **系统监控**: CPU、内存、磁盘、网络
- **应用监控**: 响应时间、错误率、吞吐量
- **数据库监控**: 连接数、查询性能、锁等待
- **缓存监控**: Redis内存使用、命中率

### 日志管理

```bash
# 查看应用日志
docker-compose -f docker-compose.pro.yml logs app

# 查看数据库日志
docker-compose -f docker-compose.pro.yml logs postgres

# 查看实时日志
docker-compose -f docker-compose.pro.yml logs -f

# 查看系统日志
tail -f /var/log/octi/deploy.log
```

### 健康检查

```bash
# 检查所有服务状态
docker-compose -f docker-compose.pro.yml ps

# 检查应用健康状态
curl http://localhost:3000/api/health

# 检查数据库连接
docker-compose -f docker-compose.pro.yml exec postgres pg_isready -U octi_user

# 检查Redis连接
docker-compose -f docker-compose.pro.yml exec redis redis-cli ping
```

## 🔍 故障排查

### 常见问题

#### 1. 服务启动失败

```bash
# 查看详细错误信息
docker-compose -f docker-compose.pro.yml logs [service_name]

# 检查资源使用情况
docker stats

# 检查磁盘空间
df -h
```

#### 2. 数据库连接失败

```bash
# 检查数据库状态
docker-compose -f docker-compose.pro.yml exec postgres pg_isready

# 检查数据库日志
docker-compose -f docker-compose.pro.yml logs postgres

# 手动连接测试
docker-compose -f docker-compose.pro.yml exec postgres psql -U octi_user -d octi_production
```

#### 3. 应用响应慢

```bash
# 检查应用性能指标
curl http://localhost:3000/api/metrics

# 检查数据库性能
docker-compose -f docker-compose.pro.yml exec postgres psql -U octi_user -d octi_production -c "SELECT * FROM pg_stat_activity;"

# 检查Redis性能
docker-compose -f docker-compose.pro.yml exec redis redis-cli info stats
```

### 紧急恢复

```bash
# 快速重启所有服务
docker-compose -f docker-compose.pro.yml restart

# 重新部署应用
docker-compose -f docker-compose.pro.yml up -d --force-recreate app

# 从备份恢复数据库
docker-compose -f docker-compose.pro.yml exec -T postgres psql -U octi_user -d octi_production < backup.sql
```

## 🔒 安全加固

### 1. 防火墙配置

```bash
# 配置UFW防火墙
sudo ufw default deny incoming
sudo ufw default allow outgoing
sudo ufw allow ssh
sudo ufw allow 80
sudo ufw allow 443
sudo ufw enable
```

### 2. SSL证书配置

```bash
# 使用Let's Encrypt获取免费SSL证书
sudo apt install certbot
sudo certbot certonly --standalone -d your-domain.com

# 复制证书到项目目录
sudo cp /etc/letsencrypt/live/your-domain.com/fullchain.pem /opt/octi/docker/nginx/ssl/cert.pem
sudo cp /etc/letsencrypt/live/your-domain.com/privkey.pem /opt/octi/docker/nginx/ssl/key.pem
```

### 3. 定期安全更新

```bash
# 创建自动更新脚本
cat > /opt/octi/scripts/security-update.sh << 'EOF'
#!/bin/bash
# 更新系统包
sudo apt update && sudo apt upgrade -y

# 更新Docker镜像
cd /opt/octi
docker-compose -f docker-compose.pro.yml pull
docker-compose -f docker-compose.pro.yml up -d

# 清理未使用的镜像
docker image prune -f
EOF

chmod +x /opt/octi/scripts/security-update.sh

# 添加到crontab (每周日凌晨2点执行)
echo "0 2 * * 0 /opt/octi/scripts/security-update.sh" | crontab -
```

## 💾 备份与恢复

### 自动备份配置

```bash
# 创建备份脚本
cat > /opt/octi/scripts/backup.sh << 'EOF'
#!/bin/bash
BACKUP_DIR="/opt/octi/backups"
DATE=$(date +"%Y%m%d_%H%M%S")

# 创建备份目录
mkdir -p "$BACKUP_DIR/$DATE"

# 备份数据库
docker-compose -f /opt/octi/docker-compose.pro.yml exec -T postgres pg_dumpall -U octi_user > "$BACKUP_DIR/$DATE/postgres.sql"

# 备份Redis
docker-compose -f /opt/octi/docker-compose.pro.yml exec -T redis redis-cli --rdb - > "$BACKUP_DIR/$DATE/redis.rdb"

# 备份配置文件
cp -r /opt/octi/configs "$BACKUP_DIR/$DATE/"
cp /opt/octi/.env.production.local "$BACKUP_DIR/$DATE/"

# 压缩备份
tar -czf "$BACKUP_DIR/backup_$DATE.tar.gz" -C "$BACKUP_DIR" "$DATE"
rm -rf "$BACKUP_DIR/$DATE"

# 删除30天前的备份
find "$BACKUP_DIR" -name "backup_*.tar.gz" -mtime +30 -delete
EOF

chmod +x /opt/octi/scripts/backup.sh

# 添加到crontab (每天凌晨2点执行)
echo "0 2 * * * /opt/octi/scripts/backup.sh" | crontab -
```

### 数据恢复

```bash
# 恢复数据库
tar -xzf backup_20250127_020000.tar.gz
docker-compose -f docker-compose.pro.yml exec -T postgres psql -U octi_user -d octi_production < backup_20250127_020000/postgres.sql

# 恢复Redis
docker-compose -f docker-compose.pro.yml stop redis
cp backup_20250127_020000/redis.rdb /opt/octi/data/redis/dump.rdb
docker-compose -f docker-compose.pro.yml start redis

# 恢复配置文件
cp -r backup_20250127_020000/configs /opt/octi/
cp backup_20250127_020000/.env.production.local /opt/octi/
```

## ⚡ 性能优化

### 1. 数据库优化

```sql
-- 创建索引
CREATE INDEX CONCURRENTLY idx_assessments_user_id ON assessments(user_id);
CREATE INDEX CONCURRENTLY idx_assessments_created_at ON assessments(created_at);
CREATE INDEX CONCURRENTLY idx_users_email ON users(email);

-- 分析表统计信息
ANALYZE;

-- 清理无用数据
VACUUM ANALYZE;
```

### 2. Redis优化

```bash
# 优化Redis配置
echo "maxmemory-policy allkeys-lru" >> /opt/octi/docker/redis/redis.conf
echo "tcp-keepalive 60" >> /opt/octi/docker/redis/redis.conf
echo "timeout 300" >> /opt/octi/docker/redis/redis.conf
```

### 3. 应用优化

```bash
# 启用应用缓存
export ENABLE_CACHE=true
export CACHE_TTL=3600

# 优化Node.js性能
export NODE_OPTIONS="--max-old-space-size=2048"
export UV_THREADPOOL_SIZE=16
```

### 4. 系统优化

```bash
# 优化系统参数
echo "vm.swappiness=10" >> /etc/sysctl.conf
echo "net.core.somaxconn=65535" >> /etc/sysctl.conf
echo "net.ipv4.tcp_max_syn_backlog=65535" >> /etc/sysctl.conf
sysctl -p

# 优化文件描述符限制
echo "* soft nofile 65535" >> /etc/security/limits.conf
echo "* hard nofile 65535" >> /etc/security/limits.conf
```

## 📞 技术支持

如果在部署过程中遇到问题，请:

1. 查看部署日志: `/var/log/octi/deploy.log`
2. 检查服务状态: `docker-compose -f docker-compose.pro.yml ps`
3. 查看服务日志: `docker-compose -f docker-compose.pro.yml logs`
4. 联系技术支持团队

---

**部署完成后，请及时修改所有默认密码，并定期进行安全更新！**
