# OCTI腾讯云部署脚本测试指南

## 🧪 测试部署模式选择功能

### 1. 帮助信息测试
```bash
./scripts/deploy-tencent-cloud-octi.sh --help
```
**预期结果：** 显示完整的帮助信息，包括两种部署模式的说明

### 2. 简化版部署测试
```bash
./scripts/deploy-tencent-cloud-octi.sh --mode simple
```
**预期结果：** 
- 直接选择简化版模式
- 使用 `docker-compose.yml` 文件
- 部署3个服务：app、postgres、redis

### 3. 完整版部署测试
```bash
./scripts/deploy-tencent-cloud-octi.sh --mode full
```
**预期结果：**
- 直接选择完整版模式
- 创建 `docker-compose.full.yml` 文件
- 创建 Nginx 和 Prometheus 配置
- 部署6个服务：app、postgres、redis、nginx、prometheus、grafana

### 4. 交互式选择测试
```bash
./scripts/deploy-tencent-cloud-octi.sh
```
**预期结果：**
- 显示部署模式选择菜单
- 用户可以选择 1（简化版）或 2（完整版）
- 根据选择执行对应的部署流程

### 5. 带域名的完整版部署测试
```bash
./scripts/deploy-tencent-cloud-octi.sh --mode full --domain example.com --email <EMAIL>
```
**预期结果：**
- 选择完整版模式
- 创建HTTPS配置的Nginx
- 配置SSL证书相关设置

## 📋 部署模式对比

| 特性 | 简化版 (simple) | 完整版 (full) |
|------|----------------|---------------|
| **服务数量** | 3个 | 6个 |
| **内存需求** | ~1GB | ~2GB |
| **磁盘需求** | ~2GB | ~5GB |
| **启动时间** | ~30秒 | ~60秒 |
| **配置文件** | docker-compose.yml | docker-compose.full.yml |
| **反向代理** | ❌ | ✅ Nginx |
| **监控系统** | ❌ | ✅ Prometheus + Grafana |
| **SSL支持** | ❌ | ✅ 自动配置 |
| **负载均衡** | ❌ | ✅ Nginx |
| **适用场景** | 开发/测试 | 生产环境 |

## 🔧 配置文件说明

### 简化版配置
- 使用现有的 `docker-compose.yml`
- 包含基础的3个服务
- 直接暴露应用端口3000

### 完整版配置
- 自动生成 `docker-compose.full.yml`
- 包含完整的6个服务
- 通过Nginx反向代理访问
- 包含监控和日志系统

## 🚀 部署流程

### 简化版流程
1. 检查系统环境
2. 选择简化版模式
3. 使用现有配置文件
4. 构建和启动服务
5. 健康检查

### 完整版流程
1. 检查系统环境
2. 选择完整版模式
3. 创建完整版配置文件
4. 创建Nginx配置
5. 创建Prometheus配置
6. 构建和启动服务
7. 健康检查

## 📊 服务端口映射

### 简化版端口
- 应用: 3000
- 数据库: 5432
- 缓存: 6379

### 完整版端口
- Nginx HTTP: 80
- Nginx HTTPS: 443 (如果配置域名)
- 应用: 3000 (内部)
- 数据库: 5432
- 缓存: 6379
- Prometheus: 9090
- Grafana: 3001

## 🎯 使用建议

### 选择简化版的情况
- 功能测试和开发
- 资源有限的服务器
- 快速原型验证
- 学习和实验

### 选择完整版的情况
- 生产环境部署
- 需要监控和日志
- 多用户访问
- 性能要求较高
- 需要SSL证书

## 🔍 故障排除

### 常见问题
1. **权限不足**: 确保用户在docker组中
2. **端口冲突**: 检查端口是否被占用
3. **内存不足**: 完整版需要至少2GB内存
4. **磁盘空间**: 确保有足够的磁盘空间

### 调试命令
```bash
# 检查服务状态
docker-compose ps

# 查看服务日志
docker-compose logs [service_name]

# 重启服务
docker-compose restart

# 完全重新部署
docker-compose down && docker-compose up -d
```
