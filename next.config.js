/** @type {import('next').NextConfig} */
const nextConfig = {
  // 启用实验性功能
  experimental: {
    // 启用服务器组件
    serverComponentsExternalPackages: ['prisma', '@prisma/client'],
    // 确保自定义模块被正确追踪到standalone构建中
    outputFileTracingIncludes: {
      '/': ['./src/**/*'],
    },
  },

  // TypeScript配置
  typescript: {
    // 在生产构建时忽略TypeScript错误（仅在开发阶段使用）
    ignoreBuildErrors: false,
  },

  // ESLint配置
  eslint: {
    // 在生产构建时忽略ESLint错误（仅在开发阶段使用）
    ignoreDuringBuilds: false,
  },

  // 图片优化配置
  images: {
    // 允许的图片域名
    domains: [
      'localhost',
      'octi.example.com',
      // 添加其他需要的图片域名
    ],
    // 图片格式
    formats: ['image/webp', 'image/avif'],
    // 图片尺寸
    deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],
    imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],
  },

  // 环境变量配置
  env: {
    CUSTOM_KEY: process.env.CUSTOM_KEY,
  },

  // 重定向配置
  async redirects() {
    return [
      // 示例重定向
      // {
      //   source: '/old-path',
      //   destination: '/new-path',
      //   permanent: true,
      // },
    ];
  },

  // 重写配置
  async rewrites() {
    return [
      // API代理重写（用于LLM API安全代理）
      {
        source: '/api/proxy/:path*',
        destination: '/api/internal/proxy/:path*',
      },
    ];
  },

  // 头部配置
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          // 安全头部
          {
            key: 'X-Frame-Options',
            value: 'DENY',
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          {
            key: 'Referrer-Policy',
            value: 'strict-origin-when-cross-origin',
          },
          {
            key: 'Permissions-Policy',
            value: 'camera=(), microphone=(), geolocation=()',
          },
        ],
      },
      {
        source: '/manifest.json',
        headers: [
          {
            key: 'Content-Type',
            value: 'application/manifest+json',
          },
        ],
      },
      {
        source: '/api/(.*)',
        headers: [
          // API安全头部
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          {
            key: 'X-Frame-Options',
            value: 'DENY',
          },
          {
            key: 'X-XSS-Protection',
            value: '1; mode=block',
          },
        ],
      },
    ];
  },

  // 压缩配置
  compress: true,

  // 电源效率配置
  poweredByHeader: false,

  // 生成配置
  generateEtags: true,

  // 页面扩展名
  pageExtensions: ['ts', 'tsx', 'js', 'jsx', 'md', 'mdx'],

  // 输出配置
  output: 'standalone',

  // Webpack配置
  webpack: (config, { buildId, dev, isServer, defaultLoaders, webpack }) => {
    // 自定义webpack配置

    // 添加别名
    config.resolve.alias = {
      ...config.resolve.alias,
      '@': require('path').resolve(__dirname, 'src'),
    };

    // 优化配置
    if (!dev && !isServer) {
      // 生产环境优化
      config.optimization = {
        ...config.optimization,
        splitChunks: {
          chunks: 'all',
          cacheGroups: {
            vendor: {
              test: /[\\/]node_modules[\\/]/,
              name: 'vendors',
              chunks: 'all',
            },
          },
        },
      };
    }

    // 处理特定模块
    config.module.rules.push({
      test: /\.svg$/,
      use: ['@svgr/webpack'],
    });

    // 忽略外部脚本错误（如MetaMask等浏览器扩展）
    if (!isServer) {
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
        net: false,
        tls: false,
        crypto: false,
      };
    }

    return config;
  },

  // 构建配置
  distDir: '.next',
  cleanDistDir: true,

  // 开发服务器配置
  devIndicators: {
    buildActivity: true,
    buildActivityPosition: 'bottom-right',
  },

  // 国际化配置（如果需要）
  // i18n: {
  //   locales: ['zh-CN', 'en'],
  //   defaultLocale: 'zh-CN',
  // },

  // 分析配置（用于bundle分析）
  ...(process.env.ANALYZE === 'true' && {
    webpack: (config, { isServer }) => {
      if (!isServer) {
        const { BundleAnalyzerPlugin } = require('webpack-bundle-analyzer');
        config.plugins.push(
          new BundleAnalyzerPlugin({
            analyzerMode: 'static',
            openAnalyzer: false,
            reportFilename: '../bundle-analyzer-report.html',
          })
        );
      }
      return config;
    },
  }),
};

module.exports = nextConfig;
