# OCTI 部署脚本说明

本目录包含OCTI智能评估系统的核心部署脚本，已清理并精简为必要的脚本。

## 📁 核心脚本文件

### 1. `docker-deploy.sh` - 统一Docker部署管理脚本 ⭐
**用途**: 统一的Docker环境管理和部署
**功能**:
- 支持开发和生产环境
- 服务状态管理
- 健康检查和日志查看
- 资源清理和构建

**使用方法**:
```bash
# 启动生产环境
./scripts/docker-deploy.sh prod

# 启动开发环境
./scripts/docker-deploy.sh dev

# 查看服务状态
./scripts/docker-deploy.sh status

# 查看日志
./scripts/docker-deploy.sh logs

# 健康检查
./scripts/docker-deploy.sh health

# 查看帮助
./scripts/docker-deploy.sh help
```

### 2. `check-env.sh` - 环境变量检查脚本
**用途**: 检查环境变量配置完整性
**功能**:
- 验证必要的环境变量
- 检查默认值和安全配置
- 支持开发和生产环境检查

**使用方法**:
```bash
# 检查所有环境文件
./scripts/check-env.sh

# 只检查生产环境
./scripts/check-env.sh production

# 只检查开发环境
./scripts/check-env.sh dev
```

### 3. `docker-build-safe.sh` - 安全Docker构建脚本
**用途**: 安全的Docker镜像构建
**功能**:
- 检查Docker环境
- 清理旧Docker资源
- 构建新的Docker镜像
- 构建验证

**使用方法**:
```bash
# 标准构建
./scripts/docker-build-safe.sh

# 跳过验证直接构建
./scripts/docker-build-safe.sh --skip-check
```

### 4. `deploy-production.sh` - 生产环境部署脚本
**用途**: 本地生产环境完整部署
**功能**:
- 环境检查和验证
- 构建生产镜像
- 部署所有服务
- 健康检查和验证

### 5. `deploy-tencent-cloud.sh` - 腾讯云部署脚本
**用途**: 腾讯云服务器一键部署
**功能**:
- 系统环境自动配置
- Docker环境安装
- 防火墙配置
- SSL证书配置（可选）

### 6. `deploy-tencent-cloud-octi.sh` - octi用户专用部署脚本 v2.0 ⭐
**用途**: 为octi服务账户设计的智能部署脚本
**功能**:
- 🚀 **部署模式选择** - 简化版或完整生产版
- 🔧 无需sudo权限运行
- 🎯 智能环境检查和配置
- 📊 完整的健康检查和监控

**部署模式：**

**简化版 (simple)** - 推荐用于测试和小规模使用
- 包含：Next.js应用 + PostgreSQL + Redis
- 资源需求：~1GB内存，~2GB磁盘
- 启动时间：~30秒
- 适用场景：功能测试、开发环境、小规模使用

**完整版 (full)** - 适用于生产环境
- 包含：简化版 + Nginx + Prometheus + Grafana
- 资源需求：~2GB内存，~5GB磁盘
- 启动时间：~60秒
- 适用场景：生产环境、性能监控、大规模使用

**使用方法：**
```bash
# 交互式选择部署模式
./scripts/deploy-tencent-cloud-octi.sh

# 直接部署简化版
./scripts/deploy-tencent-cloud-octi.sh --mode simple

# 部署完整版并配置域名
./scripts/deploy-tencent-cloud-octi.sh --mode full --domain your-domain.com

# 查看所有选项
./scripts/deploy-tencent-cloud-octi.sh --help
```

## 📊 部署模式对比

| 特性 | 简化版 (simple) | 完整版 (full) |
|------|----------------|---------------|
| **服务数量** | 3个 | 6个 |
| **内存需求** | ~1GB | ~2GB |
| **磁盘需求** | ~2GB | ~5GB |
| **启动时间** | ~30秒 | ~60秒 |
| **反向代理** | ❌ | ✅ Nginx |
| **监控系统** | ❌ | ✅ Prometheus + Grafana |
| **SSL支持** | ❌ | ✅ 自动配置 |
| **适用场景** | 开发/测试 | 生产环境 |

### 服务端口映射

**简化版端口：**
- 应用: 3000
- 数据库: 5432
- 缓存: 6379

**完整版端口：**
- Nginx HTTP: 80
- Nginx HTTPS: 443 (配置域名时)
- Prometheus: 9090
- Grafana: 3001
- 其他服务通过Nginx代理访问

## 🚀 腾讯云部署指南

### 方案选择

#### 方案一：octi用户部署（推荐，更安全）
适用于生产环境，使用专用服务账户，无需sudo权限

#### 方案二：root用户部署
适用于测试环境，需要root权限

### 前置条件
1. 腾讯云CVM实例（Ubuntu 22.04）
2. 已配置SSH密钥登录
3. 实例具有公网IP
4. 如需SSL，请确保域名已解析到实例IP

## 🔐 方案一：octi用户部署（推荐）

### 步骤1：系统管理员设置环境

```bash
# 1. 以root用户连接到服务器
ssh root@your-server-ip

# 2. 上传项目代码
scp -r /path/to/octi-project/* root@your-server-ip:/tmp/octi/

# 3. 在服务器上执行设置脚本
cd /tmp/octi
chmod +x scripts/*.sh
sudo ./scripts/setup-octi-user.sh
```

### 步骤2：使用octi用户部署
```bash
# 1. 切换到octi用户
su - octi

# 2. 上传项目代码到octi用户目录
# 方式一：从临时目录复制
cp -r /tmp/octi/* /opt/octi/

# 方式二：直接上传到octi用户（推荐）
# 在本地执行：scp -r /path/to/octi-project/* octi@your-server-ip:/opt/octi/

# 3. 进入项目目录并部署
cd /opt/octi
chmod +x scripts/*.sh
./scripts/deploy-tencent-cloud-octi.sh

# 或者带域名的部署
./scripts/deploy-tencent-cloud-octi.sh --domain your-domain.com --email <EMAIL>
```

## 🔧 方案二：root用户部署

#### 1. 连接到腾讯云服务器
```bash
ssh root@your-server-ip
```

#### 2. 上传项目代码
选择以下方式之一：

**方式一：使用scp上传**
```bash
# 在本地执行
scp -r /path/to/octi-project/* root@your-server-ip:/tmp/octi/
```

**方式二：使用git克隆**
```bash
# 在服务器执行
git clone https://github.com/your-username/octi-project.git /tmp/octi
```

**方式三：使用rsync同步**
```bash
# 在本地执行
rsync -avz /path/to/octi-project/ root@your-server-ip:/tmp/octi/
```

#### 3. 添加执行权限并执行部署脚本
```bash
# 进入项目目录
cd /tmp/octi

# 添加脚本执行权限（重要！）
chmod +x scripts/*.sh

# 执行腾讯云部署脚本
./scripts/deploy-tencent-cloud.sh

# 或者带域名的部署
./scripts/deploy-tencent-cloud.sh --domain your-domain.com --email <EMAIL>
```

#### 4. 配置环境变量
脚本会提示您编辑环境变量文件：
```bash
# 编辑生产环境配置
nano /opt/octi/.env.production
```

重要配置项：
- `DATABASE_URL`: PostgreSQL连接字符串
- `REDIS_URL`: Redis连接字符串
- `NEXTAUTH_SECRET`: NextAuth密钥
- `JWT_SECRET`: JWT密钥
- `ENCRYPTION_KEY`: 加密密钥
- `MINIMAX_API_KEY`: MiniMax API密钥
- `DEEPSEEK_API_KEY`: DeepSeek API密钥

#### 5. 完成部署
脚本会自动：
- 安装所有依赖
- 配置防火墙
- 构建并启动服务
- 配置SSL证书（如提供域名）
- 执行健康检查

### 部署后管理

#### 查看服务状态
```bash
cd /opt/octi
docker-compose ps
```

#### 查看服务日志
```bash
cd /opt/octi
docker-compose logs [service-name]
```

#### 重启服务
```bash
cd /opt/octi
docker-compose restart [service-name]
```

#### 停止服务
```bash
cd /opt/octi
docker-compose down
```

#### 更新应用
```bash
cd /opt/octi
git pull  # 如果使用git
docker-compose build --no-cache
docker-compose up -d
```

## 🔧 故障排除

### 常见问题

1. **Docker权限问题**
   ```bash
   sudo usermod -aG docker $USER
   newgrp docker
   ```

2. **端口被占用**
   ```bash
   sudo netstat -tulpn | grep :80
   sudo kill -9 <PID>
   ```

3. **SSL证书申请失败**
   - 确保域名已正确解析
   - 检查防火墙是否开放80/443端口
   - 确保Nginx未占用80端口

4. **服务启动失败**
   ```bash
   cd /opt/octi
   docker-compose logs
   ```

### 日志位置
- 应用日志: `/opt/octi/logs/`
- Docker日志: `docker-compose logs`
- 系统日志: `/var/log/syslog`

## 📞 支持

如遇到问题，请检查：
1. 系统是否为Ubuntu 22.04
2. 网络连接是否正常
3. 域名解析是否正确
4. 环境变量是否配置完整

更多帮助请查看项目文档或联系技术支持。
