#!/bin/bash

# OCTI 环境变量检查脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# 检查环境文件
check_env_file() {
    local env_file=$1
    local env_name=$2
    
    log_info "检查 $env_name 环境文件: $env_file"
    
    if [[ ! -f "$env_file" ]]; then
        log_error "环境文件不存在: $env_file"
        return 1
    fi
    
    # 检查必要的配置项
    local required_vars=(
        "NODE_ENV"
        "DATABASE_URL"
        "REDIS_URL"
        "NEXTAUTH_SECRET"
        "JWT_SECRET"
        "ENCRYPTION_KEY"
        "MINIMAX_API_KEY"
        "DEEPSEEK_API_KEY"
    )
    
    local missing_vars=()
    local default_vars=()
    
    for var in "${required_vars[@]}"; do
        if ! grep -q "^$var=" "$env_file"; then
            missing_vars+=("$var")
        else
            local value=$(grep "^$var=" "$env_file" | cut -d'=' -f2- | tr -d '"')
            if [[ "$value" == *"your_"* ]] || [[ "$value" == *"change_this"* ]] || [[ "$value" == *"here"* ]]; then
                default_vars+=("$var")
            fi
        fi
    done
    
    if [[ ${#missing_vars[@]} -gt 0 ]]; then
        log_error "缺少必要的环境变量: ${missing_vars[*]}"
        return 1
    fi
    
    if [[ ${#default_vars[@]} -gt 0 ]]; then
        log_warning "以下环境变量仍使用默认值，请修改: ${default_vars[*]}"
    fi
    
    log_success "$env_name 环境文件检查通过"
    return 0
}

# 主函数
main() {
    log_info "开始检查OCTI环境文件配置..."
    echo ""
    
    local has_error=false
    
    # 检查 .env 文件
    if ! check_env_file ".env" "默认"; then
        has_error=true
    fi
    echo ""
    
    # 检查 .env.production 文件
    if ! check_env_file ".env.production" "生产"; then
        has_error=true
    fi
    echo ""
    
    # 检查 .env.example 文件
    if [[ -f ".env.example" ]]; then
        log_success "模板文件 .env.example 存在"
    else
        log_error "模板文件 .env.example 不存在"
        has_error=true
    fi
    echo ""
    
    if [[ "$has_error" == "true" ]]; then
        log_error "环境文件检查失败"
        echo ""
        echo "💡 修复建议："
        echo "1. 确保所有必要的环境变量都已配置"
        echo "2. 修改默认的密码和密钥"
        echo "3. 参考 ENV_GUIDE.md 获取详细配置说明"
        exit 1
    else
        log_success "所有环境文件检查通过！"
        echo ""
        echo "📋 环境文件状态："
        echo "✅ .env - 默认环境文件（本地开发/Docker）"
        echo "✅ .env.production - 生产环境文件"
        echo "✅ .env.example - 模板文件"
        echo ""
        echo "🚀 可以开始部署了！"
    fi
}

# 解析命令行参数
case "${1:-check}" in
    check|all)
        main
        ;;
    dev)
        check_env_file ".env" "默认"
        ;;
    prod|production)
        check_env_file ".env.production" "生产"
        ;;
    help|--help|-h)
        echo "OCTI环境变量检查脚本"
        echo "用法: $0 [命令]"
        echo ""
        echo "命令:"
        echo "  check      - 检查所有环境文件（默认）"
        echo "  dev        - 只检查 .env 文件"
        echo "  production - 只检查 .env.production 文件"
        echo "  help       - 显示帮助"
        ;;
    *)
        log_error "未知命令: $1"
        echo "使用 '$0 help' 查看可用命令"
        exit 1
        ;;
esac
