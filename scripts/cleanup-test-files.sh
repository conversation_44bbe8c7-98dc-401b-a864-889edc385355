#!/bin/bash

# OCTI测试文件清理脚本
# 清理不必要的测试页面，保留有价值的测试文件

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

echo "🧹 OCTI测试文件清理"
echo "==================="

# 定义要删除的所有测试目录
TEST_DIRS_TO_DELETE=(
    "src/app/test-focus-area"
    "src/app/test-format"
    "src/app/test-full-flow"
    "src/app/test-intelligent"
    "src/app/test-intelligent-questionnaire"
    "src/app/test-llm"
    "src/app/test-professional"
    "src/app/test-questionnaire"
    "src/app/test-simple"
    "src/app/test-single-question"
    "src/app/test-standard-report"
)

# 显示清理计划
echo "📋 清理计划："
echo ""
echo "🗑️ 将要删除的所有测试目录："
for dir in "${TEST_DIRS_TO_DELETE[@]}"; do
    if [ -d "$dir" ]; then
        echo "  - $dir"
    fi
done

echo ""
echo "⚠️ 注意：这将删除所有测试页面，包括刚创建的关注领域测试页面"

echo ""
read -p "确认执行清理？(y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    log_info "清理已取消"
    exit 0
fi

# 执行清理
log_info "开始清理测试文件..."

deleted_count=0
for dir in "${TEST_DIRS_TO_DELETE[@]}"; do
    if [ -d "$dir" ]; then
        log_warning "删除目录: $dir"
        rm -rf "$dir"
        ((deleted_count++))
    else
        log_info "目录不存在，跳过: $dir"
    fi
done

# 清理结果
echo ""
echo "🎯 清理结果："
echo "============="
echo "删除的测试目录: $deleted_count"

# 显示剩余的测试文件
echo ""
echo "📁 剩余的测试相关文件："
find src/app -name "test-*" -type d 2>/dev/null | sort || echo "无测试目录"

# 检查是否有其他测试文件
echo ""
echo "🔍 其他可能的测试文件："
find src -name "*test*" -type f | grep -v node_modules | head -10 || echo "未发现其他测试文件"

log_success "🎉 所有测试文件清理完成！"

echo ""
echo "✨ 清理效果："
echo "- 删除了所有测试页面，代码更加简洁"
echo "- 减少了维护负担"
echo "- 项目结构更加清晰"
echo ""
echo "💡 如果需要测试功能，可以："
echo "- 使用正常的评估流程进行测试"
echo "- 查看浏览器控制台日志验证功能"
echo "- 检查Docker日志确认修复效果"
