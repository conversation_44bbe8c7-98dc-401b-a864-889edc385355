#!/bin/bash

# OCTI智能评估系统 - 生产环境部署脚本
# 版本: v4.0.0
# 作者: OCTI Team

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查依赖
check_dependencies() {
    log_info "检查系统依赖..."

    # 检查Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装，请先安装Docker"
        exit 1
    fi

    # 检查Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose未安装，请先安装Docker Compose"
        exit 1
    fi

    # 检查Docker版本
    DOCKER_VERSION=$(docker --version | grep -oE '[0-9]+\.[0-9]+\.[0-9]+' | head -1)
    log_info "Docker版本: $DOCKER_VERSION"

    # 检查Docker Compose版本
    COMPOSE_VERSION=$(docker-compose --version | grep -oE '[0-9]+\.[0-9]+\.[0-9]+' | head -1)
    log_info "Docker Compose版本: $COMPOSE_VERSION"

    log_success "依赖检查完成"
}

# 环境配置检查
check_environment() {
    log_info "检查环境配置..."

    # 检查环境变量文件
    if [ ! -f ".env.production" ]; then
        log_warning ".env.production文件不存在，将从模板创建"
        if [ -f ".env.example" ]; then
            cp .env.example .env.production
            log_info "请编辑 .env.production 文件配置生产环境变量"
            read -p "按回车键继续..."
        else
            log_error ".env.example模板文件不存在"
            exit 1
        fi
    fi

    log_success "环境配置检查完成"
}

# 构建镜像
build_images() {
    log_info "构建生产环境Docker镜像..."

    # 选择构建模式
    echo "选择构建模式:"
    echo "1) 快速构建（使用缓存）"
    echo "2) 完全重建（清除缓存）"
    read -p "请选择 [1-2]: " BUILD_MODE

    case $BUILD_MODE in
        1)
            log_info "使用缓存构建..."
            docker-compose -f docker-compose.yml build
            ;;
        2)
            log_info "完全重建（清除缓存）..."
            docker-compose -f docker-compose.yml build --no-cache
            ;;
        *)
            log_warning "无效选择，使用默认快速构建"
            docker-compose -f docker-compose.yml build
            ;;
    esac

    log_success "镜像构建完成"
}

# 部署服务
deploy_services() {
    log_info "部署生产环境服务..."

    # 停止现有服务
    log_info "停止现有服务..."
    docker-compose -f docker-compose.yml down

    # 启动新服务
    log_info "启动新服务..."
    docker-compose -f docker-compose.yml up -d

    # 等待服务启动
    log_info "等待服务启动..."
    sleep 10

    log_success "服务部署完成"
}

# 数据库初始化
init_database() {
    log_info "初始化数据库..."

    # 等待数据库就绪
    log_info "等待数据库就绪..."
    timeout=60
    while [ $timeout -gt 0 ]; do
        if docker-compose -f docker-compose.yml exec -T postgres pg_isready -U postgres &> /dev/null; then
            break
        fi
        sleep 2
        timeout=$((timeout-2))
    done

    if [ $timeout -le 0 ]; then
        log_error "数据库启动超时"
        exit 1
    fi

    # 检查数据库状态并处理迁移
    log_info "检查数据库状态..."

    # 尝试运行迁移，如果失败则重置数据库
    if ! docker-compose -f docker-compose.yml exec -T app npx prisma migrate deploy 2>/dev/null; then
        log_warning "数据库迁移失败，尝试重置数据库..."

        # 询问用户是否要重置数据库
        read -p "数据库已存在数据，是否要重置数据库？(y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            log_info "重置数据库..."
            docker-compose -f docker-compose.yml exec -T app npx prisma migrate reset --force
        else
            log_info "跳过数据库迁移，使用现有数据库..."
            # 只生成客户端
            docker-compose -f docker-compose.yml exec -T app npx prisma generate
        fi
    else
        log_success "数据库迁移成功"
        # 生成Prisma客户端
        log_info "生成Prisma客户端..."
        docker-compose -f docker-compose.yml exec -T app npx prisma generate
    fi

    log_success "数据库初始化完成"
}

# 健康检查
health_check() {
    log_info "执行健康检查..."

    # 检查容器状态
    log_info "检查容器状态..."
    docker-compose -f docker-compose.yml ps

    # 等待应用就绪
    log_info "等待应用就绪..."
    timeout=120
    while [ $timeout -gt 0 ]; do
        if curl -f http://localhost:3000/api/health &> /dev/null; then
            break
        fi
        sleep 5
        timeout=$((timeout-5))
    done

    if [ $timeout -le 0 ]; then
        log_error "应用健康检查失败"
        log_info "查看应用日志:"
        docker-compose -f docker-compose.yml logs app --tail 20
        exit 1
    fi

    # 显示健康检查结果
    log_info "应用健康检查结果:"
    curl -s http://localhost:3000/api/health | jq '.' || curl -s http://localhost:3000/api/health

    log_success "健康检查通过"
}

# 显示部署信息
show_deployment_info() {
    log_success "=== 部署完成 ==="
    echo
    log_info "服务访问地址:"
    echo "  应用地址: http://localhost:3000"
    echo "  健康检查: http://localhost:3000/api/health"
    echo
    log_info "管理命令:"
    echo "  查看日志: docker-compose -f docker-compose.yml logs -f"
    echo "  查看状态: docker-compose -f docker-compose.yml ps"
    echo "  停止服务: docker-compose -f docker-compose.yml down"
    echo "  重启服务: docker-compose -f docker-compose.yml restart"
    echo
    log_info "数据库连接:"
    echo "  主机: localhost"
    echo "  端口: 5432"
    echo "  数据库: octi_db"
    echo "  用户: postgres"
    echo
    log_info "Redis连接:"
    echo "  主机: localhost"
    echo "  端口: 6379"
    echo
}

# 清理函数
cleanup() {
    if [ $? -ne 0 ]; then
        log_error "部署过程中发生错误"
        log_info "查看错误日志:"
        docker-compose -f docker-compose.yml logs --tail 20
    fi
}

# 主函数
main() {
    echo "========================================"
    echo "  OCTI智能评估系统 - 生产环境部署脚本"
    echo "  版本: v4.0.0"
    echo "========================================"
    echo

    # 设置错误处理
    trap cleanup EXIT

    # 确认部署
    read -p "确认要部署到生产环境吗？[y/N]: " CONFIRM
    if [[ ! $CONFIRM =~ ^[Yy]$ ]]; then
        log_info "部署已取消"
        exit 0
    fi

    # 执行部署步骤
    check_dependencies
    check_environment
    build_images
    deploy_services
    init_database
    health_check
    show_deployment_info

    log_success "生产环境部署成功完成！"
}

# 运行主函数
main "$@"