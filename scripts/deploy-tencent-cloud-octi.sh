#!/bin/bash

# OCTI 腾讯云部署脚本 v2.0 - 专为octi服务账户设计
# 支持简化版和完整生产版部署选择
# 适用于已预配置权限的octi用户

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m'

# 日志函数
log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }
log_step() { echo -e "${PURPLE}[STEP]${NC} $1"; }
log_choice() { echo -e "${CYAN}[CHOICE]${NC} $1"; }

# 配置变量
PROJECT_NAME="octi"
PROJECT_DIR="/opt/octi"
DOMAIN=""
EMAIL=""
DEPLOYMENT_MODE=""

# 解析命令行参数
FORCE_REBUILD=false
SKIP_CACHE_CLEANUP=false
PARALLEL_BUILD=true
UPDATE_MODE=false

while [[ $# -gt 0 ]]; do
    case $1 in
        --domain)
            DOMAIN="$2"
            shift 2
            ;;
        --email)
            EMAIL="$2"
            shift 2
            ;;
        --mode)
            DEPLOYMENT_MODE="$2"
            shift 2
            ;;
        --force-rebuild)
            FORCE_REBUILD=true
            shift
            ;;
        --skip-cache-cleanup)
            SKIP_CACHE_CLEANUP=true
            shift
            ;;
        --no-parallel)
            PARALLEL_BUILD=false
            shift
            ;;
        update)
            UPDATE_MODE=true
            shift
            ;;
        -h|--help)
            echo "OCTI腾讯云部署脚本 v2.0 - octi用户版"
            echo "用法: $0 [选项|命令]"
            echo ""
            echo "选项:"
            echo "  --domain DOMAIN        设置域名（可选，用于SSL证书）"
            echo "  --email EMAIL          设置邮箱（用于SSL证书申请）"
            echo "  --mode MODE            部署模式：simple|full（可选，不指定则交互选择）"
            echo "  --force-rebuild        强制重新构建，忽略缓存"
            echo "  --skip-cache-cleanup   跳过缓存清理"
            echo "  --no-parallel          禁用并行构建"
            echo "  -h, --help             显示帮助信息"
            echo ""
            echo "命令:"
            echo "  update                 更新模式：拉取最新代码并重新部署"
            echo ""
            echo "部署模式:"
            echo "  🚀 simple - 简化版（推荐）"
            echo "     ├── Next.js应用 + PostgreSQL + Redis"
            echo "     ├── 快速启动，资源占用少"
            echo "     └── 适合功能测试和小规模使用"
            echo ""
            echo "  🏭 full - 完整生产版"
            echo "     ├── 包含Nginx反向代理"
            echo "     ├── Prometheus + Grafana监控"
            echo "     ├── 完整的安全配置"
            echo "     └── 适合生产环境和大规模使用"
            echo ""
            echo "优化特性:"
            echo "  ✅ 智能缓存管理 - 避免重复构建"
            echo "  ✅ 分步构建策略 - 依赖、Prisma、应用分离"
            echo "  ✅ 中国镜像源加速 - 使用npmmirror.com"
            echo "  ✅ 并行构建支持 - 提升构建速度"
            echo "  ✅ 构建缓存持久化 - 跨部署复用"
            echo "  ✅ 部署模式选择 - 简化版或完整版"
            echo ""
            echo "前置条件:"
            echo "  1. 当前用户必须是octi"
            echo "  2. Docker已安装且octi用户在docker组中"
            echo "  3. 项目代码已上传到当前目录"
            echo ""
            echo "示例:"
            echo "  $0                                    # 交互式选择部署模式"
            echo "  $0 --mode simple                     # 直接部署简化版"
            echo "  $0 --mode full --domain example.com  # 部署完整版并配置域名"
            echo "  $0 update                             # 更新模式：拉取最新代码并重新部署"
            exit 0
            ;;
        *)
            log_error "未知参数: $1"
            exit 1
            ;;
    esac
done

# 检查系统环境
check_system() {
    log_info "检查系统环境..."
    
    # 检查是否为Ubuntu 22.04
    if ! grep -q "Ubuntu 22.04" /etc/os-release; then
        log_error "此脚本仅支持Ubuntu 22.04系统"
        exit 1
    fi
    
    # 检查当前用户
    CURRENT_USER=$(whoami)
    if [[ "$CURRENT_USER" != "octi" ]]; then
        log_warning "当前用户是 $CURRENT_USER，建议使用octi用户运行"
        read -p "继续执行？(y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            log_error "用户取消执行"
            exit 1
        fi
    fi
    
    log_success "系统环境检查通过"
}

# 检查依赖
check_dependencies() {
    log_info "检查系统依赖..."
    
    local deps=("docker" "docker-compose" "curl" "git")
    local missing_deps=()
    
    for dep in "${deps[@]}"; do
        if ! command -v "$dep" &> /dev/null; then
            missing_deps+=("$dep")
        fi
    done
    
    if [[ ${#missing_deps[@]} -gt 0 ]]; then
        log_error "缺少以下依赖: ${missing_deps[*]}"
        log_info "请联系系统管理员安装缺少的依赖"
        exit 1
    fi
    
    # 检查Docker权限
    if ! docker info &> /dev/null; then
        log_error "Docker权限不足，请确保当前用户在docker组中"
        log_info "管理员可运行: sudo usermod -aG docker $CURRENT_USER"
        log_info "然后重新登录或运行: newgrp docker"
        exit 1
    fi
    
    # 检查Node.js版本
    check_nodejs_version

    log_success "依赖检查通过"
}

# 检查Node.js版本
check_nodejs_version() {
    log_info "检查Node.js版本..."

    if ! command -v node &> /dev/null; then
        log_warning "未安装Node.js，将使用Docker容器中的Node.js v20"
        return 0
    fi

    local current_version=$(node -v | sed 's/v//')
    local required_version="18.0.0"

    # 简单的版本比较
    if [[ "$(printf '%s\n' "$required_version" "$current_version" | sort -V | head -n1)" != "$required_version" ]]; then
        log_warning "当前Node.js版本: v$current_version (推荐: v20+)"
        log_info "将使用Docker容器中的Node.js v20进行构建"
        log_info "这不会影响部署，因为应用运行在Docker容器中"
    else
        log_success "Node.js版本检查通过: v$current_version"
    fi
}

# 检查项目代码
check_project_code() {
    log_info "检查项目代码..."
    
    # 检查必要文件
    local required_files=("package.json" "docker-compose.yml" ".env.example")
    local missing_files=()
    
    for file in "${required_files[@]}"; do
        if [[ ! -f "$file" ]]; then
            missing_files+=("$file")
        fi
    done
    
    if [[ ${#missing_files[@]} -gt 0 ]]; then
        log_error "缺少必要文件: ${missing_files[*]}"
        log_info "请确保项目代码已正确上传"
        exit 1
    fi
    
    log_success "项目代码检查通过"
}

# 创建项目目录
setup_project() {
    log_info "设置项目目录..."
    
    # 如果不在项目目录，则复制到项目目录
    if [[ "$(pwd)" != "$PROJECT_DIR" ]]; then
        log_info "复制项目到 $PROJECT_DIR..."
        
        # 创建项目目录（如果不存在）
        if [[ ! -d "$PROJECT_DIR" ]]; then
            mkdir -p "$PROJECT_DIR" || {
                log_error "无法创建项目目录 $PROJECT_DIR"
                log_info "请联系系统管理员创建目录并设置权限"
                exit 1
            }
        fi
        
        # 复制项目文件
        cp -r . "$PROJECT_DIR/" || {
            log_error "无法复制项目文件到 $PROJECT_DIR"
            exit 1
        }
        
        # 切换到项目目录
        cd "$PROJECT_DIR"
    fi
    
    # 创建数据目录
    mkdir -p data/{postgres,redis,prometheus,grafana}
    mkdir -p logs
    mkdir -p backups
    
    log_success "项目目录设置完成"
}

# 选择部署模式
choose_deployment_mode() {
    if [[ -n "$DEPLOYMENT_MODE" ]]; then
        log_info "使用指定的部署模式: $DEPLOYMENT_MODE"
        return 0
    fi

    log_step "选择部署模式"
    echo ""
    echo "请选择OCTI系统的部署模式："
    echo ""
    log_choice "1. 🚀 简化版 (推荐)"
    echo "   ├── 包含: Next.js应用 + PostgreSQL + Redis"
    echo "   ├── 优势: 快速启动，资源占用少，维护简单"
    echo "   ├── 适用: 功能测试、小规模使用、开发环境"
    echo "   └── 资源: ~1GB内存，~2GB磁盘"
    echo ""
    log_choice "2. 🏭 完整生产版"
    echo "   ├── 包含: 简化版 + Nginx + Prometheus + Grafana"
    echo "   ├── 优势: 完整监控，负载均衡，生产级安全"
    echo "   ├── 适用: 生产环境，大规模使用，性能监控"
    echo "   └── 资源: ~2GB内存，~5GB磁盘"
    echo ""

    while true; do
        read -p "请选择部署模式 [1-2]: " choice
        case $choice in
            1)
                DEPLOYMENT_MODE="simple"
                log_success "已选择: 简化版部署"
                break
                ;;
            2)
                DEPLOYMENT_MODE="full"
                log_success "已选择: 完整生产版部署"
                break
                ;;
            *)
                log_warning "请输入有效选项 (1 或 2)"
                ;;
        esac
    done

    echo ""
    log_info "部署模式: $DEPLOYMENT_MODE"
}

# 创建对应的docker-compose文件
create_compose_files() {
    log_info "创建Docker Compose配置文件..."

    if [[ "$DEPLOYMENT_MODE" == "full" ]]; then
        log_info "创建完整生产版配置..."
        create_full_compose_file
        create_nginx_config
        create_prometheus_config
    else
        log_info "使用简化版配置..."
        # 简化版直接使用现有的docker-compose.yml
    fi

    log_success "Docker Compose配置创建完成"
}

# 创建完整版docker-compose文件
create_full_compose_file() {
    cat > docker-compose.full.yml << 'EOF'
# OCTI智能评估系统 - 完整生产环境配置
# 包含Nginx、监控等完整功能

services:
  # Next.js应用服务
  app:
    build:
      context: .
      target: production
    image: octi-production:latest
    container_name: octi_app_full
    restart: unless-stopped
    environment:
      - NODE_ENV=production
      - DATABASE_URL=postgresql://postgres:${POSTGRES_PASSWORD:-octi123456}@postgres:5432/octi
      - REDIS_URL=redis://redis:6379
      - NEXTAUTH_SECRET=${NEXTAUTH_SECRET:-octi-super-secret-key-for-production-2024}
      - NEXTAUTH_URL=${NEXTAUTH_URL:-http://localhost}
      - PRISMA_CLI_BINARY_TARGETS=debian-openssl-3.0.x
      - NEXT_TELEMETRY_DISABLED=1
      - MINIMAX_API_KEY=${MINIMAX_API_KEY:-}
      - MINIMAX_API_KEY_1=${MINIMAX_API_KEY_1:-}
      - MINIMAX_API_KEY_2=${MINIMAX_API_KEY_2:-}
      - MINIMAX_API_KEY_3=${MINIMAX_API_KEY_3:-}
      - DEEPSEEK_API_KEY=${DEEPSEEK_API_KEY:-}
      - LOG_LEVEL=info
      - CONFIG_PATH=/app/configs
    volumes:
      - ./configs:/app/configs:ro
      - ./logs:/app/logs
    networks:
      - octi_full_network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ['CMD', 'curl', '-f', 'http://localhost:3000/api/health']
      interval: 60s
      timeout: 30s
      retries: 5
      start_period: 120s

  # PostgreSQL数据库服务
  postgres:
    image: postgres:15-alpine
    container_name: octi_postgres_full
    restart: unless-stopped
    environment:
      POSTGRES_DB: octi
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-octi123456}
    ports:
      - '5432:5432'
    volumes:
      - ./data/postgres:/var/lib/postgresql/data
      - ./init-db.sql:/docker-entrypoint-initdb.d/init-db.sql:ro
    networks:
      - octi_full_network
    healthcheck:
      test: ['CMD-SHELL', 'pg_isready -U postgres -d octi']
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis缓存服务
  redis:
    image: redis:7-alpine
    container_name: octi_redis_full
    restart: unless-stopped
    ports:
      - '6379:6379'
    volumes:
      - ./data/redis:/data
    networks:
      - octi_full_network
    command: redis-server --maxmemory 512mb --maxmemory-policy allkeys-lru --save 900 1 --appendonly yes
    healthcheck:
      test: ['CMD', 'redis-cli', 'ping']
      interval: 10s
      timeout: 3s
      retries: 5

  # Nginx反向代理
  nginx:
    image: nginx:1.25-alpine
    container_name: octi_nginx_full
    restart: unless-stopped
    ports:
      - '80:80'
      - '443:443'
    volumes:
      - ./docker/nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./docker/nginx/conf.d:/etc/nginx/conf.d:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
    networks:
      - octi_full_network
    depends_on:
      - app
    healthcheck:
      test: ['CMD', 'curl', '-f', 'http://localhost/health']
      interval: 30s
      timeout: 10s
      retries: 3

  # Prometheus监控
  prometheus:
    image: prom/prometheus:v2.45.0
    container_name: octi_prometheus_full
    restart: unless-stopped
    ports:
      - '9090:9090'
    volumes:
      - ./docker/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - ./docker/prometheus/alert_rules.yml:/etc/prometheus/alert_rules.yml:ro
      - ./data/prometheus:/prometheus
    networks:
      - octi_full_network
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--storage.tsdb.retention.time=30d'
      - '--web.enable-lifecycle'
      - '--web.enable-admin-api'
    user: "65534:65534"

  # Grafana仪表板
  grafana:
    image: grafana/grafana:10.0.0
    container_name: octi_grafana_full
    restart: unless-stopped
    ports:
      - '3001:3000'
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD:-admin123}
      - GF_USERS_ALLOW_SIGN_UP=false
      - GF_INSTALL_PLUGINS=
    volumes:
      - ./data/grafana:/var/lib/grafana
      - ./docker/grafana/provisioning:/etc/grafana/provisioning:ro
    networks:
      - octi_full_network
    depends_on:
      - prometheus
    user: "472:472"

# 网络配置
networks:
  octi_full_network:
    driver: bridge
    name: octi-full-network

# 数据卷
volumes:
  postgres_data:
    name: octi_postgres_full_data
  redis_data:
    name: octi_redis_full_data
  prometheus_data:
    name: octi_prometheus_full_data
  grafana_data:
    name: octi_grafana_full_data
EOF
}

# 创建Nginx配置
create_nginx_config() {
    log_info "创建Nginx配置..."

    mkdir -p nginx/ssl
    # 使用现有的docker/nginx配置目录

    # 使用现有的nginx配置文件，不需要重新创建
    if [[ ! -f "docker/nginx/nginx.conf" ]]; then
        log_error "未找到现有的Nginx配置文件: docker/nginx/nginx.conf"
        exit 1
    fi

    # 创建SSL配置（如果需要）
    if [[ -n "$DOMAIN" ]]; then
        create_nginx_ssl_config
    fi
}

# 创建HTTP配置
create_nginx_http_config() {
    cat > nginx/conf.d/octi.conf << 'EOF'
server {
    listen 80;
    server_name _;

    # 健康检查
    location /health {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }

    # 主应用
    location / {
        proxy_pass http://app:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        proxy_read_timeout 86400;
    }
}
EOF
}

# 创建HTTPS配置
create_nginx_ssl_config() {
    mkdir -p nginx/conf.d
    cat > nginx/conf.d/octi.conf << EOF
server {
    listen 80;
    server_name ${DOMAIN};
    return 301 https://\$server_name\$request_uri;
}

server {
    listen 443 ssl http2;
    server_name ${DOMAIN};

    # SSL配置
    ssl_certificate /etc/nginx/ssl/${DOMAIN}.crt;
    ssl_certificate_key /etc/nginx/ssl/${DOMAIN}.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;

    # 健康检查
    location /health {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }

    # 主应用
    location / {
        proxy_pass http://app:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_cache_bypass \$http_upgrade;
        proxy_read_timeout 86400;
    }
}
EOF
}

# 创建Prometheus配置
create_prometheus_config() {
    log_info "创建Prometheus配置..."

    # 使用现有的prometheus配置文件
    if [[ ! -f "docker/prometheus/prometheus.yml" ]]; then
        log_error "未找到现有的Prometheus配置文件: docker/prometheus/prometheus.yml"
        exit 1
    fi

    log_success "使用现有的Prometheus配置"
}

# 配置环境变量
setup_environment() {
    log_info "配置环境变量..."
    
    # 检查是否存在.env.example
    if [[ ! -f ".env.example" ]]; then
        log_error "未找到.env.example文件"
        exit 1
    fi
    
    # 复制环境变量文件
    if [[ ! -f ".env.production" ]]; then
        cp .env.example .env.production
        log_warning "已创建.env.production文件，请编辑配置"
        log_info "请编辑 $PROJECT_DIR/.env.production 文件，配置以下重要参数："
        echo "- POSTGRES_PASSWORD"
        echo "- REDIS_PASSWORD"
        echo "- NEXTAUTH_SECRET"
        echo "- JWT_SECRET"
        echo "- ENCRYPTION_KEY"
        echo "- MINIMAX_API_KEY"
        echo "- DEEPSEEK_API_KEY"
        
        if [[ -n "$DOMAIN" ]]; then
            echo "- DOMAIN=$DOMAIN"
            echo "- NEXTAUTH_URL=https://$DOMAIN"
        fi
        
        echo ""
        read -p "环境变量已配置完成？(y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            log_error "请先配置环境变量"
            exit 1
        fi
    fi
    
    log_success "环境变量配置完成"
}

# 优化Docker构建环境
optimize_build_environment() {
    log_info "优化Docker构建环境..."

    # 设置Docker构建优化
    export DOCKER_BUILDKIT=1
    export COMPOSE_DOCKER_CLI_BUILD=1

    # 设置npm镜像源加速
    export NPM_CONFIG_REGISTRY=https://registry.npmmirror.com/
    export NPM_CONFIG_FETCH_TIMEOUT=600000
    export NPM_CONFIG_FETCH_RETRY_MINTIMEOUT=20000
    export NPM_CONFIG_FETCH_RETRY_MAXTIMEOUT=120000

    log_success "构建环境优化完成"
}

# 清理构建缓存
cleanup_build_cache() {
    log_info "清理Docker构建缓存..."

    # 清理构建缓存（保留最近的）
    docker builder prune -f --filter until=24h

    # 清理未使用的镜像（保留最近的）
    docker image prune -f --filter until=24h

    # 清理未使用的容器
    docker container prune -f

    log_success "构建缓存清理完成"
}

# 创建优化的Dockerfile
create_optimized_dockerfile() {
    log_info "创建优化的Dockerfile..."

    # 备份原Dockerfile
    if [[ -f "Dockerfile" ]]; then
        cp Dockerfile Dockerfile.backup.$(date +%Y%m%d_%H%M%S)
    fi

    # 创建针对中国网络环境优化的Dockerfile
    cat > Dockerfile.optimized << 'EOF'
# 多阶段构建 - 基础镜像
FROM node:20-alpine AS base
WORKDIR /app

# 设置npm镜像源和超时
RUN npm config set registry https://registry.npmmirror.com/ && \
    npm config set fetch-timeout 600000 && \
    npm config set fetch-retry-mintimeout 20000 && \
    npm config set fetch-retry-maxtimeout 120000

# 依赖安装阶段
FROM base AS deps
COPY package.json package-lock.json ./
RUN npm ci --frozen-lockfile --prefer-offline --no-audit --no-fund

# Prisma生成阶段
FROM base AS prisma
COPY --from=deps /app/node_modules ./node_modules
COPY prisma ./prisma
RUN npx prisma generate

# 构建阶段
FROM base AS builder
COPY --from=deps /app/node_modules ./node_modules
COPY --from=prisma /app/node_modules/.prisma ./node_modules/.prisma
COPY . .

# 构建应用
ENV NEXT_TELEMETRY_DISABLED=1
ENV SKIP_ENV_VALIDATION=1
RUN npm run build

# 运行阶段
FROM node:20-alpine AS runner
WORKDIR /app

ENV NODE_ENV=production
ENV NEXT_TELEMETRY_DISABLED=1

# 创建用户
RUN addgroup --system --gid 1001 nodejs && \
    adduser --system --uid 1001 nextjs

# 复制构建产物
COPY --from=builder /app/public ./public
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static
COPY --from=builder /app/node_modules/.prisma ./node_modules/.prisma
COPY --from=builder /app/prisma ./prisma

USER nextjs

EXPOSE 3000
ENV PORT=3000
ENV HOSTNAME="0.0.0.0"

CMD ["node", "server.js"]
EOF

    log_success "优化Dockerfile创建完成"
}

# 分步构建策略
staged_build() {
    log_info "执行分步构建策略..."

    local build_cache_dir="$HOME/.octi-build-cache"
    mkdir -p "$build_cache_dir"

    # 检查是否有缓存的依赖
    local deps_hash=$(md5sum package.json package-lock.json 2>/dev/null | md5sum | cut -d' ' -f1)
    local deps_cache="$build_cache_dir/deps-$deps_hash.tar"

    if [[ -f "$deps_cache" ]]; then
        log_info "发现依赖缓存，正在恢复..."
        docker load < "$deps_cache"
        log_success "依赖缓存恢复完成"
    else
        log_info "构建依赖镜像..."

        # 构建依赖阶段
        docker build --target deps -t octi-deps:latest -f Dockerfile.optimized . || {
            log_error "依赖构建失败"
            return 1
        }

        # 缓存依赖镜像
        log_info "缓存依赖镜像..."
        docker save octi-deps:latest > "$deps_cache"
        log_success "依赖镜像缓存完成"
    fi

    # 构建Prisma阶段
    log_info "构建Prisma客户端..."
    docker build --target prisma -t octi-prisma:latest -f Dockerfile.optimized . || {
        log_error "Prisma构建失败"
        return 1
    }

    # 构建应用
    log_info "构建应用镜像..."
    docker build --target builder -t octi-builder:latest -f Dockerfile.optimized . || {
        log_error "应用构建失败"
        return 1
    }

    # 构建最终运行镜像
    log_info "构建最终运行镜像..."
    docker build -t octi-production:latest -f Dockerfile.optimized . || {
        log_error "最终镜像构建失败"
        return 1
    }

    log_success "分步构建完成"
}

# 智能构建决策
smart_build() {
    log_info "执行智能构建..."

    # 检查是否需要重新构建
    local need_rebuild=false

    # 强制重建选项
    if [[ "$FORCE_REBUILD" == "true" ]]; then
        log_info "强制重建模式，将重新构建所有镜像"
        need_rebuild=true
    else
        # 检查代码变更
        if [[ ! -f ".build-hash" ]] || ! md5sum -c .build-hash &>/dev/null; then
            log_info "检测到代码变更，需要重新构建"
            need_rebuild=true
        fi

        # 检查镜像是否存在
        if ! docker images | grep -q "octi-production.*latest"; then
            log_info "未找到应用镜像，需要构建"
            need_rebuild=true
        fi

        # 检查依赖变更
        local deps_hash=$(md5sum package.json package-lock.json 2>/dev/null | md5sum | cut -d' ' -f1)
        if [[ ! -f ".deps-hash" ]] || [[ "$(cat .deps-hash 2>/dev/null)" != "$deps_hash" ]]; then
            log_info "检测到依赖变更，需要重新构建"
            need_rebuild=true
        fi
    fi

    if [[ "$need_rebuild" == "true" ]]; then
        # 优化构建环境
        optimize_build_environment

        # 创建优化的Dockerfile
        create_optimized_dockerfile

        # 执行分步构建
        staged_build || return 1

        # 记录构建哈希
        find . -name "*.js" -o -name "*.ts" -o -name "*.tsx" -o -name "*.json" | \
            grep -v node_modules | grep -v .next | grep -v .git | \
            xargs md5sum > .build-hash

        # 记录依赖哈希
        local deps_hash=$(md5sum package.json package-lock.json 2>/dev/null | md5sum | cut -d' ' -f1)
        echo "$deps_hash" > .deps-hash

        log_success "智能构建完成"
    else
        log_info "代码未变更，跳过构建"
    fi
}

# 并行构建优化
parallel_build() {
    log_info "执行并行构建..."

    if [[ "$PARALLEL_BUILD" == "true" ]]; then
        # 使用并行构建
        docker-compose --env-file .env.production build --parallel --no-cache
    else
        # 顺序构建
        docker-compose --env-file .env.production build --no-cache
    fi
}

# 构建性能监控
monitor_build_performance() {
    local start_time=$(date +%s)

    # 执行构建
    "$@"
    local build_result=$?

    local end_time=$(date +%s)
    local duration=$((end_time - start_time))

    if [[ $build_result -eq 0 ]]; then
        log_success "构建完成，耗时: ${duration}秒"

        # 记录构建性能
        echo "$(date): 构建成功，耗时${duration}秒" >> .build-performance.log
    else
        log_error "构建失败，耗时: ${duration}秒"
        echo "$(date): 构建失败，耗时${duration}秒" >> .build-performance.log
    fi

    return $build_result
}

# 显示构建信息
show_build_info() {
    echo ""
    log_info "📊 构建信息总览"
    echo "=================================="

    # 显示镜像信息
    echo "🐳 Docker镜像:"
    docker images | grep -E "(octi|REPOSITORY)" | head -10

    echo ""
    echo "💾 缓存信息:"
    local cache_dir="$HOME/.octi-build-cache"
    if [[ -d "$cache_dir" ]]; then
        echo "  缓存目录: $cache_dir"
        echo "  缓存大小: $(du -sh $cache_dir 2>/dev/null | cut -f1)"
        echo "  缓存文件数: $(find $cache_dir -type f 2>/dev/null | wc -l)"
    else
        echo "  缓存目录: 未创建"
    fi

    echo ""
    echo "⚡ 构建性能:"
    if [[ -f ".build-performance.log" ]]; then
        echo "  最近构建记录:"
        tail -3 .build-performance.log | sed 's/^/    /'
    else
        echo "  暂无构建记录"
    fi

    echo ""
    echo "🔄 构建状态:"
    if [[ -f ".build-hash" ]]; then
        echo "  代码哈希: $(head -c 8 .build-hash)..."
        echo "  构建时间: $(stat -c %y .build-hash 2>/dev/null | cut -d' ' -f1-2)"
    else
        echo "  首次构建"
    fi

    if [[ -f ".deps-hash" ]]; then
        echo "  依赖哈希: $(cat .deps-hash | head -c 8)..."
    fi

    echo "=================================="
}

# 缓存管理功能
manage_cache() {
    local action=$1
    local cache_dir="$HOME/.octi-build-cache"

    case $action in
        "clean")
            log_info "清理构建缓存..."
            rm -rf "$cache_dir"
            rm -f .build-hash .deps-hash
            docker builder prune -f
            log_success "缓存清理完成"
            ;;
        "info")
            show_build_info
            ;;
        "size")
            if [[ -d "$cache_dir" ]]; then
                du -sh "$cache_dir"
            else
                echo "缓存目录不存在"
            fi
            ;;
        *)
            log_error "未知缓存操作: $action"
            return 1
            ;;
    esac
}

# 部署应用
deploy_application() {
    log_info "部署应用 ($DEPLOYMENT_MODE 模式)..."

    # 选择对应的compose文件
    local compose_file="docker-compose.yml"
    if [[ "$DEPLOYMENT_MODE" == "full" ]]; then
        compose_file="docker-compose.full.yml"
        if [[ ! -f "$compose_file" ]]; then
            log_error "未找到完整版配置文件: $compose_file"
            exit 1
        fi
    else
        if [[ ! -f "$compose_file" ]]; then
            log_error "未找到简化版配置文件: $compose_file"
            exit 1
        fi
    fi

    log_info "使用配置文件: $compose_file"

    # 停止现有服务
    log_info "停止现有服务..."
    docker-compose -f "$compose_file" --env-file .env.production down || true

    # 清理构建缓存（可选）
    if [[ "$SKIP_CACHE_CLEANUP" == "false" ]]; then
        read -p "是否清理构建缓存？(y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            cleanup_build_cache
        fi
    else
        log_info "跳过缓存清理"
    fi

    # 执行智能构建（带性能监控）
    monitor_build_performance smart_build || {
        log_error "构建失败"
        exit 1
    }

    # 更新compose文件使用本地镜像（如果需要）
    # 注释掉这个有问题的逻辑，直接使用原始配置文件
    # if ! grep -q "image: octi-app:latest" "$compose_file"; then
    #     log_info "更新docker-compose配置..."
    #     # 备份原配置
    #     cp "$compose_file" "${compose_file}.backup.$(date +%Y%m%d_%H%M%S)"
    #     # 这里的sed命令有问题，会破坏YAML结构
    # fi

    # 启动服务
    log_info "启动服务..."
    docker-compose -f "$compose_file" --env-file .env.production up -d

    # 等待服务启动
    log_info "等待服务启动..."
    sleep 30

    # 检查服务状态
    if docker-compose --env-file .env.production ps | grep -q "Up"; then
        log_success "应用部署成功"

        # 显示构建信息
        show_build_info

    else
        log_error "应用部署失败"
        docker-compose --env-file .env.production logs
        exit 1
    fi
}

# 数据库迁移处理
handle_database_migration() {
    log_info "处理数据库迁移..."

    # 选择对应的compose文件
    local compose_file="docker-compose.yml"
    if [[ "$DEPLOYMENT_MODE" == "full" ]]; then
        compose_file="docker-compose.full.yml"
    fi

    # 等待数据库启动
    log_info "等待数据库启动..."
    sleep 20

    # 检查数据库连接
    local db_ready=false
    for i in {1..10}; do
        if docker-compose -f "$compose_file" --env-file .env.production exec -T postgres pg_isready -U postgres -d octi >/dev/null 2>&1; then
            db_ready=true
            break
        fi
        log_info "等待数据库连接... ($i/10)"
        sleep 5
    done

    if [[ "$db_ready" != "true" ]]; then
        log_error "数据库连接失败"
        return 1
    fi

    log_success "数据库连接正常"

    # 停止应用容器进行权限修复
    log_info "停止应用容器进行权限修复..."
    docker-compose -f "$compose_file" --env-file .env.production stop app

    # 修复Prisma客户端权限问题
    log_info "修复Prisma客户端权限..."
    docker-compose -f "$compose_file" --env-file .env.production run --rm --user root app sh -c "
        echo '清理旧的Prisma客户端...'
        rm -rf /app/node_modules/.prisma/client 2>/dev/null || true
        rm -rf /app/node_modules/@prisma/client 2>/dev/null || true

        echo '重新安装Prisma客户端...'
        npm install @prisma/client

        echo '生成Prisma客户端...'
        npx prisma generate

        echo '设置正确的权限...'
        chown -R nextjs:nodejs /app/node_modules/.prisma 2>/dev/null || true
        chown -R nextjs:nodejs /app/node_modules/@prisma 2>/dev/null || true
        chmod -R 755 /app/node_modules/.prisma 2>/dev/null || true
        chmod -R 755 /app/node_modules/@prisma 2>/dev/null || true

        echo 'Prisma客户端权限修复完成'
    " || {
        log_warning "权限修复失败，尝试继续..."
    }

    # 重新启动应用容器
    log_info "重新启动应用容器..."
    docker-compose -f "$compose_file" --env-file .env.production up -d app

    # 等待应用启动
    sleep 15

    # 尝试运行迁移
    log_info "运行数据库迁移..."
    if docker-compose -f "$compose_file" --env-file .env.production exec -T app npx prisma migrate deploy 2>/dev/null; then
        log_success "数据库迁移成功"
    else
        log_warning "数据库迁移失败，可能是数据库已存在数据"

        # 自动处理：标记为基线
        log_info "尝试标记现有数据库为基线..."
        if docker-compose -f "$compose_file" --env-file .env.production exec -T app sh -c "find prisma/migrations -name '*.sql' | tail -1 | xargs basename -s .sql | xargs npx prisma migrate resolve --applied" 2>/dev/null; then
            log_success "数据库已标记为基线"
        else
            log_warning "无法自动处理迁移，将跳过迁移步骤"
        fi
    fi

    # 生成Prisma客户端
    log_info "生成Prisma客户端..."
    if docker-compose -f "$compose_file" --env-file .env.production exec -T app npx prisma generate; then
        log_success "Prisma客户端生成完成"
    else
        log_warning "Prisma客户端生成失败"
    fi

    # 检查并修复SSL兼容性问题
    log_info "检查SSL兼容性..."
    if ! docker-compose -f "$compose_file" --env-file .env.production exec -T app ls /usr/lib/*/libssl.so.1.1 >/dev/null 2>&1; then
        log_warning "SSL 1.1兼容性库缺失，尝试修复..."
        docker-compose -f "$compose_file" --env-file .env.production exec -T app sh -c "
            ln -sf /usr/lib/x86_64-linux-gnu/libssl.so.3 /usr/lib/x86_64-linux-gnu/libssl.so.1.1 2>/dev/null || \
            ln -sf /usr/lib/aarch64-linux-gnu/libssl.so.3 /usr/lib/aarch64-linux-gnu/libssl.so.1.1 2>/dev/null || \
            true
        "
        log_success "SSL兼容性修复完成"
    fi

    # 重启应用容器以确保使用新的客户端
    log_info "重启应用容器..."
    docker-compose -f "$compose_file" --env-file .env.production restart app

    # 等待应用重启
    sleep 20

    log_success "数据库迁移处理完成"
}

# 健康检查
health_check() {
    log_info "执行健康检查..."
    
    local url="http://localhost"
    if [[ -n "$DOMAIN" ]]; then
        url="https://$DOMAIN"
    fi
    
    # 检查应用响应
    local max_attempts=10
    local attempt=1
    
    while [[ $attempt -le $max_attempts ]]; do
        if curl -f -s "$url/api/health" > /dev/null 2>&1; then
            log_success "应用健康检查通过"
            return 0
        fi
        
        log_info "健康检查尝试 $attempt/$max_attempts..."
        sleep 10
        ((attempt++))
    done
    
    log_warning "应用健康检查超时，请手动检查服务状态"
    return 1
}

# 显示部署信息
show_deployment_info() {
    log_success "=== OCTI腾讯云部署完成 ==="
    echo ""
    echo "📍 项目目录: $PROJECT_DIR"
    echo "👤 运行用户: $(whoami)"
    echo "🐳 Docker状态: $(docker --version)"
    echo "🚀 部署模式: $DEPLOYMENT_MODE"
    echo ""

    # 显示访问地址
    local server_ip=$(curl -s ifconfig.me 2>/dev/null || echo 'localhost')
    if [[ -n "$DOMAIN" ]]; then
        echo "🌐 主要访问地址: https://$DOMAIN"
        echo "🌐 备用访问地址: http://$server_ip"
    else
        echo "🌐 访问地址: http://$server_ip"
    fi

    # 根据部署模式显示不同的服务端口
    echo ""
    echo "🔗 服务端口:"
    echo "  主应用: http://$server_ip:3000"
    echo "  数据库: $server_ip:5432"
    echo "  缓存: $server_ip:6379"

    if [[ "$DEPLOYMENT_MODE" == "full" ]]; then
        echo "  Nginx: http://$server_ip:80"
        if [[ -n "$DOMAIN" ]]; then
            echo "  HTTPS: https://$server_ip:443"
        fi
        echo "  Prometheus: http://$server_ip:9090"
        echo "  Grafana: http://$server_ip:3001"
    fi

    # 显示对应的命令
    local compose_file="docker-compose.yml"
    if [[ "$DEPLOYMENT_MODE" == "full" ]]; then
        compose_file="docker-compose.full.yml"
    fi

    echo ""
    echo "📋 常用命令:"
    echo "  查看服务状态: cd $PROJECT_DIR && docker-compose -f $compose_file --env-file .env.production ps"
    echo "  查看服务日志: cd $PROJECT_DIR && docker-compose -f $compose_file --env-file .env.production logs [service]"
    echo "  重启服务: cd $PROJECT_DIR && docker-compose -f $compose_file --env-file .env.production restart"
    echo "  停止服务: cd $PROJECT_DIR && docker-compose -f $compose_file --env-file .env.production down"
    echo ""
    echo "📁 重要目录:"
    echo "  项目代码: $PROJECT_DIR"
    echo "  数据目录: $PROJECT_DIR/data"
    echo "  日志目录: $PROJECT_DIR/logs"
    echo "  备份目录: $PROJECT_DIR/backups"

    if [[ "$DEPLOYMENT_MODE" == "full" ]]; then
        echo "  Nginx配置: $PROJECT_DIR/nginx"
        echo "  监控配置: $PROJECT_DIR/prometheus"
    fi
    echo ""
}

# 更新模式
update_deployment() {
    log_info "开始更新OCTI部署..."

    # 检查是否在项目目录
    if [[ ! -f "package.json" ]] || [[ ! -f "docker-compose.yml" ]]; then
        log_error "当前目录不是OCTI项目目录"
        log_info "请切换到项目目录后再运行更新命令"
        exit 1
    fi

    # 检查Git仓库
    if [[ ! -d ".git" ]]; then
        log_error "当前目录不是Git仓库"
        log_info "更新模式需要Git仓库支持"
        exit 1
    fi

    # 备份当前配置
    log_info "备份当前配置..."
    local backup_dir="backups/update-$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$backup_dir"

    # 备份重要配置文件
    local config_files=(".env.production" "docker-compose.yml" "docker-compose.full.yml")
    for file in "${config_files[@]}"; do
        if [[ -f "$file" ]]; then
            cp "$file" "$backup_dir/"
            log_info "已备份: $file"
        fi
    done

    # 停止当前服务
    log_info "停止当前服务..."
    if [[ -f "docker-compose.full.yml" ]]; then
        docker-compose -f docker-compose.full.yml --env-file .env.production down || true
    fi
    docker-compose --env-file .env.production down || true

    # 拉取最新代码
    log_info "拉取最新代码..."
    git fetch origin
    local current_branch=$(git branch --show-current)
    log_info "当前分支: $current_branch"

    # 检查是否有本地修改
    if ! git diff --quiet || ! git diff --cached --quiet; then
        log_warning "检测到本地修改，将暂存这些修改"
        git stash push -m "Auto stash before update $(date)"
    fi

    # 拉取并合并最新代码
    git pull origin "$current_branch" || {
        log_error "代码拉取失败"
        log_info "请手动解决冲突后重新运行"
        exit 1
    }

    # 恢复配置文件（如果有变化）
    log_info "检查配置文件变化..."
    for file in "${config_files[@]}"; do
        if [[ -f "$backup_dir/$file" ]] && [[ -f "$file" ]]; then
            if ! diff -q "$backup_dir/$file" "$file" > /dev/null 2>&1; then
                log_warning "配置文件 $file 有变化，请检查是否需要手动合并"
                echo "  备份文件: $backup_dir/$file"
                echo "  当前文件: $file"
            fi
        fi
    done

    # 强制重新构建
    FORCE_REBUILD=true
    SKIP_CACHE_CLEANUP=true

    # 检测部署模式
    if [[ -f "docker-compose.full.yml" ]] && docker-compose -f docker-compose.full.yml config > /dev/null 2>&1; then
        DEPLOYMENT_MODE="full"
        log_info "检测到完整版部署模式"
    else
        DEPLOYMENT_MODE="simple"
        log_info "检测到简化版部署模式"
    fi

    log_success "更新准备完成，开始重新部署..."
}

# 主函数
main() {
    # 检查是否为更新模式
    if [[ "$UPDATE_MODE" == "true" ]]; then
        log_info "开始OCTI更新模式..."
        update_deployment
        # 更新模式下跳过一些初始化步骤
        deploy_application
        handle_database_migration
        health_check
        show_deployment_info
        log_success "🎉 OCTI更新完成！"
        return 0
    fi

    log_info "开始OCTI腾讯云部署 v2.0..."
    echo "目标系统: Ubuntu 22.04"
    echo "项目目录: $PROJECT_DIR"
    echo "当前用户: $(whoami)"
    if [[ -n "$DOMAIN" ]]; then
        echo "域名: $DOMAIN"
    fi
    if [[ -n "$DEPLOYMENT_MODE" ]]; then
        echo "部署模式: $DEPLOYMENT_MODE"
    fi
    echo ""
    echo "🚀 部署特性:"
    echo "  ✅ 智能缓存管理 - 避免重复构建"
    echo "  ✅ 分步构建策略 - 依赖、Prisma、应用分离"
    echo "  ✅ 中国镜像源加速 - 使用npmmirror.com"
    echo "  ✅ 部署模式选择 - 简化版或完整版"
    if [[ "$PARALLEL_BUILD" == "true" ]]; then
        echo "  ✅ 并行构建支持 - 提升构建速度"
    fi
    if [[ "$FORCE_REBUILD" == "true" ]]; then
        echo "  🔄 强制重建模式 - 忽略所有缓存"
    fi
    echo ""

    check_system
    check_dependencies
    check_project_code
    setup_project
    choose_deployment_mode
    create_compose_files
    setup_environment
    deploy_application
    handle_database_migration
    health_check
    show_deployment_info

    log_success "🎉 OCTI腾讯云部署完成！"

    # 显示构建统计
    echo ""
    log_info "💡 构建优化提示:"
    echo "  - 下次部署时，未变更的代码将跳过构建"
    echo "  - 使用 --force-rebuild 强制重新构建"
    echo "  - 使用 --skip-cache-cleanup 跳过缓存清理询问"
    echo "  - 缓存目录: $HOME/.octi-build-cache"
}

main "$@"
