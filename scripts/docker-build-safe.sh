#!/bin/bash

# OCTI Docker构建脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# 配置变量
IMAGE_NAME="octi-app"
# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        --skip-check)
            SKIP_CHECK=true
            shift
            ;;
        -h|--help)
            echo "用法: $0 [选项]"
            echo "选项:"
            echo "  --skip-check  跳过本地验证直接构建"
            echo "  -h, --help    显示帮助信息"
            exit 0
            ;;
        *)
            log_error "未知参数: $1"
            exit 1
            ;;
    esac
done

# 检查Docker环境
check_docker() {
    log_info "检查Docker环境..."
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装"
        exit 1
    fi
    if ! docker info &> /dev/null; then
        log_error "Docker服务未运行"
        exit 1
    fi
    log_success "Docker环境检查通过"
}

# 清理旧镜像
cleanup_old_images() {
    log_info "清理OCTI项目的旧Docker资源..."

    # 停止并删除OCTI相关容器
    local octi_containers=$(docker ps -a --filter "label=com.docker.compose.project=octi_test" -q)
    if [ -n "$octi_containers" ]; then
        docker stop $octi_containers > /dev/null 2>&1 || true
        docker rm -f $octi_containers > /dev/null 2>&1 || true
        log_info "已清理OCTI容器"
    fi

    # 删除OCTI应用镜像
    local app_images=$(docker images --filter "reference=$IMAGE_NAME" -q)
    if [ -n "$app_images" ]; then
        docker rmi -f $app_images > /dev/null 2>&1 || true
        log_info "已清理OCTI应用镜像"
    fi

    log_success "Docker资源清理完成"
}

# 构建Docker镜像
build_docker_image() {
    log_info "开始构建Docker镜像..."

    if docker build --no-cache -t $IMAGE_NAME .; then
        log_success "Docker镜像构建成功"
        docker images | grep $IMAGE_NAME
    else
        log_error "Docker镜像构建失败"
        exit 1
    fi
}

# 主函数
main() {
    log_info "开始OCTI Docker构建流程..."

    # 检查Docker环境
    check_docker

    # 本地验证（除非跳过）
    if [ "$SKIP_CHECK" = false ]; then
        log_info "执行本地验证..."
        if [ -f "./scripts/quick-check.sh" ]; then
            ./scripts/quick-check.sh
        else
            log_info "跳过本地验证（验证脚本不存在）"
        fi
    else
        log_info "跳过本地验证"
    fi

    # 清理旧镜像
    cleanup_old_images

    # 构建镜像
    build_docker_image

    log_success "Docker构建完成！"
    log_info "下一步可以运行: docker-compose up -d"
}

main "$@"
