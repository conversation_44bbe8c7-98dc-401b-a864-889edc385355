#!/bin/bash

# OCTI快速云端部署脚本
# 优化网络和构建速度

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

echo "🚀 OCTI快速云端部署脚本"
echo "======================"

# 1. 检查网络连接
log_info "1. 检查网络连接..."
if ! curl -s --connect-timeout 5 mirrors.aliyun.com >/dev/null; then
    log_warning "阿里云镜像源连接失败，尝试其他镜像源"
    # 可以添加其他镜像源的检查
fi

# 2. 优化Docker构建环境
log_info "2. 优化Docker构建环境..."

# 启用BuildKit以获得更好的缓存和并行构建
export DOCKER_BUILDKIT=1
export COMPOSE_DOCKER_CLI_BUILD=1

# 3. 停止现有服务
log_info "3. 停止现有服务..."
docker-compose down 2>/dev/null || true

# 4. 清理旧镜像（可选）
read -p "是否清理旧镜像以节省空间？(y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    log_info "清理旧镜像..."
    docker system prune -f
    docker rmi octi-production:latest 2>/dev/null || true
fi

# 5. 使用优化的构建命令
log_info "5. 开始优化构建..."

# 使用BuildKit缓存挂载进行构建
log_info "使用BuildKit缓存挂载构建镜像..."
docker build \
    --build-arg BUILDKIT_INLINE_CACHE=1 \
    --cache-from octi-production:latest \
    -t octi-production:latest \
    . || {
    log_error "构建失败，尝试无缓存构建..."
    docker build --no-cache -t octi-production:latest .
}

# 6. 启动服务
log_info "6. 启动服务..."
docker-compose up -d

# 7. 等待服务启动
log_info "7. 等待服务启动..."
sleep 30

# 8. 健康检查
log_info "8. 执行健康检查..."
for i in {1..10}; do
    if curl -s http://localhost:3000/api/health >/dev/null; then
        log_success "✅ 应用启动成功"
        break
    fi
    if [[ $i -eq 10 ]]; then
        log_error "❌ 应用启动失败"
        echo "查看日志:"
        docker-compose logs --tail 20 app
        exit 1
    else
        log_info "等待应用启动... ($i/10)"
        sleep 10
    fi
done

# 9. 显示部署信息
echo ""
echo "🎉 快速部署完成！"
echo ""
echo "📊 服务状态:"
docker-compose ps

echo ""
echo "🌐 访问地址:"
SERVER_IP=$(curl -s ifconfig.me 2>/dev/null || echo 'localhost')
echo "  应用: http://$SERVER_IP:3000"
echo "  健康检查: http://$SERVER_IP:3000/api/health"

echo ""
echo "📋 管理命令:"
echo "  查看日志: docker-compose logs -f app"
echo "  重启服务: docker-compose restart app"
echo "  停止服务: docker-compose down"

# 10. 性能优化建议
echo ""
echo "⚡ 性能优化建议:"
echo "  1. 定期清理Docker缓存: docker system prune -f"
echo "  2. 使用本地镜像仓库缓存常用镜像"
echo "  3. 配置Docker镜像加速器"
echo "  4. 监控服务器资源使用情况"
