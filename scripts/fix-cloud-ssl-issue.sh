#!/bin/bash

# OCTI云端SSL问题修复脚本
# 修复Prisma客户端SSL库兼容性问题

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

echo "🔧 OCTI云端SSL问题修复脚本"
echo "========================="

# 检查是否在云端环境
if [[ ! -f "/etc/os-release" ]] || ! grep -q "Ubuntu\|Debian" /etc/os-release; then
    log_warning "此脚本专为Ubuntu/Debian云端环境设计"
fi

# 1. 停止应用容器
log_info "1. 停止应用容器..."
docker-compose down app 2>/dev/null || true

# 2. 清理旧镜像
log_info "2. 清理旧镜像..."
docker rmi octi-production:latest 2>/dev/null || true

# 3. 重新构建镜像（使用修复的SSL配置）
log_info "3. 重新构建镜像..."
docker-compose build --no-cache app

# 4. 启动基础服务
log_info "4. 确保基础服务运行..."
docker-compose up -d postgres redis

# 等待数据库启动
sleep 20

# 5. 启动应用
log_info "5. 启动应用..."
docker-compose up -d app

# 6. 等待应用启动
log_info "6. 等待应用启动..."
sleep 30

# 7. 检查SSL库问题
log_info "7. 检查SSL库问题..."
if docker-compose exec -T app ls -la /usr/lib/x86_64-linux-gnu/libssl.so.1.1 2>/dev/null || \
   docker-compose exec -T app ls -la /usr/lib/aarch64-linux-gnu/libssl.so.1.1 2>/dev/null; then
    log_success "SSL兼容性库已正确安装"
else
    log_warning "SSL兼容性库可能未正确安装，尝试手动修复..."
    
    # 手动在容器内创建符号链接
    docker-compose exec -T app sh -c "
        ln -sf /usr/lib/x86_64-linux-gnu/libssl.so.3 /usr/lib/x86_64-linux-gnu/libssl.so.1.1 2>/dev/null || \
        ln -sf /usr/lib/aarch64-linux-gnu/libssl.so.3 /usr/lib/aarch64-linux-gnu/libssl.so.1.1 2>/dev/null || \
        true
    "
    
    # 重启应用容器
    docker-compose restart app
    sleep 20
fi

# 8. 测试Prisma连接
log_info "8. 测试Prisma数据库连接..."
if docker-compose exec -T app npx prisma db pull --preview-feature >/dev/null 2>&1; then
    log_success "Prisma数据库连接正常"
else
    log_warning "Prisma数据库连接仍有问题，尝试重新生成客户端..."
    
    # 重新生成Prisma客户端
    docker-compose exec -T app npx prisma generate
    
    # 再次重启应用
    docker-compose restart app
    sleep 20
fi

# 9. 健康检查
log_info "9. 执行健康检查..."
for i in {1..10}; do
    if curl -s http://localhost:3000/api/health >/dev/null; then
        log_success "✅ 应用健康检查通过"
        break
    fi
    if [[ $i -eq 10 ]]; then
        log_error "❌ 应用健康检查失败"
        echo ""
        echo "📝 错误诊断信息:"
        echo "应用日志:"
        docker-compose logs --tail 20 app
        echo ""
        echo "容器状态:"
        docker-compose ps
        exit 1
    else
        log_info "等待应用启动... ($i/10)"
        sleep 10
    fi
done

# 10. 测试智能生成API
log_info "10. 测试智能生成API..."
TEST_RESPONSE=$(curl -s -X POST http://localhost:3000/api/questionnaire/generate-intelligent \
  -H "Content-Type: application/json" \
  -d '{
    "dimension": "SF",
    "profile": {
      "organizationType": "民间组织",
      "serviceArea": ["医疗健康"],
      "developmentStage": "成长期",
      "organizationScale": "中型"
    },
    "count": 2
  }' \
  --max-time 30 || echo "API测试失败")

if echo "$TEST_RESPONSE" | grep -q "success\|questions"; then
    log_success "✅ 智能生成API正常"
else
    log_warning "⚠️ 智能生成API可能有问题"
    echo "API响应: $TEST_RESPONSE"
fi

# 11. 显示最终状态
echo ""
echo "📊 最终状态检查:"
docker-compose ps

echo ""
echo "🌐 服务访问地址:"
SERVER_IP=$(curl -s ifconfig.me 2>/dev/null || echo 'localhost')
echo "  主应用: http://$SERVER_IP:3000"
echo "  健康检查: http://$SERVER_IP:3000/api/health"

echo ""
log_success "🎉 SSL问题修复完成！"

echo ""
echo "📋 如果仍有问题，请尝试："
echo "  1. 查看详细日志: docker-compose logs -f app"
echo "  2. 检查容器内SSL库: docker-compose exec app ls -la /usr/lib/*/libssl*"
echo "  3. 重新生成Prisma: docker-compose exec app npx prisma generate"
echo "  4. 完全重建: docker-compose down && docker system prune -f && docker-compose up -d --build"
