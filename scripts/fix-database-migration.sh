#!/bin/bash

# OCTI数据库迁移修复脚本
# 解决Prisma迁移P3005错误：数据库不为空

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

echo "🔧 OCTI数据库迁移修复脚本"
echo "========================="

# 检查Docker服务是否运行
if ! docker-compose ps | grep -q "Up"; then
    log_error "Docker服务未运行，请先启动服务"
    echo "运行: docker-compose up -d"
    exit 1
fi

# 检查应用容器是否健康
if ! docker-compose ps app | grep -q "Up"; then
    log_error "应用容器未运行"
    exit 1
fi

log_info "检查数据库连接..."
if ! docker-compose exec -T app npx prisma db pull --preview-feature 2>/dev/null; then
    log_error "无法连接到数据库"
    exit 1
fi

log_success "数据库连接正常"

echo ""
echo "选择修复方案："
echo "1) 重置数据库（清空所有数据）"
echo "2) 标记现有数据库为基线（保留数据）"
echo "3) 强制部署迁移（可能失败）"
echo "4) 只生成Prisma客户端"

read -p "请选择 [1-4]: " choice

case $choice in
    1)
        log_warning "⚠️  这将删除所有现有数据！"
        read -p "确认要重置数据库吗？(y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            log_info "重置数据库..."
            docker-compose exec -T app npx prisma migrate reset --force
            log_success "数据库重置完成"
        else
            log_info "操作已取消"
            exit 0
        fi
        ;;
    2)
        log_info "标记现有数据库为基线..."
        # 获取最新的迁移文件
        LATEST_MIGRATION=$(docker-compose exec -T app find prisma/migrations -name "*.sql" | tail -1 | xargs basename -s .sql)
        if [[ -n "$LATEST_MIGRATION" ]]; then
            docker-compose exec -T app npx prisma migrate resolve --applied "$LATEST_MIGRATION"
            log_success "数据库已标记为基线"
        else
            log_error "未找到迁移文件"
            exit 1
        fi
        ;;
    3)
        log_info "强制部署迁移..."
        docker-compose exec -T app npx prisma migrate deploy --force
        log_success "迁移部署完成"
        ;;
    4)
        log_info "生成Prisma客户端..."
        docker-compose exec -T app npx prisma generate
        log_success "Prisma客户端生成完成"
        ;;
    *)
        log_error "无效选择"
        exit 1
        ;;
esac

# 生成Prisma客户端（如果还没生成）
if [[ $choice != "4" ]]; then
    log_info "生成Prisma客户端..."
    docker-compose exec -T app npx prisma generate
    log_success "Prisma客户端生成完成"
fi

# 测试数据库连接
log_info "测试数据库连接..."
if docker-compose exec -T app npx prisma db pull --preview-feature >/dev/null 2>&1; then
    log_success "✅ 数据库连接测试通过"
else
    log_warning "⚠️ 数据库连接测试失败"
fi

# 重启应用容器
log_info "重启应用容器..."
docker-compose restart app

# 等待应用启动
log_info "等待应用启动..."
sleep 10

# 健康检查
log_info "执行健康检查..."
for i in {1..5}; do
    if curl -s http://localhost:3000/api/health >/dev/null; then
        log_success "✅ 应用健康检查通过"
        break
    fi
    if [[ $i -eq 5 ]]; then
        log_warning "⚠️ 应用健康检查失败"
        echo "请检查应用日志: docker-compose logs app"
    else
        log_info "等待应用启动... ($i/5)"
        sleep 5
    fi
done

echo ""
log_success "🎉 数据库迁移修复完成！"
echo ""
echo "📋 后续操作："
echo "  查看应用状态: docker-compose ps"
echo "  查看应用日志: docker-compose logs -f app"
echo "  访问应用: http://localhost:3000"
echo "  健康检查: curl http://localhost:3000/api/health"
