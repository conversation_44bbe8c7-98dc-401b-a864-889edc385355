#!/bin/bash

# OCTI智能评估系统 - 完整版部署修复脚本
# 专门修复云端完整版部署的问题

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

echo "🔧 OCTI完整版部署修复脚本"
echo "========================"

# 1. 停止所有服务
log_info "1. 停止所有服务..."
docker-compose -f docker-compose.full.yml down 2>/dev/null || true
docker-compose down 2>/dev/null || true

# 2. 检查并修复目录权限
log_info "2. 修复目录权限..."

# 创建数据目录
mkdir -p data/{postgres,redis,prometheus,grafana}
mkdir -p logs

# 修复Grafana权限问题
log_info "修复Grafana权限..."
sudo chown -R 472:472 data/grafana
sudo chmod -R 755 data/grafana

# 修复Prometheus权限问题
log_info "修复Prometheus权限..."
sudo chown -R 65534:65534 data/prometheus
sudo chmod -R 755 data/prometheus

# 确保配置文件存在
log_info "检查配置文件..."
if [[ ! -f "docker/prometheus/prometheus.yml" ]]; then
    log_error "缺少Prometheus配置文件"
    exit 1
fi

if [[ ! -f "docker/grafana/provisioning/datasources/prometheus.yml" ]]; then
    log_error "缺少Grafana数据源配置文件"
    exit 1
fi

log_success "目录权限修复完成"

# 3. 修复应用健康检查问题
log_info "3. 修复应用健康检查..."

# 检查curl是否在容器中可用
log_info "确保应用容器包含curl..."

# 4. 清理旧镜像和容器
log_info "4. 清理旧镜像和容器..."
docker system prune -f
docker rmi octi-production:latest 2>/dev/null || true

# 5. 重新构建镜像
log_info "5. 重新构建镜像..."
docker-compose -f docker-compose.full.yml build --no-cache

# 6. 启动基础服务
log_info "6. 启动基础服务..."
docker-compose -f docker-compose.full.yml up -d postgres redis

# 等待数据库启动
log_info "等待数据库启动..."
sleep 30

# 检查数据库连接
if docker-compose -f docker-compose.full.yml exec -T postgres pg_isready -U postgres -d octi; then
    log_success "数据库连接正常"
else
    log_error "数据库连接失败"
    exit 1
fi

# 7. 启动应用服务
log_info "7. 启动应用服务..."
docker-compose -f docker-compose.full.yml up -d app

# 等待应用启动
log_info "等待应用启动..."
sleep 60

# 检查应用健康
for i in {1..10}; do
    if docker-compose -f docker-compose.full.yml exec -T app curl -f http://localhost:3000/api/health > /dev/null 2>&1; then
        log_success "应用启动成功"
        break
    fi
    if [[ $i -eq 10 ]]; then
        log_error "应用启动失败"
        echo "应用日志:"
        docker-compose -f docker-compose.full.yml logs --tail 20 app
        exit 1
    else
        log_info "等待应用启动... ($i/10)"
        sleep 10
    fi
done

# 8. 启动Nginx
log_info "8. 启动Nginx..."
docker-compose -f docker-compose.full.yml up -d nginx

sleep 10

# 9. 启动监控服务
log_info "9. 启动监控服务..."
docker-compose -f docker-compose.full.yml up -d prometheus grafana

# 等待监控服务启动
sleep 30

# 10. 最终健康检查
log_info "10. 执行最终健康检查..."

echo ""
echo "📊 容器状态:"
docker-compose -f docker-compose.full.yml ps

echo ""
echo "🏥 健康检查结果:"

# 检查应用
if curl -s http://localhost:3000/api/health > /dev/null; then
    log_success "✅ 应用服务正常"
else
    log_warning "⚠️ 应用服务异常"
fi

# 检查Nginx
if curl -s http://localhost/health > /dev/null; then
    log_success "✅ Nginx服务正常"
else
    log_warning "⚠️ Nginx服务异常"
fi

# 检查Prometheus
if curl -s http://localhost:9090/-/healthy > /dev/null; then
    log_success "✅ Prometheus服务正常"
else
    log_warning "⚠️ Prometheus服务异常"
fi

# 检查Grafana
if curl -s http://localhost:3001/api/health > /dev/null; then
    log_success "✅ Grafana服务正常"
else
    log_warning "⚠️ Grafana服务异常"
fi

echo ""
echo "🌐 服务访问地址:"
SERVER_IP=$(curl -s ifconfig.me 2>/dev/null || echo 'localhost')
echo "  主应用: http://$SERVER_IP:3000"
echo "  Nginx代理: http://$SERVER_IP"
echo "  Prometheus: http://$SERVER_IP:9090"
echo "  Grafana: http://$SERVER_IP:3001 (admin/admin123)"

echo ""
echo "📋 管理命令:"
echo "  查看状态: docker-compose -f docker-compose.full.yml ps"
echo "  查看日志: docker-compose -f docker-compose.full.yml logs -f [service]"
echo "  重启服务: docker-compose -f docker-compose.full.yml restart [service]"
echo "  停止服务: docker-compose -f docker-compose.full.yml down"

echo ""
log_success "✅ 完整版部署修复完成！"
