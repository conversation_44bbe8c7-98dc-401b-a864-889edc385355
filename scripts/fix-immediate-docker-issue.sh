#!/bin/bash

# 立即修复Docker镜像源问题
# 解决Dockerfile.optimized和Docker配置问题

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

echo "🚨 立即修复Docker镜像源问题"
echo "=========================="

# 1. 重置Docker配置为腾讯云镜像源
log_info "1. 重置Docker配置为腾讯云镜像源..."

sudo tee /etc/docker/daemon.json > /dev/null << 'EOF'
{
  "registry-mirrors": [
    "https://mirror.ccs.tencentyun.com"
  ],
  "log-driver": "json-file",
  "log-opts": {
    "max-size": "100m",
    "max-file": "3"
  },
  "storage-driver": "overlay2",
  "dns": ["8.8.8.8", "114.114.114.114"]
}
EOF

sudo systemctl daemon-reload
sudo systemctl restart docker
sleep 10

log_success "Docker配置已重置为腾讯云镜像源"

# 2. 删除可能存在的Dockerfile.optimized
log_info "2. 清理可能存在的旧Dockerfile文件..."
rm -f Dockerfile.optimized Dockerfile.backup Dockerfile.fixed 2>/dev/null || true

# 3. 确保使用正确的Dockerfile
log_info "3. 检查Dockerfile内容..."
if grep -q "mirror.ccs.tencentyun.com" Dockerfile; then
    log_success "Dockerfile已使用腾讯云镜像源"
else
    log_error "Dockerfile未使用腾讯云镜像源，需要修复"
    exit 1
fi

# 4. 预拉取必要镜像
log_info "4. 预拉取必要镜像..."

IMAGES=(
    "mirror.ccs.tencentyun.com/library/node:20-alpine"
    "mirror.ccs.tencentyun.com/library/node:20-slim"
    "mirror.ccs.tencentyun.com/library/postgres:15-alpine"
    "mirror.ccs.tencentyun.com/library/redis:7-alpine"
)

for image in "${IMAGES[@]}"; do
    log_info "拉取 $image..."
    if timeout 120 docker pull "$image"; then
        log_success "$image 拉取成功"
    else
        log_warning "$image 拉取失败"
    fi
done

# 5. 清理旧的构建缓存
log_info "5. 清理旧的构建缓存..."
docker system prune -f
docker rmi octi-production:latest 2>/dev/null || true

# 6. 使用正确的Dockerfile构建
log_info "6. 使用正确的Dockerfile构建..."

if docker-compose build --no-cache app; then
    log_success "构建成功！"
else
    log_error "构建失败"
    
    # 显示详细错误信息
    echo ""
    echo "📋 诊断信息:"
    echo "Docker配置:"
    cat /etc/docker/daemon.json
    
    echo ""
    echo "可用镜像:"
    docker images | grep "mirror.ccs.tencentyun.com" || echo "无腾讯云镜像"
    
    echo ""
    echo "网络测试:"
    curl -s --connect-timeout 5 https://mirror.ccs.tencentyun.com || echo "腾讯云镜像源连接失败"
    
    exit 1
fi

# 7. 启动服务
log_info "7. 启动服务..."
docker-compose up -d

# 8. 等待服务启动
log_info "8. 等待服务启动..."
sleep 30

# 9. 健康检查
log_info "9. 健康检查..."
for i in {1..5}; do
    if curl -s http://localhost:3000/api/health >/dev/null; then
        log_success "✅ 应用启动成功"
        break
    fi
    if [[ $i -eq 5 ]]; then
        log_warning "⚠️ 应用启动可能有问题"
        echo "查看日志: docker-compose logs app"
    else
        log_info "等待应用启动... ($i/5)"
        sleep 10
    fi
done

echo ""
echo "📊 服务状态:"
docker-compose ps

echo ""
log_success "🎉 修复完成！"

echo ""
echo "📋 后续操作:"
echo "  查看日志: docker-compose logs -f app"
echo "  访问应用: http://localhost:3000"
echo "  健康检查: curl http://localhost:3000/api/health"
