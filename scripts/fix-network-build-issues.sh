#!/bin/bash

# OCTI网络构建问题修复脚本
# 专门解决Ubuntu云服务器Docker构建网络超时问题

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

echo "🔧 OCTI网络构建问题修复脚本"
echo "=========================="

# 1. 检测系统信息
log_info "1. 检测系统信息..."
echo "系统版本: $(lsb_release -d 2>/dev/null | cut -f2 || echo 'Unknown')"
echo "内核版本: $(uname -r)"
echo "Docker版本: $(docker --version)"

# 2. 测试网络连通性
log_info "2. 测试网络连通性..."

test_connectivity() {
    local name="$1"
    local url="$2"
    
    if timeout 10 curl -s --connect-timeout 5 "$url" >/dev/null 2>&1; then
        log_success "$name: 连通正常"
        return 0
    else
        log_warning "$name: 连通失败"
        return 1
    fi
}

# 测试各个镜像源
MIRRORS=(
    "阿里云:https://mirrors.aliyun.com"
    "腾讯云:https://mirrors.tencent.com"
    "清华:https://mirrors.ustc.edu.cn"
    "网易:https://mirrors.163.com"
)

WORKING_MIRRORS=()
for mirror in "${MIRRORS[@]}"; do
    IFS=':' read -r name url <<< "$mirror"
    if test_connectivity "$name" "$url"; then
        WORKING_MIRRORS+=("$name:$url")
    fi
done

if [[ ${#WORKING_MIRRORS[@]} -eq 0 ]]; then
    log_error "所有镜像源都无法连接！"
    echo "请检查网络连接或防火墙设置"
    exit 1
fi

log_success "可用镜像源: ${#WORKING_MIRRORS[@]}个"

# 3. 配置Docker镜像加速器
log_info "3. 配置Docker镜像加速器..."

# 备份原配置
if [[ -f "/etc/docker/daemon.json" ]]; then
    sudo cp /etc/docker/daemon.json /etc/docker/daemon.json.backup.$(date +%Y%m%d_%H%M%S)
fi

# 创建优化的Docker配置
cat > /tmp/daemon.json << 'EOF'
{
  "registry-mirrors": [
    "https://docker.mirrors.ustc.edu.cn",
    "https://hub-mirror.c.163.com",
    "https://mirror.baidubce.com"
  ],
  "log-driver": "json-file",
  "log-opts": {
    "max-size": "100m",
    "max-file": "3"
  },
  "storage-driver": "overlay2",
  "max-concurrent-downloads": 10,
  "max-concurrent-uploads": 5,
  "dns": ["8.8.8.8", "114.114.114.114"]
}
EOF

sudo mv /tmp/daemon.json /etc/docker/daemon.json
sudo systemctl daemon-reload
sudo systemctl restart docker

log_success "Docker配置已优化"

# 4. 测试Docker网络
log_info "4. 测试Docker网络..."
sleep 5

if docker run --rm alpine ping -c 2 mirrors.aliyun.com >/dev/null 2>&1; then
    log_success "Docker网络测试通过"
else
    log_warning "Docker网络测试失败"
fi

# 5. 创建网络优化的构建脚本
log_info "5. 创建网络优化的构建脚本..."

cat > /tmp/build-with-retry.sh << 'EOF'
#!/bin/bash

# 带重试的Docker构建脚本
set -e

MAX_RETRIES=3
RETRY_COUNT=0

build_with_retry() {
    while [[ $RETRY_COUNT -lt $MAX_RETRIES ]]; do
        echo "🔄 构建尝试 $((RETRY_COUNT + 1))/$MAX_RETRIES..."
        
        if timeout 1800 docker-compose build --no-cache app; then
            echo "✅ 构建成功！"
            return 0
        else
            RETRY_COUNT=$((RETRY_COUNT + 1))
            if [[ $RETRY_COUNT -lt $MAX_RETRIES ]]; then
                echo "⚠️ 构建失败，30秒后重试..."
                sleep 30
                
                # 清理失败的构建缓存
                docker system prune -f
            else
                echo "❌ 构建失败，已达到最大重试次数"
                return 1
            fi
        fi
    done
}

# 执行构建
build_with_retry
EOF

chmod +x /tmp/build-with-retry.sh
mv /tmp/build-with-retry.sh ./build-with-retry.sh

log_success "网络优化构建脚本已创建: ./build-with-retry.sh"

# 6. 提供离线包方案
log_info "6. 准备离线包方案..."

cat > /tmp/prepare-offline-packages.sh << 'EOF'
#!/bin/bash

# 离线包准备脚本
echo "📦 准备离线包..."

# 创建离线包目录
mkdir -p deb-packages

# 下载必要的包
cd deb-packages

# 对于Ubuntu/Debian系统
if command -v apt-get >/dev/null; then
    apt-get download \
        openssl \
        ca-certificates \
        libssl3 \
        dumb-init \
        curl \
        2>/dev/null || echo "部分包下载失败，请手动下载"
fi

echo "离线包准备完成，请将deb-packages目录复制到项目根目录"
echo "然后在Dockerfile中添加："
echo "COPY deb-packages /tmp/deb-packages"
echo "RUN dpkg -i /tmp/deb-packages/*.deb || apt-get install -f -y"
EOF

chmod +x /tmp/prepare-offline-packages.sh
mv /tmp/prepare-offline-packages.sh ./prepare-offline-packages.sh

log_success "离线包准备脚本已创建: ./prepare-offline-packages.sh"

# 7. 显示解决方案总结
echo ""
echo "🎯 网络问题解决方案总结:"
echo "========================"
echo ""
echo "✅ 已完成的优化:"
echo "  1. Docker镜像加速器配置"
echo "  2. DNS优化配置"
echo "  3. 并发下载优化"
echo "  4. Dockerfile镜像源优化"
echo ""
echo "🚀 推荐的构建方法:"
echo "  方法1: 使用重试构建脚本"
echo "    ./build-with-retry.sh"
echo ""
echo "  方法2: 手动构建（如果网络稳定）"
echo "    docker-compose build --no-cache app"
echo ""
echo "  方法3: 离线包方案（网络极差时）"
echo "    ./prepare-offline-packages.sh"
echo "    # 然后修改Dockerfile使用离线包"
echo ""
echo "💡 其他建议:"
echo "  - 在网络较好的时间段进行构建"
echo "  - 考虑使用预构建的基础镜像"
echo "  - 定期清理Docker缓存: docker system prune -f"

echo ""
log_success "🎉 网络问题修复完成！"
