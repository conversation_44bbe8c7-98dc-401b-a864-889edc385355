#!/bin/bash

# OCTI Prisma权限修复脚本
# 专门解决Prisma客户端权限问题和数据库迁移问题

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

echo "🔧 OCTI Prisma权限修复脚本"
echo "========================="

# 检查Docker Compose文件
COMPOSE_FILE="docker-compose.yml"
if [[ ! -f "$COMPOSE_FILE" ]]; then
    log_error "未找到 $COMPOSE_FILE 文件"
    exit 1
fi

# 1. 停止应用容器
log_info "1. 停止应用容器..."
docker-compose stop app 2>/dev/null || true

# 2. 检查数据库连接
log_info "2. 检查数据库连接..."
if ! docker-compose ps postgres | grep -q "Up"; then
    log_info "启动数据库服务..."
    docker-compose up -d postgres redis
    sleep 15
fi

# 等待数据库就绪
log_info "等待数据库就绪..."
for i in {1..30}; do
    if docker-compose exec -T postgres pg_isready -U postgres -d octi >/dev/null 2>&1; then
        log_success "数据库连接正常"
        break
    fi
    if [[ $i -eq 30 ]]; then
        log_error "数据库连接超时"
        exit 1
    fi
    sleep 2
done

# 3. 重新构建应用镜像（修复权限问题）
log_info "3. 重新构建应用镜像..."
docker-compose build --no-cache app

# 4. 启动应用容器（临时以root用户运行）
log_info "4. 启动应用容器进行权限修复..."
docker-compose run --rm --user root app sh -c "
    echo '修复Prisma客户端权限...'
    
    # 清理旧的Prisma客户端
    rm -rf /app/node_modules/.prisma/client 2>/dev/null || true
    rm -rf /app/node_modules/@prisma/client 2>/dev/null || true
    
    # 重新安装Prisma客户端
    npm install @prisma/client
    
    # 生成Prisma客户端
    npx prisma generate
    
    # 设置正确的权限
    chown -R nextjs:nodejs /app/node_modules/.prisma
    chown -R nextjs:nodejs /app/node_modules/@prisma
    chmod -R 755 /app/node_modules/.prisma
    chmod -R 755 /app/node_modules/@prisma
    
    echo 'Prisma客户端权限修复完成'
"

# 5. 处理数据库迁移
log_info "5. 处理数据库迁移..."

# 检查是否需要迁移
if docker-compose run --rm app npx prisma migrate status | grep -q "Database schema is up to date"; then
    log_success "数据库模式已是最新"
else
    log_warning "数据库迁移失败，可能是数据库已存在数据"
    log_info "尝试标记现有数据库为基线..."
    
    # 尝试标记为基线
    if docker-compose run --rm app npx prisma db push --accept-data-loss 2>/dev/null; then
        log_success "数据库模式同步完成"
    else
        log_warning "无法自动处理迁移，将跳过迁移步骤"
    fi
fi

# 6. 生成Prisma客户端
log_info "6. 生成Prisma客户端..."
if docker-compose run --rm app npx prisma generate; then
    log_success "Prisma客户端生成完成"
else
    log_warning "Prisma客户端生成失败"
fi

# 7. 检查SSL兼容性
log_info "7. 检查SSL兼容性..."
if ! docker-compose run --rm app ls /usr/lib/*/libssl.so.1.1 >/dev/null 2>&1; then
    log_warning "SSL 1.1兼容性库缺失，尝试修复..."
    docker-compose run --rm --user root app sh -c "
        ln -sf /usr/lib/x86_64-linux-gnu/libssl.so.3 /usr/lib/x86_64-linux-gnu/libssl.so.1.1 2>/dev/null || \
        ln -sf /usr/lib/aarch64-linux-gnu/libssl.so.3 /usr/lib/aarch64-linux-gnu/libssl.so.1.1 2>/dev/null || \
        ln -sf /lib/x86_64-linux-gnu/libssl.so.3 /lib/x86_64-linux-gnu/libssl.so.1.1 2>/dev/null || \
        ln -sf /lib/aarch64-linux-gnu/libssl.so.3 /lib/aarch64-linux-gnu/libssl.so.1.1 2>/dev/null || \
        true
    "
    log_success "SSL兼容性修复完成"
fi

# 8. 重启应用容器
log_info "8. 重启应用容器..."
docker-compose up -d app

# 9. 等待应用启动
log_info "9. 等待应用启动..."
sleep 30

# 10. 健康检查
log_info "10. 执行健康检查..."
for i in {1..10}; do
    log_info "健康检查尝试 $i/10..."
    if curl -s http://localhost:3000/api/health >/dev/null 2>&1; then
        log_success "✅ 应用健康检查通过"
        break
    fi
    if [[ $i -eq 10 ]]; then
        log_error "❌ 应用健康检查失败"
        echo ""
        echo "📝 错误诊断信息:"
        echo "应用日志:"
        docker-compose logs --tail 20 app
        echo ""
        echo "容器状态:"
        docker-compose ps
        exit 1
    else
        sleep 10
    fi
done

echo ""
log_success "🎉 Prisma权限修复完成！"
echo ""
echo "📋 服务状态:"
docker-compose ps
echo ""
echo "📋 后续操作："
echo "  查看应用日志: docker-compose logs -f app"
echo "  访问应用: http://localhost:3000"
echo "  健康检查: curl http://localhost:3000/api/health"
