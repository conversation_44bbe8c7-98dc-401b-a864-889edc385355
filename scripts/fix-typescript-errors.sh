#!/bin/bash

# OCTI TypeScript错误系统性修复脚本
# 一次性修复所有TypeScript编译错误

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

echo "🔧 OCTI TypeScript错误系统性修复"
echo "=================================="

# 1. 修复模型名称错误
fix_model_names() {
    log_info "修复所有模型名称错误..."
    
    # 搜索并修复所有错误的模型名称
    find src -name "*.ts" -o -name "*.tsx" | while read file; do
        if grep -q "minimax-M1" "$file" 2>/dev/null; then
            log_warning "修复文件: $file"
            sed -i.bak "s/'minimax-M1'/'MiniMax-M1'/g" "$file"
            sed -i.bak 's/"minimax-M1"/"MiniMax-M1"/g' "$file"
            rm -f "$file.bak"
        fi
    done
    
    # 检查是否还有错误的模型名称
    local remaining=$(find src -name "*.ts" -o -name "*.tsx" | xargs grep -l "minimax-M1" 2>/dev/null | wc -l)
    if [ "$remaining" -eq 0 ]; then
        log_success "所有模型名称已修复"
    else
        log_error "仍有 $remaining 个文件包含错误的模型名称"
    fi
}

# 2. 修复类属性初始化问题
fix_class_properties() {
    log_info "修复类属性初始化问题..."
    
    # 常见的未初始化属性模式
    local files_to_check=(
        "src/services/contextual-prompt-generator.ts"
        "src/services/evaluation-angle-matrix.ts"
        "src/services/agents/organization-mentor.ts"
        "src/services/agents/question-designer.ts"
    )
    
    for file in "${files_to_check[@]}"; do
        if [[ -f "$file" ]]; then
            log_info "检查文件: $file"
            
            # 修复Map类型的未初始化属性
            if grep -q "private.*Map<.*>;" "$file"; then
                log_warning "修复Map属性初始化: $file"
                sed -i.bak 's/private \([^:]*\): Map<\([^>]*\)>;/private \1: Map<\2> = new Map();/g' "$file"
                rm -f "$file.bak"
            fi
            
            # 修复其他常见的未初始化属性
            if grep -q "private.*\[\];" "$file"; then
                log_warning "修复数组属性初始化: $file"
                sed -i.bak 's/private \([^:]*\): \([^[]*\)\[\];/private \1: \2[] = [];/g' "$file"
                rm -f "$file.bak"
            fi
        fi
    done
}

# 3. 修复类型注解问题
fix_type_annotations() {
    log_info "修复类型注解问题..."
    
    # 修复reduce函数的类型注解
    find src -name "*.ts" | while read file; do
        if grep -q "\.reduce(" "$file" 2>/dev/null; then
            # 检查是否缺少类型注解
            if grep -q "\.reduce((sum, " "$file" && ! grep -q "\.reduce((sum: " "$file"; then
                log_warning "修复reduce类型注解: $file"
                # 这里需要手动处理，因为自动替换可能不准确
                log_info "  需要手动检查: $file"
            fi
        fi
    done
}

# 4. 修复接口属性不匹配问题
fix_interface_mismatches() {
    log_info "修复接口属性不匹配问题..."
    
    # 修复Question接口相关的属性使用
    find src -name "*.ts" | while read file; do
        if grep -q "\.text" "$file" 2>/dev/null; then
            # 检查是否在Question类型的上下文中使用了.text而不是.title
            if grep -B5 -A5 "\.text" "$file" | grep -q "Question\|question"; then
                log_warning "可能的接口属性不匹配: $file"
                log_info "  需要检查是否应该使用.title而不是.text"
            fi
        fi
    done
}

# 5. 生成修复报告
generate_fix_report() {
    log_info "生成修复报告..."
    
    local report_file="typescript-fix-report.md"
    
    cat > "$report_file" << EOF
# TypeScript错误修复报告

**修复时间:** $(date)

## 修复内容

### 1. 模型名称修复
- 搜索并替换所有 'minimax-M1' 为 'MiniMax-M1'
- 确保API调用使用正确的模型名称

### 2. 类属性初始化修复
- 修复Map类型属性的初始化
- 修复数组类型属性的初始化
- 确保所有私有属性都有明确的初始化

### 3. 类型注解修复
- 检查reduce函数的参数类型注解
- 确保所有函数参数都有明确的类型

### 4. 接口属性修复
- 检查Question接口的属性使用
- 确保使用正确的属性名（title vs text）

## 需要手动检查的文件

EOF

    # 添加需要手动检查的文件列表
    find src -name "*.ts" | while read file; do
        if grep -q "\.reduce((sum, " "$file" && ! grep -q "\.reduce((sum: " "$file"; then
            echo "- $file (reduce函数类型注解)" >> "$report_file"
        fi
        if grep -B5 -A5 "\.text" "$file" | grep -q "Question\|question"; then
            echo "- $file (Question接口属性)" >> "$report_file"
        fi
    done
    
    log_success "修复报告已生成: $report_file"
}

# 6. 验证修复效果
verify_fixes() {
    log_info "验证修复效果..."
    
    # 检查模型名称
    local incorrect_models=$(find src -name "*.ts" -o -name "*.tsx" | xargs grep -l "minimax-M1" 2>/dev/null | wc -l)
    log_info "错误模型名称文件数: $incorrect_models"
    
    # 检查正确模型名称
    local correct_models=$(find src -name "*.ts" -o -name "*.tsx" | xargs grep -l "MiniMax-M1" 2>/dev/null | wc -l)
    log_info "正确模型名称文件数: $correct_models"
    
    if [ "$incorrect_models" -eq 0 ] && [ "$correct_models" -gt 0 ]; then
        log_success "模型名称修复验证通过"
    else
        log_error "模型名称修复验证失败"
    fi
}

# 主函数
main() {
    echo "开始系统性修复TypeScript错误..."
    echo ""
    
    fix_model_names
    fix_class_properties
    fix_type_annotations
    fix_interface_mismatches
    generate_fix_report
    verify_fixes
    
    echo ""
    log_success "🎉 TypeScript错误系统性修复完成！"
    echo ""
    log_info "📋 下一步："
    echo "   1. 检查生成的修复报告"
    echo "   2. 手动修复报告中列出的文件"
    echo "   3. 重新构建应用验证修复效果"
    echo "   4. 运行测试确保功能正常"
}

main "$@"
