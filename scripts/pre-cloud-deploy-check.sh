#!/bin/bash

# OCTI云端部署前检查脚本
# 在上传到云端之前进行本地检查

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

echo "🔍 OCTI云端部署前检查"
echo "===================="

# 1. 检查必要文件
log_info "1. 检查必要文件..."
required_files=(
    "scripts/deploy-tencent-cloud-octi.sh"
    "Dockerfile"
    "docker-compose.yml"
    "package.json"
    "prisma/schema.prisma"
    ".env.example"
)

missing_files=()
for file in "${required_files[@]}"; do
    if [[ ! -f "$file" ]]; then
        missing_files+=("$file")
    fi
done

if [[ ${#missing_files[@]} -gt 0 ]]; then
    log_error "缺少必要文件: ${missing_files[*]}"
    exit 1
fi

log_success "必要文件检查通过"

# 2. 检查Docker配置
log_info "2. 检查Docker配置..."

# 检查Dockerfile中的SSL配置
if grep -q "libssl1.1" Dockerfile; then
    log_error "Dockerfile中仍使用libssl1.1，应该使用libssl3"
    exit 1
fi

if grep -q "debian-openssl-1.1.x" Dockerfile; then
    log_error "Dockerfile中仍使用debian-openssl-1.1.x，应该使用debian-openssl-3.0.x"
    exit 1
fi

log_success "Docker配置检查通过"

# 3. 检查Prisma配置
log_info "3. 检查Prisma配置..."

if grep -q "debian-openssl-1.1.x" prisma/schema.prisma; then
    log_error "Prisma配置中仍使用debian-openssl-1.1.x，应该使用debian-openssl-3.0.x"
    exit 1
fi

log_success "Prisma配置检查通过"

# 4. 检查API参数验证
log_info "4. 检查API参数验证..."

if [[ -f "src/app/api/questionnaire/generate-intelligent/route.ts" ]]; then
    if ! grep -q "profile.*organizationType" src/app/api/questionnaire/generate-intelligent/route.ts; then
        log_warning "API参数验证可能不完整"
    else
        log_success "API参数验证检查通过"
    fi
else
    log_warning "未找到智能生成API文件"
fi

# 5. 检查TypeScript编译
log_info "5. 检查TypeScript编译..."

if command -v npm &> /dev/null; then
    if npm run type-check >/dev/null 2>&1; then
        log_success "TypeScript编译检查通过"
    else
        log_warning "TypeScript编译有警告，但不影响部署"
    fi
else
    log_warning "未安装npm，跳过TypeScript检查"
fi

# 6. 检查环境变量模板
log_info "6. 检查环境变量模板..."

if [[ -f ".env.example" ]]; then
    required_vars=("MINIMAX_API_KEY" "DEEPSEEK_API_KEY" "POSTGRES_PASSWORD")
    missing_vars=()
    
    for var in "${required_vars[@]}"; do
        if ! grep -q "^$var=" .env.example; then
            missing_vars+=("$var")
        fi
    done
    
    if [[ ${#missing_vars[@]} -gt 0 ]]; then
        log_warning "环境变量模板缺少: ${missing_vars[*]}"
    else
        log_success "环境变量模板检查通过"
    fi
else
    log_error "缺少.env.example文件"
    exit 1
fi

# 7. 检查部署脚本
log_info "7. 检查部署脚本..."

if [[ -x "scripts/deploy-tencent-cloud-octi.sh" ]]; then
    log_success "部署脚本可执行"
else
    log_warning "部署脚本没有执行权限，将自动修复"
    chmod +x scripts/deploy-tencent-cloud-octi.sh
fi

# 检查脚本是否包含数据库迁移处理
if grep -q "handle_database_migration" scripts/deploy-tencent-cloud-octi.sh; then
    log_success "部署脚本包含数据库迁移处理"
else
    log_error "部署脚本缺少数据库迁移处理"
    exit 1
fi

# 8. 生成部署清单
log_info "8. 生成部署清单..."

cat > deployment-checklist.md << 'EOF'
# OCTI云端部署清单

## 📋 部署前准备

### 1. 服务器要求
- [ ] Ubuntu 20.04+ 或 CentOS 7+
- [ ] 2GB+ 内存
- [ ] 10GB+ 磁盘空间
- [ ] Docker 20.10+
- [ ] Docker Compose 2.0+

### 2. 网络要求
- [ ] 开放端口 80 (HTTP)
- [ ] 开放端口 443 (HTTPS，如果使用域名)
- [ ] 开放端口 3000 (应用，可选)
- [ ] 服务器可访问外网 (下载镜像和API调用)

### 3. 环境变量配置
- [ ] 配置 MINIMAX_API_KEY
- [ ] 配置 DEEPSEEK_API_KEY  
- [ ] 配置 POSTGRES_PASSWORD
- [ ] 配置域名 (可选)

## 🚀 部署步骤

### 1. 上传代码
```bash
# 方式1: 使用scp
scp -r . user@server:/opt/octi/

# 方式2: 使用git
git clone <repository> /opt/octi/
```

### 2. 执行部署
```bash
cd /opt/octi
chmod +x scripts/deploy-tencent-cloud-octi.sh
./scripts/deploy-tencent-cloud-octi.sh --mode simple
```

### 3. 验证部署
```bash
# 检查服务状态
docker-compose ps

# 检查应用健康
curl http://localhost:3000/api/health

# 测试智能生成
curl -X POST http://localhost:3000/api/questionnaire/generate-intelligent \
  -H "Content-Type: application/json" \
  -d '{"profile":{"organizationType":"民间组织"}}'
```

## 🔧 常见问题

### 1. SSL库错误
- 已修复：使用libssl3替代libssl1.1

### 2. 数据库迁移错误
- 已修复：自动处理P3005错误

### 3. Node.js版本不匹配
- 已修复：使用Docker容器中的Node.js v20

### 4. 智能生成502错误
- 已修复：增强API参数验证

## 📞 支持

如遇问题，请检查：
1. 容器日志: `docker-compose logs app`
2. 健康状态: `curl http://localhost:3000/api/health`
3. 数据库连接: `docker-compose exec postgres pg_isready`
EOF

log_success "部署清单已生成: deployment-checklist.md"

echo ""
echo "✅ 云端部署前检查完成！"
echo ""
echo "📋 检查结果总结:"
echo "  ✅ 必要文件完整"
echo "  ✅ Docker配置正确"
echo "  ✅ Prisma配置正确"
echo "  ✅ 部署脚本就绪"
echo ""
echo "🚀 现在可以安全地部署到云端了！"
echo ""
echo "📝 建议的部署命令:"
echo "  ./scripts/deploy-tencent-cloud-octi.sh --mode simple --force-rebuild"
