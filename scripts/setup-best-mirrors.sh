#!/bin/bash

# 最佳Docker镜像源配置脚本
# 基于经验和地理位置的推荐配置

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

echo "🎯 最佳Docker镜像源配置"
echo "======================"

# 检测地理位置（简单方法）
detect_region() {
    local ip_info=$(curl -s --connect-timeout 5 ipinfo.io/country 2>/dev/null || echo "CN")
    echo "$ip_info"
}

# 根据地区推荐镜像源
get_recommended_mirrors() {
    local region="$1"
    
    case "$region" in
        "CN"|"")
            # 中国大陆推荐配置（基于经验）
            echo "阿里云 腾讯云 清华"
            ;;
        "HK"|"TW"|"MO")
            # 港澳台推荐配置
            echo "阿里云 腾讯云 网易"
            ;;
        *)
            # 海外推荐配置
            echo "清华 阿里云 网易"
            ;;
    esac
}

# 镜像源映射
declare -A MIRROR_URLS=(
    ["阿里云"]="https://registry.cn-hangzhou.aliyuncs.com"
    ["网易"]="https://hub-mirror.c.163.com"
    ["百度"]="https://mirror.baidubce.com"
    ["腾讯云"]="https://mirror.ccs.tencentyun.com"
    ["清华"]="https://docker.mirrors.ustc.edu.cn"
)

# 1. 检测地理位置
log_info "1. 检测地理位置..."
REGION=$(detect_region)
log_info "检测到地区: $REGION"

# 2. 获取推荐配置
RECOMMENDED=$(get_recommended_mirrors "$REGION")
log_info "推荐镜像源: $RECOMMENDED"

# 3. 生成配置
log_info "2. 生成最佳配置..."

MIRROR_LIST=()
for name in $RECOMMENDED; do
    if [[ -n "${MIRROR_URLS[$name]}" ]]; then
        MIRROR_LIST+=("${MIRROR_URLS[$name]}")
    fi
done

# 备份原配置
if [[ -f "/etc/docker/daemon.json" ]]; then
    sudo cp /etc/docker/daemon.json /etc/docker/daemon.json.backup.$(date +%Y%m%d_%H%M%S)
    log_info "原配置已备份"
fi

# 生成新配置
cat > /tmp/daemon.json << EOF
{
  "registry-mirrors": [
$(printf '    "%s",\n' "${MIRROR_LIST[@]}" | sed '$ s/,$//')
  ],
  "log-driver": "json-file",
  "log-opts": {
    "max-size": "100m",
    "max-file": "3"
  },
  "storage-driver": "overlay2",
  "max-concurrent-downloads": 10,
  "max-concurrent-uploads": 5
}
EOF

echo ""
echo "📋 推荐配置:"
echo "============"
cat /tmp/daemon.json

echo ""
read -p "是否应用此配置？(Y/n): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Nn]$ ]]; then
    # 应用配置
    sudo mv /tmp/daemon.json /etc/docker/daemon.json
    
    # 重启Docker
    log_info "3. 重启Docker服务..."
    sudo systemctl daemon-reload
    sudo systemctl restart docker
    
    # 等待Docker启动
    sleep 5
    
    log_success "配置已应用！"
    
    # 4. 测试配置
    log_info "4. 测试新配置..."
    
    start_time=$(date +%s)
    if docker pull hello-world:latest >/dev/null 2>&1; then
        end_time=$(date +%s)
        duration=$((end_time - start_time))
        log_success "测试成功！拉取耗时: ${duration}秒"
        
        # 清理测试镜像
        docker rmi hello-world:latest >/dev/null 2>&1 || true
    else
        log_warning "测试失败，可能需要手动调整配置"
    fi
    
    # 5. 显示配置信息
    echo ""
    echo "🎉 配置完成！"
    echo ""
    echo "📊 当前配置:"
    for i in "${!MIRROR_LIST[@]}"; do
        name=""
        url="${MIRROR_LIST[$i]}"
        for mirror_name in "${!MIRROR_URLS[@]}"; do
            if [[ "${MIRROR_URLS[$mirror_name]}" == "$url" ]]; then
                name="$mirror_name"
                break
            fi
        done
        echo "  $((i+1)). $name: $url"
    done
    
else
    log_info "配置已取消"
    rm -f /tmp/daemon.json
fi

echo ""
echo "💡 优化建议:"
echo "  1. 如果速度仍然慢，运行测试脚本: ./scripts/test-docker-mirrors.sh"
echo "  2. 定期清理Docker缓存: docker system prune -f"
echo "  3. 监控网络状况，必要时调整镜像源顺序"

echo ""
echo "🔧 管理命令:"
echo "  查看当前配置: cat /etc/docker/daemon.json"
echo "  重启Docker: sudo systemctl restart docker"
echo "  查看Docker信息: docker info | grep -A 5 'Registry Mirrors'"
