#!/bin/bash

# Docker镜像源配置脚本
# 配置国内镜像源加速Docker构建

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

echo "🔧 Docker镜像源配置脚本"
echo "======================"

# 1. 配置Docker Hub镜像加速
log_info "1. 配置Docker Hub镜像加速..."

# 创建Docker配置目录
sudo mkdir -p /etc/docker

# 配置Docker镜像加速器
cat > /tmp/daemon.json << 'EOF'
{
  "registry-mirrors": [
    "https://docker.mirrors.ustc.edu.cn",
    "https://hub-mirror.c.163.com",
    "https://mirror.baidubce.com"
  ],
  "log-driver": "json-file",
  "log-opts": {
    "max-size": "100m",
    "max-file": "3"
  },
  "storage-driver": "overlay2"
}
EOF

sudo mv /tmp/daemon.json /etc/docker/daemon.json

# 重启Docker服务
log_info "重启Docker服务..."
sudo systemctl daemon-reload
sudo systemctl restart docker

log_success "Docker镜像加速配置完成"

# 2. 测试镜像拉取速度
log_info "2. 测试镜像拉取速度..."

# 测试拉取一个小镜像
start_time=$(date +%s)
if docker pull hello-world:latest >/dev/null 2>&1; then
    end_time=$(date +%s)
    duration=$((end_time - start_time))
    log_success "镜像拉取测试成功，耗时: ${duration}秒"
else
    log_warning "镜像拉取测试失败"
fi

# 3. 显示配置信息
echo ""
echo "📋 当前Docker配置:"
echo "镜像加速器:"
echo "  - https://docker.mirrors.ustc.edu.cn"
echo "  - https://hub-mirror.c.163.com"
echo "  - https://mirror.baidubce.com"

echo ""
echo "📊 Docker信息:"
docker info | grep -A 10 "Registry Mirrors" || echo "未找到镜像加速器信息"

echo ""
log_success "🎉 Docker镜像源配置完成！"

echo ""
echo "💡 使用建议:"
echo "  1. 现在Docker构建会更快"
echo "  2. 如果仍然慢，可以尝试其他镜像源"
echo "  3. 定期清理Docker缓存: docker system prune -f"
