#!/bin/bash

# OCTI TypeScript错误简化扫描器
# 快速发现并修复常见的TypeScript错误

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

echo "🔍 OCTI TypeScript错误快速扫描器"
echo "================================"

# 1. 扫描forEach/map/reduce参数缺少类型注解
scan_array_methods() {
    log_info "扫描数组方法参数类型注解..."
    
    local errors=0
    
    # 扫描forEach
    local foreach_files=$(find src -name "*.ts" -exec grep -l "\.forEach(" {} \; 2>/dev/null || true)
    for file in $foreach_files; do
        local bad_lines=$(grep -n "\.forEach((" "$file" | grep -v ": " | head -5)
        if [ -n "$bad_lines" ]; then
            log_warning "forEach参数缺少类型: $file"
            echo "$bad_lines" | while read line; do
                echo "  $line"
            done
            ((errors++))
        fi
    done
    
    # 扫描reduce
    local reduce_files=$(find src -name "*.ts" -exec grep -l "\.reduce(" {} \; 2>/dev/null || true)
    for file in $reduce_files; do
        local bad_lines=$(grep -n "\.reduce((" "$file" | grep -v ": " | head -5)
        if [ -n "$bad_lines" ]; then
            log_warning "reduce参数缺少类型: $file"
            echo "$bad_lines" | while read line; do
                echo "  $line"
            done
            ((errors++))
        fi
    done
    
    # 扫描map
    local map_files=$(find src -name "*.ts" -exec grep -l "\.map(" {} \; 2>/dev/null || true)
    for file in $map_files; do
        local bad_lines=$(grep -n "\.map((" "$file" | grep -v ": " | head -5)
        if [ -n "$bad_lines" ]; then
            log_warning "map参数缺少类型: $file"
            echo "$bad_lines" | while read line; do
                echo "  $line"
            done
            ((errors++))
        fi
    done
    
    if [ $errors -eq 0 ]; then
        log_success "数组方法参数类型检查通过"
    else
        log_error "发现 $errors 个数组方法类型问题"
    fi
    
    return $errors
}

# 2. 扫描模型名称错误
scan_model_names() {
    log_info "扫描模型名称错误..."
    
    local bad_files=$(find src -name "*.ts" -exec grep -l "minimax-M1" {} \; 2>/dev/null || true)
    
    if [ -n "$bad_files" ]; then
        log_error "发现错误的模型名称:"
        for file in $bad_files; do
            echo "  ❌ $file"
            grep -n "minimax-M1" "$file" | head -3
        done
        return 1
    else
        log_success "模型名称检查通过"
        return 0
    fi
}

# 3. 扫描未初始化属性
scan_uninitialized_props() {
    log_info "扫描未初始化属性..."
    
    local errors=0
    local prop_files=$(find src -name "*.ts" -exec grep -l "private.*:" {} \; 2>/dev/null || true)
    
    for file in $prop_files; do
        # 查找private属性但没有=的行
        local bad_lines=$(grep -n "private.*:.*;" "$file" | grep -v "=" | head -3)
        if [ -n "$bad_lines" ]; then
            log_warning "可能的未初始化属性: $file"
            echo "$bad_lines" | while read line; do
                echo "  $line"
            done
            ((errors++))
        fi
    done
    
    if [ $errors -eq 0 ]; then
        log_success "属性初始化检查通过"
    else
        log_warning "发现 $errors 个可能的未初始化属性"
    fi
    
    return $errors
}

# 4. 快速修复函数
quick_fix() {
    log_info "执行快速修复..."
    
    # 修复模型名称
    local fixed_models=0
    find src -name "*.ts" | while read file; do
        if grep -q "minimax-M1" "$file"; then
            log_warning "修复模型名称: $file"
            sed -i.bak "s/'minimax-M1'/'MiniMax-M1'/g" "$file"
            sed -i.bak 's/"minimax-M1"/"MiniMax-M1"/g' "$file"
            rm -f "$file.bak"
            ((fixed_models++))
        fi
    done
    
    if [ $fixed_models -gt 0 ]; then
        log_success "已修复 $fixed_models 个模型名称错误"
    fi
}

# 5. 生成需要手动修复的文件列表
generate_manual_fix_list() {
    log_info "生成手动修复列表..."
    
    local fix_file="typescript-manual-fixes.txt"
    echo "# TypeScript手动修复列表" > "$fix_file"
    echo "# 生成时间: $(date)" >> "$fix_file"
    echo "" >> "$fix_file"
    
    # 需要手动修复类型注解的文件
    echo "## 需要添加类型注解的文件:" >> "$fix_file"
    
    # forEach文件
    find src -name "*.ts" -exec grep -l "\.forEach(" {} \; 2>/dev/null | while read file; do
        if grep -q "\.forEach((" "$file" && ! grep -q "\.forEach((.*: " "$file"; then
            echo "- $file (forEach参数类型)" >> "$fix_file"
        fi
    done
    
    # reduce文件
    find src -name "*.ts" -exec grep -l "\.reduce(" {} \; 2>/dev/null | while read file; do
        if grep -q "\.reduce((" "$file" && ! grep -q "\.reduce((.*: " "$file"; then
            echo "- $file (reduce参数类型)" >> "$fix_file"
        fi
    done
    
    # map文件
    find src -name "*.ts" -exec grep -l "\.map(" {} \; 2>/dev/null | while read file; do
        if grep -q "\.map((" "$file" && ! grep -q "\.map((.*: " "$file"; then
            echo "- $file (map参数类型)" >> "$fix_file"
        fi
    done
    
    echo "" >> "$fix_file"
    echo "## 修复示例:" >> "$fix_file"
    echo "// 修复前" >> "$fix_file"
    echo ".forEach((item, index) => ...)" >> "$fix_file"
    echo "" >> "$fix_file"
    echo "// 修复后" >> "$fix_file"
    echo ".forEach((item: any, index: number) => ...)" >> "$fix_file"
    
    log_success "手动修复列表已生成: $fix_file"
}

# 6. 显示修复建议
show_fix_suggestions() {
    echo ""
    log_info "📋 修复建议"
    echo "================================"
    echo "1. 🔧 自动修复: bash scripts/simple-ts-scanner.sh --fix"
    echo "2. 📝 手动修复: 查看 typescript-manual-fixes.txt"
    echo "3. ✅ 验证修复: docker compose build app"
    echo ""
    echo "常见修复模式:"
    echo "  .forEach((item, index) => ...)  →  .forEach((item: any, index: number) => ...)"
    echo "  .reduce((sum, item) => ...)     →  .reduce((sum: number, item: any) => ...)"
    echo "  .map((item, index) => ...)      →  .map((item: any, index: number) => ...)"
    echo ""
}

# 主函数
main() {
    local fix_mode=false
    
    # 解析参数
    if [[ "$1" == "--fix" ]]; then
        fix_mode=true
    fi
    
    echo "开始TypeScript错误快速扫描..."
    echo ""
    
    local total_errors=0
    
    # 执行扫描
    scan_array_methods || ((total_errors++))
    scan_model_names || ((total_errors++))
    scan_uninitialized_props || ((total_errors++))
    
    # 生成修复列表
    generate_manual_fix_list
    
    # 如果启用修复模式
    if [ "$fix_mode" = true ]; then
        quick_fix
    fi
    
    # 显示建议
    show_fix_suggestions
    
    echo ""
    if [ $total_errors -gt 0 ]; then
        log_error "❌ 发现 $total_errors 类TypeScript错误"
        echo ""
        log_info "💡 运行自动修复: bash scripts/simple-ts-scanner.sh --fix"
        exit 1
    else
        log_success "🎉 TypeScript错误扫描通过！"
        exit 0
    fi
}

main "$@"
