#!/bin/bash

# Docker镜像源速度测试脚本
# 自动测试并选择最快的镜像源

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

echo "🧪 Docker镜像源速度测试"
echo "======================"

# 定义镜像源列表
declare -A MIRRORS=(
    ["阿里云"]="https://registry.cn-hangzhou.aliyuncs.com"
    ["网易"]="https://hub-mirror.c.163.com"
    ["百度"]="https://mirror.baidubce.com"
    ["腾讯云"]="https://mirror.ccs.tencentyun.com"
    ["清华"]="https://docker.mirrors.ustc.edu.cn"
)

# 测试镜像
TEST_IMAGE="hello-world:latest"

# 结果数组
declare -A RESULTS=()

# 备份原始配置
log_info "备份原始Docker配置..."
if [[ -f "/etc/docker/daemon.json" ]]; then
    sudo cp /etc/docker/daemon.json /etc/docker/daemon.json.backup
fi

# 测试函数
test_mirror() {
    local name="$1"
    local url="$2"
    
    log_info "测试 $name ($url)..."
    
    # 创建临时配置
    cat > /tmp/daemon.json << EOF
{
  "registry-mirrors": ["$url"],
  "log-driver": "json-file",
  "log-opts": {
    "max-size": "100m",
    "max-file": "3"
  }
}
EOF
    
    # 应用配置
    sudo mv /tmp/daemon.json /etc/docker/daemon.json
    sudo systemctl daemon-reload
    sudo systemctl restart docker
    
    # 等待Docker启动
    sleep 5
    
    # 清理镜像
    docker rmi $TEST_IMAGE 2>/dev/null || true
    
    # 测试下载速度
    local start_time=$(date +%s.%N)
    
    if timeout 60 docker pull $TEST_IMAGE >/dev/null 2>&1; then
        local end_time=$(date +%s.%N)
        local duration=$(echo "$end_time - $start_time" | bc -l)
        local duration_int=$(printf "%.2f" $duration)
        
        RESULTS["$name"]="$duration_int"
        log_success "$name: ${duration_int}秒"
    else
        RESULTS["$name"]="TIMEOUT"
        log_error "$name: 超时或失败"
    fi
    
    # 清理测试镜像
    docker rmi $TEST_IMAGE 2>/dev/null || true
}

# 连通性测试函数
test_connectivity() {
    local name="$1"
    local url="$2"
    
    log_info "测试 $name 连通性..."
    
    # 提取主机名
    local host=$(echo $url | sed 's|https\?://||' | sed 's|/.*||')
    
    if timeout 10 curl -s --connect-timeout 5 "$url" >/dev/null 2>&1; then
        log_success "$name: 连通正常"
        return 0
    else
        log_warning "$name: 连通失败"
        return 1
    fi
}

# 1. 首先测试连通性
log_info "1. 测试镜像源连通性..."
echo ""

AVAILABLE_MIRRORS=()
for name in "${!MIRRORS[@]}"; do
    url="${MIRRORS[$name]}"
    if test_connectivity "$name" "$url"; then
        AVAILABLE_MIRRORS+=("$name")
    fi
done

echo ""
log_info "可用镜像源: ${AVAILABLE_MIRRORS[*]}"

if [[ ${#AVAILABLE_MIRRORS[@]} -eq 0 ]]; then
    log_error "没有可用的镜像源！"
    exit 1
fi

# 2. 测试下载速度
log_info "2. 测试下载速度..."
echo ""

for name in "${AVAILABLE_MIRRORS[@]}"; do
    url="${MIRRORS[$name]}"
    test_mirror "$name" "$url"
    echo ""
done

# 3. 分析结果
log_info "3. 测试结果分析..."
echo ""

echo "📊 测试结果汇总:"
echo "=================="

# 排序结果
SORTED_RESULTS=()
for name in "${!RESULTS[@]}"; do
    result="${RESULTS[$name]}"
    if [[ "$result" != "TIMEOUT" ]]; then
        SORTED_RESULTS+=("$result:$name")
    fi
done

# 按速度排序
IFS=$'\n' SORTED_RESULTS=($(sort -n <<<"${SORTED_RESULTS[*]}"))

echo ""
echo "🏆 速度排名:"
for i in "${!SORTED_RESULTS[@]}"; do
    IFS=':' read -r time name <<< "${SORTED_RESULTS[$i]}"
    rank=$((i + 1))
    if [[ $rank -eq 1 ]]; then
        echo "  🥇 $rank. $name: ${time}秒"
    elif [[ $rank -eq 2 ]]; then
        echo "  🥈 $rank. $name: ${time}秒"
    elif [[ $rank -eq 3 ]]; then
        echo "  🥉 $rank. $name: ${time}秒"
    else
        echo "     $rank. $name: ${time}秒"
    fi
done

# 显示失败的镜像源
echo ""
echo "❌ 失败的镜像源:"
for name in "${!RESULTS[@]}"; do
    if [[ "${RESULTS[$name]}" == "TIMEOUT" ]]; then
        echo "     $name: 超时或连接失败"
    fi
done

# 4. 生成推荐配置
if [[ ${#SORTED_RESULTS[@]} -ge 3 ]]; then
    log_info "4. 生成推荐配置..."
    
    # 获取前三名
    TOP3=()
    for i in {0..2}; do
        if [[ $i -lt ${#SORTED_RESULTS[@]} ]]; then
            IFS=':' read -r time name <<< "${SORTED_RESULTS[$i]}"
            TOP3+=("${MIRRORS[$name]}")
        fi
    done
    
    # 生成配置文件
    cat > /tmp/recommended-daemon.json << EOF
{
  "registry-mirrors": [
$(printf '    "%s",\n' "${TOP3[@]}" | sed '$ s/,$//')
  ],
  "log-driver": "json-file",
  "log-opts": {
    "max-size": "100m",
    "max-file": "3"
  },
  "storage-driver": "overlay2"
}
EOF
    
    echo ""
    echo "🎯 推荐配置 (前三名):"
    echo "===================="
    cat /tmp/recommended-daemon.json
    
    echo ""
    read -p "是否应用推荐配置？(y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        sudo mv /tmp/recommended-daemon.json /etc/docker/daemon.json
        sudo systemctl daemon-reload
        sudo systemctl restart docker
        log_success "推荐配置已应用！"
    else
        log_info "配置文件已保存到: /tmp/recommended-daemon.json"
    fi
else
    log_warning "可用镜像源不足3个，无法生成推荐配置"
fi

# 5. 恢复原始配置（如果用户选择不应用推荐配置）
if [[ ! $REPLY =~ ^[Yy]$ ]] && [[ -f "/etc/docker/daemon.json.backup" ]]; then
    log_info "恢复原始配置..."
    sudo mv /etc/docker/daemon.json.backup /etc/docker/daemon.json
    sudo systemctl daemon-reload
    sudo systemctl restart docker
fi

echo ""
log_success "🎉 镜像源测试完成！"

echo ""
echo "💡 使用建议:"
echo "  1. 选择速度最快的前3个镜像源"
echo "  2. 定期重新测试，因为网络状况会变化"
echo "  3. 如果某个镜像源不稳定，可以移除它"
