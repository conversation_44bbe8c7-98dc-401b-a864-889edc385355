#!/bin/bash

# OCTI Markdown符号清理测试脚本
# 验证分析结果渲染器是否正确清理Markdown符号

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

echo "🧹 OCTI Markdown符号清理测试"
echo "================================"

# 检查关键文件
check_files() {
    log_info "检查渲染器文件..."
    
    local files=(
        "src/components/ui/analysis-cards.tsx"
        "src/components/ui/enhanced-analysis-renderer.tsx"
        "src/lib/analysis/block-parser.ts"
        "src/components/ui/advanced-analysis-renderer.tsx"
    )
    
    for file in "${files[@]}"; do
        if [[ ! -f "$file" ]]; then
            log_error "文件不存在: $file"
            return 1
        fi
    done
    
    log_success "渲染器文件检查通过"
}

# 检查清理函数是否存在
check_cleanup_functions() {
    log_info "检查Markdown清理函数..."
    
    # 检查analysis-cards.tsx中的cleanMarkdownSymbols函数
    if grep -q "cleanMarkdownSymbols" src/components/ui/analysis-cards.tsx; then
        log_success "analysis-cards.tsx: cleanMarkdownSymbols函数已添加"
    else
        log_error "analysis-cards.tsx: 缺少cleanMarkdownSymbols函数"
        return 1
    fi
    
    # 检查enhanced-analysis-renderer.tsx中的清理逻辑
    if grep -q "\\*\\*.*\\*\\*" src/components/ui/enhanced-analysis-renderer.tsx; then
        log_success "enhanced-analysis-renderer.tsx: 粗体符号清理已添加"
    else
        log_warning "enhanced-analysis-renderer.tsx: 可能缺少粗体符号清理"
    fi
    
    # 检查block-parser.ts中的清理逻辑
    if grep -q "\\*\\*.*\\*\\*" src/lib/analysis/block-parser.ts; then
        log_success "block-parser.ts: Markdown符号清理已添加"
    else
        log_warning "block-parser.ts: 可能缺少Markdown符号清理"
    fi
}

# 测试清理效果
test_cleanup_effectiveness() {
    log_info "测试Markdown符号清理效果..."
    
    # 创建测试内容
    local test_content="**这是粗体文本** 和 *这是斜体文本* 以及 \`这是代码\` 还有 ~~删除线~~"
    local expected_result="这是粗体文本 和 这是斜体文本 以及 这是代码 还有 删除线"
    
    log_info "测试内容: $test_content"
    log_info "期望结果: $expected_result"
    
    # 模拟清理过程
    local cleaned_content=$(echo "$test_content" | \
        sed 's/\*\*\([^*]*\)\*\*/\1/g' | \
        sed 's/\*\([^*]*\)\*/\1/g' | \
        sed 's/`\([^`]*\)`/\1/g' | \
        sed 's/~~\([^~]*\)~~/\1/g')
    
    log_info "清理结果: $cleaned_content"
    
    if [[ "$cleaned_content" == "$expected_result" ]]; then
        log_success "Markdown符号清理测试通过"
    else
        log_warning "Markdown符号清理可能不完全"
    fi
}

# 检查代码块清理
test_code_block_cleanup() {
    log_info "测试代码块清理..."
    
    # 测试代码块内容
    local test_content='这是正常文本
```javascript
console.log("这是代码块");
```
这是更多正常文本'
    
    log_info "测试代码块清理逻辑..."
    
    # 检查是否有代码块清理逻辑
    if grep -q "```.*```" src/components/ui/analysis-cards.tsx; then
        log_success "代码块清理逻辑已添加"
    else
        log_warning "可能缺少代码块清理逻辑"
    fi
}

# 检查渲染器使用情况
check_renderer_usage() {
    log_info "检查渲染器使用情况..."
    
    # 检查专业版页面使用的渲染器
    if grep -q "AdvancedAnalysisRenderer" src/app/assessment/results/professional/page.tsx; then
        log_success "专业版页面使用AdvancedAnalysisRenderer"
        
        # 检查是否正确传递内容
        if grep -q "content={analysisResult" src/app/assessment/results/professional/page.tsx; then
            log_success "分析结果内容正确传递给渲染器"
        else
            log_warning "分析结果内容传递可能有问题"
        fi
    else
        log_warning "专业版页面可能使用了其他渲染器"
    fi
}

# 生成修复建议
generate_fix_suggestions() {
    echo ""
    log_info "📋 修复建议"
    echo "================================"
    echo "✅ 已完成的修复:"
    echo "   - analysis-cards.tsx: 添加了cleanMarkdownSymbols函数"
    echo "   - enhanced-analysis-renderer.tsx: 增强了Markdown符号清理"
    echo "   - block-parser.ts: 在cleanLine函数中添加了符号清理"
    echo ""
    echo "🔧 清理的符号类型:"
    echo "   - **粗体文本** → 粗体文本"
    echo "   - *斜体文本* → 斜体文本"
    echo "   - \`代码文本\` → 代码文本"
    echo "   - ~~删除线~~ → 删除线"
    echo "   - ```代码块``` → (移除整个代码块)"
    echo "   - [链接文本](url) → 链接文本"
    echo ""
    echo "🚀 验证方法:"
    echo "   1. 重新构建应用: docker-compose build"
    echo "   2. 重启服务: docker-compose up -d"
    echo "   3. 进行一次专业版分析测试"
    echo "   4. 检查分析结果是否还有**或```符号"
    echo ""
    echo "💡 如果问题仍然存在:"
    echo "   1. 检查AI返回的原始内容格式"
    echo "   2. 确认使用的是正确的渲染器"
    echo "   3. 检查浏览器缓存是否已清理"
}

# 主函数
main() {
    echo "开始Markdown符号清理测试..."
    echo ""
    
    check_files || exit 1
    check_cleanup_functions || exit 1
    test_cleanup_effectiveness
    test_code_block_cleanup
    check_renderer_usage
    generate_fix_suggestions
    
    echo ""
    log_success "🎉 Markdown符号清理测试完成！"
    echo ""
    log_info "💡 下一步："
    echo "   1. 重新构建并重启应用"
    echo "   2. 进行专业版分析测试"
    echo "   3. 验证分析结果中的符号是否已清理"
}

main "$@"
