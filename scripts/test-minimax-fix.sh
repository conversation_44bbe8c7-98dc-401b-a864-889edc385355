#!/bin/bash

# OCTI MiniMax修复验证脚本
# 验证MiniMax模型名称修复是否成功

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

echo "🔧 OCTI MiniMax修复验证"
echo "========================"

# 检查文件是否存在
check_files() {
    log_info "检查关键文件..."
    
    local files=(
        "src/services/analysis/professional-analysis-service.ts"
        "src/services/llm/llm-client.ts"
    )
    
    for file in "${files[@]}"; do
        if [[ ! -f "$file" ]]; then
            log_error "文件不存在: $file"
            return 1
        fi
    done
    
    log_success "关键文件检查通过"
}

# 检查模型名称一致性
check_model_names() {
    log_info "检查MiniMax模型名称一致性..."
    
    # 检查professional-analysis-service.ts
    local service_model=$(grep -n "model:" src/services/analysis/professional-analysis-service.ts | grep -o "'[^']*'" | head -1)
    log_info "professional-analysis-service.ts 中的模型名称: $service_model"
    
    # 检查llm-client.ts
    local client_model=$(grep -n "request.model ||" src/services/llm/llm-client.ts | grep -o "'[^']*'" | head -1)
    log_info "llm-client.ts 中的默认模型名称: $client_model"
    
    # 验证一致性
    if [[ "$service_model" == "$client_model" ]]; then
        log_success "模型名称一致: $service_model"
        
        # 验证是否为正确的MiniMax模型名称
        if [[ "$service_model" == "'MiniMax-M1'" ]]; then
            log_success "使用正确的MiniMax模型名称: MiniMax-M1"
        else
            log_warning "模型名称可能不正确: $service_model"
            log_info "期望的模型名称: 'MiniMax-M1'"
        fi
    else
        log_error "模型名称不一致!"
        log_error "professional-analysis-service.ts: $service_model"
        log_error "llm-client.ts: $client_model"
        return 1
    fi
}

# 检查API密钥配置
check_api_keys() {
    log_info "检查API密钥配置..."
    
    if [[ -f ".env" ]]; then
        local minimax_keys=$(grep -c "MINIMAX_API_KEY" .env || echo "0")
        log_info "发现 $minimax_keys 个MiniMax API密钥配置"
        
        if [[ $minimax_keys -gt 0 ]]; then
            log_success "MiniMax API密钥已配置"
        else
            log_warning "未找到MiniMax API密钥配置"
        fi
    else
        log_warning "未找到.env文件"
    fi
}

# 检查Docker容器状态
check_docker_status() {
    log_info "检查Docker容器状态..."
    
    if command -v docker &> /dev/null; then
        local app_container=$(docker ps --filter "name=octi-app" --format "table {{.Names}}\t{{.Status}}" | grep -v NAMES || echo "")
        
        if [[ -n "$app_container" ]]; then
            log_info "应用容器状态:"
            echo "$app_container"
            log_success "应用容器正在运行"
        else
            log_warning "未找到运行中的应用容器"
        fi
    else
        log_warning "Docker未安装或不可用"
    fi
}

# 测试API健康检查
test_api_health() {
    log_info "测试API健康检查..."
    
    local health_url="http://localhost:3000/api/health"
    
    if command -v curl &> /dev/null; then
        if curl -f -s "$health_url" > /dev/null 2>&1; then
            log_success "API健康检查通过"
            
            # 获取健康检查响应
            local response=$(curl -s "$health_url" 2>/dev/null || echo "无响应")
            log_info "健康检查响应: $response"
        else
            log_warning "API健康检查失败"
            log_info "请确保应用正在运行在端口3000"
        fi
    else
        log_warning "curl未安装，跳过API测试"
    fi
}

# 显示修复总结
show_fix_summary() {
    echo ""
    log_info "📋 修复总结"
    echo "========================"
    echo "✅ 修复内容:"
    echo "   - 统一MiniMax模型名称为 'MiniMax-M1'"
    echo "   - 修复了professional-analysis-service.ts中的大小写问题"
    echo "   - 确保与llm-client.ts中的默认模型名称一致"
    echo ""
    echo "🔧 修改的文件:"
    echo "   - src/services/analysis/professional-analysis-service.ts (第188行)"
    echo ""
    echo "📚 相关文档:"
    echo "   - docs/plan-c-implementation-summary.md (已更新)"
    echo "   - docs/fixes/三个问题修复报告.md"
    echo ""
    echo "🚀 云服务器更新方法:"
    echo "   1. Git拉取: git pull origin main"
    echo "   2. 使用更新脚本: ./scripts/deploy-tencent-cloud-octi.sh update"
    echo "   3. 手动重建: docker-compose build && docker-compose up -d"
}

# 主函数
main() {
    echo "开始验证MiniMax修复..."
    echo ""
    
    check_files || exit 1
    check_model_names || exit 1
    check_api_keys
    check_docker_status
    test_api_health
    show_fix_summary
    
    echo ""
    log_success "🎉 MiniMax修复验证完成！"
    echo ""
    log_info "💡 如果API调用仍有问题，请检查："
    echo "   1. API密钥是否正确配置"
    echo "   2. 网络连接是否正常"
    echo "   3. MiniMax服务是否可用"
}

main "$@"
