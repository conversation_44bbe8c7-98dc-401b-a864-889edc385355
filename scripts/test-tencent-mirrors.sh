#!/bin/bash

# 腾讯云镜像源测试脚本
# 验证所有必要镜像是否可用

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

echo "🧪 腾讯云镜像源测试"
echo "=================="

# 定义需要的镜像
IMAGES=(
    "mirror.ccs.tencentyun.com/library/node:20-alpine"
    "mirror.ccs.tencentyun.com/library/node:20-slim"
    "mirror.ccs.tencentyun.com/library/postgres:15-alpine"
    "mirror.ccs.tencentyun.com/library/redis:7-alpine"
    "mirror.ccs.tencentyun.com/library/adminer:latest"
)

# 测试镜像拉取
log_info "测试镜像拉取..."
echo ""

SUCCESS_COUNT=0
TOTAL_COUNT=${#IMAGES[@]}

for image in "${IMAGES[@]}"; do
    log_info "测试 $image..."
    
    if timeout 120 docker pull "$image" >/dev/null 2>&1; then
        log_success "✅ $image 拉取成功"
        SUCCESS_COUNT=$((SUCCESS_COUNT + 1))
    else
        log_error "❌ $image 拉取失败"
    fi
    echo ""
done

# 显示结果
echo "📊 测试结果:"
echo "============"
echo "成功: $SUCCESS_COUNT/$TOTAL_COUNT"

if [[ $SUCCESS_COUNT -eq $TOTAL_COUNT ]]; then
    log_success "🎉 所有镜像都可用！"
    
    echo ""
    echo "📋 现在可以安全构建:"
    echo "  docker-compose build --no-cache app"
    echo "  docker-compose up -d"
    
elif [[ $SUCCESS_COUNT -gt 0 ]]; then
    log_warning "⚠️ 部分镜像可用，可以尝试构建"
    
else
    log_error "❌ 所有镜像都不可用"
    echo ""
    echo "🔧 建议解决方案:"
    echo "  1. 检查网络连接"
    echo "  2. 尝试其他镜像源"
    echo "  3. 联系网络管理员"
fi

# 显示镜像信息
echo ""
echo "📦 已拉取的镜像:"
docker images | grep "mirror.ccs.tencentyun.com" || echo "无腾讯云镜像"

echo ""
echo "💾 磁盘使用情况:"
docker system df
