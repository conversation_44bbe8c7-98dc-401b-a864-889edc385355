#!/bin/bash

# OCTI TypeScript错误全面扫描和修复工具
# 一次性发现并修复所有TypeScript错误

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m'

# 日志函数
log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }
log_scan() { echo -e "${PURPLE}[SCAN]${NC} $1"; }

echo "🔍 OCTI TypeScript错误全面扫描器"
echo "=================================="

# 1. 扫描隐式any类型错误
scan_implicit_any() {
    log_scan "扫描隐式any类型错误..."
    
    local any_errors=()
    
    # 扫描forEach参数
    while IFS= read -r -d '' file; do
        if grep -n "\.forEach(" "$file" 2>/dev/null | grep -v ": " | grep -q "("; then
            local line_nums=$(grep -n "\.forEach(" "$file" | grep -v ": " | cut -d: -f1)
            for line_num in $line_nums; do
                any_errors+=("$file:$line_num:forEach参数缺少类型注解")
            done
        fi
    done < <(find src -name "*.ts" -type f -print0)
    
    # 扫描reduce参数
    while IFS= read -r -d '' file; do
        if grep -n "\.reduce(" "$file" 2>/dev/null | grep -v ": " | grep -q "("; then
            local line_nums=$(grep -n "\.reduce(" "$file" | grep -v ": " | cut -d: -f1)
            for line_num in $line_nums; do
                any_errors+=("$file:$line_num:reduce参数缺少类型注解")
            done
        fi
    done < <(find src -name "*.ts" -type f -print0)
    
    # 扫描map参数
    while IFS= read -r -d '' file; do
        if grep -n "\.map(" "$file" 2>/dev/null | grep -v ": " | grep -q "("; then
            local line_nums=$(grep -n "\.map(" "$file" | grep -v ": " | cut -d: -f1)
            for line_num in $line_nums; do
                any_errors+=("$file:$line_num:map参数缺少类型注解")
            done
        fi
    done < <(find src -name "*.ts" -type f -print0)
    
    # 扫描filter参数
    while IFS= read -r -d '' file; do
        if grep -n "\.filter(" "$file" 2>/dev/null | grep -v ": " | grep -q "("; then
            local line_nums=$(grep -n "\.filter(" "$file" | grep -v ": " | cut -d: -f1)
            for line_num in $line_nums; do
                any_errors+=("$file:$line_num:filter参数缺少类型注解")
            done
        fi
    done < <(find src -name "*.ts" -type f -print0)
    
    if [ ${#any_errors[@]} -gt 0 ]; then
        log_error "发现 ${#any_errors[@]} 个隐式any类型错误:"
        for error in "${any_errors[@]}"; do
            echo "  ❌ $error"
        done
        return 1
    else
        log_success "未发现隐式any类型错误"
        return 0
    fi
}

# 2. 扫描未初始化属性错误
scan_uninitialized_properties() {
    log_scan "扫描未初始化属性错误..."
    
    local prop_errors=()
    
    while IFS= read -r -d '' file; do
        # 查找private属性声明但没有初始化的情况
        while IFS= read -r line; do
            if [[ "$line" =~ private[[:space:]]+[^:]+:[[:space:]]*[^=;]+;$ ]]; then
                local line_num=$(grep -n "$line" "$file" | cut -d: -f1)
                prop_errors+=("$file:$line_num:未初始化的私有属性")
            fi
        done < <(grep "private.*:.*;" "$file" 2>/dev/null | grep -v "=")
    done < <(find src -name "*.ts" -type f -print0)
    
    if [ ${#prop_errors[@]} -gt 0 ]; then
        log_error "发现 ${#prop_errors[@]} 个未初始化属性错误:"
        for error in "${prop_errors[@]}"; do
            echo "  ❌ $error"
        done
        return 1
    else
        log_success "未发现未初始化属性错误"
        return 0
    fi
}

# 3. 扫描接口不匹配错误
scan_interface_mismatches() {
    log_scan "扫描接口不匹配错误..."
    
    local interface_errors=()
    
    # 检查Question接口的text vs title使用
    while IFS= read -r -d '' file; do
        if grep -q "Question\|question" "$file" && grep -q "\.text" "$file"; then
            local line_nums=$(grep -n "\.text" "$file" | cut -d: -f1)
            for line_num in $line_nums; do
                interface_errors+=("$file:$line_num:可能应该使用.title而不是.text")
            done
        fi
    done < <(find src -name "*.ts" -type f -print0)
    
    if [ ${#interface_errors[@]} -gt 0 ]; then
        log_warning "发现 ${#interface_errors[@]} 个潜在接口不匹配:"
        for error in "${interface_errors[@]}"; do
            echo "  ⚠️ $error"
        done
        return 1
    else
        log_success "未发现接口不匹配错误"
        return 0
    fi
}

# 4. 扫描模型名称错误
scan_model_name_errors() {
    log_scan "扫描模型名称错误..."
    
    local model_errors=()
    
    while IFS= read -r -d '' file; do
        if grep -q "minimax-M1" "$file"; then
            local line_nums=$(grep -n "minimax-M1" "$file" | cut -d: -f1)
            for line_num in $line_nums; do
                model_errors+=("$file:$line_num:错误的模型名称minimax-M1")
            done
        fi
    done < <(find src -name "*.ts" -type f -print0)
    
    if [ ${#model_errors[@]} -gt 0 ]; then
        log_error "发现 ${#model_errors[@]} 个模型名称错误:"
        for error in "${model_errors[@]}"; do
            echo "  ❌ $error"
        done
        return 1
    else
        log_success "未发现模型名称错误"
        return 0
    fi
}

# 5. 自动修复函数
auto_fix_errors() {
    log_info "开始自动修复错误..."
    
    # 修复forEach参数类型
    find src -name "*.ts" -type f | while read file; do
        if grep -q "\.forEach(" "$file" && ! grep -q "\.forEach((.*: " "$file"; then
            log_warning "修复forEach参数类型: $file"
            # 这里需要更智能的替换，暂时标记需要手动修复
            echo "需要手动修复: $file (forEach参数类型)" >> typescript-manual-fixes.txt
        fi
    done
    
    # 修复reduce参数类型
    find src -name "*.ts" -type f | while read file; do
        if grep -q "\.reduce(" "$file" && ! grep -q "\.reduce((.*: " "$file"; then
            log_warning "修复reduce参数类型: $file"
            echo "需要手动修复: $file (reduce参数类型)" >> typescript-manual-fixes.txt
        fi
    done
    
    # 修复模型名称
    find src -name "*.ts" -type f | while read file; do
        if grep -q "minimax-M1" "$file"; then
            log_warning "修复模型名称: $file"
            sed -i.bak "s/'minimax-M1'/'MiniMax-M1'/g" "$file"
            sed -i.bak 's/"minimax-M1"/"MiniMax-M1"/g' "$file"
            rm -f "$file.bak"
        fi
    done
}

# 6. 生成修复报告
generate_scan_report() {
    log_info "生成扫描报告..."
    
    local report_file="typescript-scan-report-$(date +%Y%m%d_%H%M%S).md"
    
    cat > "$report_file" << EOF
# TypeScript错误全面扫描报告

**扫描时间:** $(date)

## 扫描结果汇总

EOF

    # 添加扫描结果
    local total_errors=0
    
    if ! scan_implicit_any > /dev/null 2>&1; then
        echo "### ❌ 隐式any类型错误" >> "$report_file"
        echo "发现多个函数参数缺少类型注解的问题" >> "$report_file"
        echo "" >> "$report_file"
        ((total_errors++))
    fi
    
    if ! scan_uninitialized_properties > /dev/null 2>&1; then
        echo "### ❌ 未初始化属性错误" >> "$report_file"
        echo "发现类属性声明但未初始化的问题" >> "$report_file"
        echo "" >> "$report_file"
        ((total_errors++))
    fi
    
    if ! scan_interface_mismatches > /dev/null 2>&1; then
        echo "### ⚠️ 接口不匹配警告" >> "$report_file"
        echo "发现潜在的接口属性使用不一致问题" >> "$report_file"
        echo "" >> "$report_file"
        ((total_errors++))
    fi
    
    if ! scan_model_name_errors > /dev/null 2>&1; then
        echo "### ❌ 模型名称错误" >> "$report_file"
        echo "发现错误的MiniMax模型名称" >> "$report_file"
        echo "" >> "$report_file"
        ((total_errors++))
    fi
    
    echo "## 修复建议" >> "$report_file"
    echo "" >> "$report_file"
    echo "1. 运行自动修复: \`bash scripts/typescript-error-scanner.sh --fix\`" >> "$report_file"
    echo "2. 手动修复复杂类型注解问题" >> "$report_file"
    echo "3. 重新构建验证: \`docker compose build app\`" >> "$report_file"
    echo "" >> "$report_file"
    echo "**总计发现 $total_errors 类错误**" >> "$report_file"
    
    log_success "扫描报告已生成: $report_file"
}

# 主函数
main() {
    local fix_mode=false
    
    # 解析参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            --fix)
                fix_mode=true
                shift
                ;;
            *)
                shift
                ;;
        esac
    done
    
    echo "开始TypeScript错误全面扫描..."
    echo ""
    
    local has_errors=false
    
    # 执行所有扫描
    if ! scan_implicit_any; then has_errors=true; fi
    if ! scan_uninitialized_properties; then has_errors=true; fi
    if ! scan_interface_mismatches; then has_errors=true; fi
    if ! scan_model_name_errors; then has_errors=true; fi
    
    # 生成报告
    generate_scan_report
    
    # 如果启用修复模式
    if [ "$fix_mode" = true ]; then
        auto_fix_errors
    fi
    
    echo ""
    if [ "$has_errors" = true ]; then
        log_error "❌ 扫描发现错误，请查看报告并修复"
        echo ""
        log_info "💡 运行修复命令："
        echo "   bash scripts/typescript-error-scanner.sh --fix"
        exit 1
    else
        log_success "🎉 扫描通过：未发现TypeScript错误！"
        exit 0
    fi
}

main "$@"
