#!/bin/bash

# OCTI关注领域修复验证脚本
# 验证用户指定的关注领域是否被正确使用

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

echo "🔍 OCTI关注领域修复验证"
echo "=========================="

# 1. 检查关键文件是否包含修复代码
check_code_changes() {
    log_info "检查代码修复是否存在..."
    
    # 检查OrganizationProfile接口是否包含userSpecifiedFocusArea
    if grep -q "userSpecifiedFocusArea" src/services/intelligent-question-generator.ts; then
        log_success "✅ OrganizationProfile接口已包含userSpecifiedFocusArea字段"
    else
        log_error "❌ OrganizationProfile接口缺少userSpecifiedFocusArea字段"
        return 1
    fi
    
    # 检查getPrimaryFocusArea方法是否存在
    if grep -q "getPrimaryFocusArea" src/services/intelligent-question-generator.ts; then
        log_success "✅ getPrimaryFocusArea方法已实现"
    else
        log_error "❌ getPrimaryFocusArea方法未找到"
        return 1
    fi
    
    # 检查数据提取逻辑是否包含组织信息获取
    if grep -q "octi_organization_info" src/services/questionnaire-service.ts; then
        log_success "✅ 数据提取逻辑已更新"
    else
        log_error "❌ 数据提取逻辑未更新"
        return 1
    fi
    
    # 检查提示词是否包含严格约束
    if grep -q "严格要求" src/services/intelligent-question-generator.ts; then
        log_success "✅ 提示词约束已强化"
    else
        log_error "❌ 提示词约束未找到"
        return 1
    fi
    
    return 0
}

# 2. 检查Docker容器状态
check_docker_status() {
    log_info "检查Docker容器状态..."
    
    if docker ps | grep -q "octi-app"; then
        log_success "✅ OCTI应用容器正在运行"
        
        # 检查容器构建时间
        local build_time=$(docker inspect octi-app --format='{{.Created}}' 2>/dev/null || echo "unknown")
        log_info "容器构建时间: $build_time"
        
        return 0
    else
        log_error "❌ OCTI应用容器未运行"
        return 1
    fi
}

# 3. 测试API端点
test_api_endpoints() {
    log_info "测试API端点..."
    
    # 检查健康状态
    if curl -s http://localhost:3000/api/health > /dev/null; then
        log_success "✅ API健康检查通过"
    else
        log_error "❌ API健康检查失败"
        return 1
    fi
    
    return 0
}

# 4. 生成测试数据并验证
test_focus_area_logic() {
    log_info "测试关注领域优先级逻辑..."
    
    # 创建测试数据
    local test_data='{
        "organizationName": "测试公益咨询机构",
        "organizationType": "民间组织", 
        "mainFocus": "公益咨询",
        "organizationScale": "中型",
        "location": "北京"
    }'
    
    # 模拟保存到localStorage的数据
    log_info "模拟用户输入: 组织类型=民间组织, 关注领域=公益咨询"
    
    # 检查测试页面是否可访问
    if curl -s http://localhost:3000/test-focus-area > /dev/null; then
        log_success "✅ 测试页面可访问"
        log_info "请访问 http://localhost:3000/test-focus-area 进行手动验证"
    else
        log_warning "⚠️ 测试页面不可访问，请手动测试"
    fi
    
    return 0
}

# 5. 检查日志中的关键信息
check_application_logs() {
    log_info "检查应用日志..."
    
    # 获取最近的应用日志
    local logs=$(docker logs octi-app --tail 50 2>/dev/null || echo "无法获取日志")
    
    if echo "$logs" | grep -q "使用用户指定的关注领域"; then
        log_success "✅ 发现用户关注领域使用日志"
    else
        log_warning "⚠️ 未发现用户关注领域使用日志"
    fi
    
    if echo "$logs" | grep -q "智能问题生成将围绕核心领域"; then
        log_success "✅ 发现智能问题生成日志"
    else
        log_warning "⚠️ 未发现智能问题生成日志"
    fi
    
    return 0
}

# 主函数
main() {
    local total_checks=0
    local passed_checks=0
    
    echo "开始验证关注领域修复..."
    echo ""
    
    # 执行所有检查
    if check_code_changes; then ((passed_checks++)); fi; ((total_checks++))
    if check_docker_status; then ((passed_checks++)); fi; ((total_checks++))
    if test_api_endpoints; then ((passed_checks++)); fi; ((total_checks++))
    if test_focus_area_logic; then ((passed_checks++)); fi; ((total_checks++))
    if check_application_logs; then ((passed_checks++)); fi; ((total_checks++))
    
    echo ""
    echo "验证结果汇总:"
    echo "=============="
    echo "总检查项: $total_checks"
    echo "通过检查: $passed_checks"
    echo "通过率: $(( passed_checks * 100 / total_checks ))%"
    
    if [ $passed_checks -eq $total_checks ]; then
        log_success "🎉 所有验证通过！关注领域修复已生效"
        echo ""
        echo "📋 下一步操作："
        echo "1. 访问 http://localhost:3000/test-focus-area 进行详细测试"
        echo "2. 设置测试数据：组织类型=民间组织，关注领域=公益咨询"
        echo "3. 执行智能问题生成测试"
        echo "4. 验证生成的问题是否围绕'公益咨询'而不是医疗"
        exit 0
    else
        log_error "❌ 部分验证失败，可能需要重新构建"
        echo ""
        echo "🔧 建议操作："
        echo "1. 清理Docker缓存: docker system prune -a -f"
        echo "2. 重新构建: ./scripts/docker-deploy.sh build dev --no-cache"
        echo "3. 重新启动: ./scripts/docker-deploy.sh dev"
        echo "4. 再次运行验证: bash scripts/verify-focus-area-fix.sh"
        exit 1
    fi
}

main "$@"
