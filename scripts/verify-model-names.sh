#!/bin/bash

# OCTI MiniMax模型名称验证脚本
# 确保所有文件中的MiniMax模型名称都是正确的大小写

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

echo "🔍 OCTI MiniMax模型名称验证"
echo "================================"

# 检查错误的模型名称（小写m）
check_incorrect_names() {
    log_info "检查错误的模型名称（minimax-M1）..."
    
    local incorrect_files=()
    
    # 搜索所有TypeScript文件中的错误模型名称
    while IFS= read -r -d '' file; do
        if grep -q "minimax-M1" "$file" 2>/dev/null; then
            incorrect_files+=("$file")
        fi
    done < <(find src -name "*.ts" -type f -print0)
    
    if [ ${#incorrect_files[@]} -eq 0 ]; then
        log_success "未发现错误的模型名称"
        return 0
    else
        log_error "发现 ${#incorrect_files[@]} 个文件包含错误的模型名称:"
        for file in "${incorrect_files[@]}"; do
            echo "  ❌ $file"
            grep -n "minimax-M1" "$file" | head -3
        done
        return 1
    fi
}

# 检查正确的模型名称（大写M）
check_correct_names() {
    log_info "检查正确的模型名称（MiniMax-M1）..."
    
    local correct_files=()
    
    # 搜索所有TypeScript文件中的正确模型名称
    while IFS= read -r -d '' file; do
        if grep -q "MiniMax-M1" "$file" 2>/dev/null; then
            correct_files+=("$file")
        fi
    done < <(find src -name "*.ts" -type f -print0)
    
    log_success "发现 ${#correct_files[@]} 个文件使用正确的模型名称:"
    for file in "${correct_files[@]}"; do
        echo "  ✅ $file"
    done
}

# 检查配置文件中的模型名称
check_config_files() {
    log_info "检查配置文件中的模型名称..."
    
    local config_files=(
        "src/constants/index.ts"
        "configs/system.json"
        ".env.example"
    )
    
    for file in "${config_files[@]}"; do
        if [[ -f "$file" ]]; then
            if grep -q "abab6.5s-chat\|MiniMax-M1" "$file" 2>/dev/null; then
                log_success "$file: 使用正确的模型名称"
            else
                log_warning "$file: 未找到模型名称配置"
            fi
        else
            log_warning "$file: 文件不存在"
        fi
    done
}

# 生成修复建议
generate_fix_suggestions() {
    echo ""
    log_info "📋 修复建议"
    echo "================================"
    echo "✅ 正确的MiniMax模型名称: 'MiniMax-M1'"
    echo "❌ 错误的模型名称: 'minimax-M1'"
    echo ""
    echo "🔧 如果发现错误的模型名称，请手动修复:"
    echo "   1. 打开包含错误名称的文件"
    echo "   2. 将 'minimax-M1' 替换为 'MiniMax-M1'"
    echo "   3. 保存文件"
    echo ""
    echo "🚀 修复后的操作:"
    echo "   1. 重新构建: docker-compose build app"
    echo "   2. 重启服务: docker-compose up -d"
    echo "   3. 测试MiniMax API调用"
}

# 显示当前状态
show_current_status() {
    echo ""
    log_info "📊 当前状态总结"
    echo "================================"
    
    # 统计正确和错误的使用
    local correct_count=$(find src -name "*.ts" -type f -exec grep -l "MiniMax-M1" {} \; 2>/dev/null | wc -l)
    local incorrect_count=$(find src -name "*.ts" -type f -exec grep -l "minimax-M1" {} \; 2>/dev/null | wc -l)
    
    echo "✅ 使用正确模型名称的文件: $correct_count 个"
    echo "❌ 使用错误模型名称的文件: $incorrect_count 个"
    
    if [ $incorrect_count -eq 0 ]; then
        log_success "🎉 所有文件都使用正确的模型名称！"
    else
        log_error "⚠️ 仍有文件使用错误的模型名称，需要修复"
    fi
}

# 主函数
main() {
    echo "开始验证MiniMax模型名称..."
    echo ""
    
    local has_errors=false
    
    if ! check_incorrect_names; then
        has_errors=true
    fi
    
    check_correct_names
    check_config_files
    show_current_status
    generate_fix_suggestions
    
    echo ""
    if [ "$has_errors" = true ]; then
        log_error "❌ 验证失败：发现错误的模型名称"
        echo ""
        log_info "💡 请修复错误的模型名称后重新运行验证"
        exit 1
    else
        log_success "🎉 验证通过：所有模型名称都正确！"
        echo ""
        log_info "💡 现在可以重新构建应用测试MiniMax调用"
        exit 0
    fi
}

main "$@"
