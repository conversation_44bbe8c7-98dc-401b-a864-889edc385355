/**
 * OCTI智能评估系统 - 管理端评估管理页面
 *
 * 管理员用于管理所有评估项目的页面
 */

'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { Plus, Search, Filter, Eye, Edit, Trash2, Download } from 'lucide-react';
import { AdminLayout, AdminPageContainer } from '@/components/layouts/admin-layout';

/**
 * 模拟评估数据
 */
const mockAssessments = [
  {
    id: '1',
    title: '绿色地球环保基金会 - 标准评估',
    organizationName: '绿色地球环保基金会',
    organizationType: '基金会',
    status: 'completed',
    type: 'standard',
    createdAt: '2025-01-25',
    completedAt: '2025-01-30',
    score: 85,
    progress: 100,
  },
  {
    id: '2',
    title: '希望之光教育机构 - 专业评估',
    organizationName: '希望之光教育机构',
    organizationType: '民间组织',
    status: 'in_progress',
    type: 'professional',
    createdAt: '2025-01-29',
    progress: 65,
  },
  {
    id: '3',
    title: '爱心助老服务中心 - 标准评估',
    organizationName: '爱心助老服务中心',
    organizationType: '慈善机构',
    status: 'completed',
    type: 'standard',
    createdAt: '2025-01-20',
    completedAt: '2025-01-28',
    score: 78,
    progress: 100,
  },
  {
    id: '4',
    title: '青少年发展促进会 - 专业评估',
    organizationName: '青少年发展促进会',
    organizationType: '社会团体',
    status: 'analysis',
    type: 'professional',
    createdAt: '2025-01-22',
    completedAt: '2025-01-27',
    progress: 100,
  },
];

/**
 * 状态标签组件
 */
function StatusBadge({ status }: { status: string }) {
  const statusConfig = {
    completed: { label: '已完成', color: 'bg-green-100 text-green-800' },
    in_progress: { label: '进行中', color: 'bg-blue-100 text-blue-800' },
    analysis: { label: '分析中', color: 'bg-yellow-100 text-yellow-800' },
    draft: { label: '草稿', color: 'bg-gray-100 text-gray-800' },
    cancelled: { label: '已取消', color: 'bg-red-100 text-red-800' },
  };

  const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.draft;

  return (
    <span
      className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.color}`}
    >
      {config.label}
    </span>
  );
}

/**
 * 管理端评估管理页面
 */
export default function AdminAssessmentsPage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [typeFilter, setTypeFilter] = useState('all');

  const mockUser = {
    name: '管理员',
    email: '<EMAIL>',
  };

  // 过滤评估数据
  const filteredAssessments = mockAssessments.filter(assessment => {
    const matchesSearch =
      assessment.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      assessment.organizationName.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || assessment.status === statusFilter;
    const matchesType = typeFilter === 'all' || assessment.type === typeFilter;

    return matchesSearch && matchesStatus && matchesType;
  });

  return (
    <AdminLayout user={mockUser}>
      <AdminPageContainer
        title='评估管理'
        description='管理所有评估项目，查看评估状态和结果'
        actions={
          <Link
            href='/admin/assessments/create'
            className='inline-flex items-center px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary/90 transition-colors'
          >
            <Plus className='h-4 w-4 mr-2' />
            创建评估
          </Link>
        }
      >
        {/* 搜索和筛选 */}
        <div className='bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6'>
          <div className='flex flex-col md:flex-row gap-4'>
            {/* 搜索框 */}
            <div className='flex-1'>
              <div className='relative'>
                <Search className='absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400' />
                <input
                  type='text'
                  placeholder='搜索评估或组织名称...'
                  value={searchTerm}
                  onChange={e => setSearchTerm(e.target.value)}
                  className='w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent'
                />
              </div>
            </div>

            {/* 状态筛选 */}
            <div className='flex items-center space-x-2'>
              <Filter className='h-4 w-4 text-gray-400' />
              <select
                value={statusFilter}
                onChange={e => setStatusFilter(e.target.value)}
                className='px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent'
              >
                <option value='all'>所有状态</option>
                <option value='completed'>已完成</option>
                <option value='in_progress'>进行中</option>
                <option value='analysis'>分析中</option>
                <option value='draft'>草稿</option>
              </select>
            </div>

            {/* 类型筛选 */}
            <div>
              <select
                value={typeFilter}
                onChange={e => setTypeFilter(e.target.value)}
                className='px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent'
              >
                <option value='all'>所有类型</option>
                <option value='standard'>标准评估</option>
                <option value='professional'>专业评估</option>
              </select>
            </div>
          </div>
        </div>

        {/* 评估列表 */}
        <div className='bg-white rounded-lg shadow-sm border border-gray-200'>
          <div className='overflow-x-auto'>
            <table className='w-full'>
              <thead className='bg-gray-50 border-b border-gray-200'>
                <tr>
                  <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                    评估信息
                  </th>
                  <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                    组织
                  </th>
                  <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                    状态
                  </th>
                  <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                    类型
                  </th>
                  <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                    进度/得分
                  </th>
                  <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                    创建时间
                  </th>
                  <th className='px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider'>
                    操作
                  </th>
                </tr>
              </thead>
              <tbody className='bg-white divide-y divide-gray-200'>
                {filteredAssessments.map(assessment => (
                  <tr key={assessment.id} className='hover:bg-gray-50'>
                    <td className='px-6 py-4'>
                      <div>
                        <div className='text-sm font-medium text-gray-900'>{assessment.title}</div>
                        {assessment.completedAt && (
                          <div className='text-sm text-gray-500'>
                            完成于 {assessment.completedAt}
                          </div>
                        )}
                      </div>
                    </td>
                    <td className='px-6 py-4'>
                      <div>
                        <div className='text-sm font-medium text-gray-900'>
                          {assessment.organizationName}
                        </div>
                        <div className='text-sm text-gray-500'>{assessment.organizationType}</div>
                      </div>
                    </td>
                    <td className='px-6 py-4'>
                      <StatusBadge status={assessment.status} />
                    </td>
                    <td className='px-6 py-4'>
                      <span className='text-sm text-gray-900'>
                        {assessment.type === 'standard' ? '标准评估' : '专业评估'}
                      </span>
                    </td>
                    <td className='px-6 py-4'>
                      {assessment.status === 'completed' && assessment.score ? (
                        <div className='text-sm'>
                          <span className='font-medium text-gray-900'>{assessment.score}分</span>
                        </div>
                      ) : (
                        <div className='flex items-center'>
                          <div className='w-16 bg-gray-200 rounded-full h-2 mr-2'>
                            <div
                              className='bg-primary h-2 rounded-full'
                              style={{ width: `${assessment.progress}%` }}
                            />
                          </div>
                          <span className='text-sm text-gray-600'>{assessment.progress}%</span>
                        </div>
                      )}
                    </td>
                    <td className='px-6 py-4 text-sm text-gray-500'>{assessment.createdAt}</td>
                    <td className='px-6 py-4 text-right'>
                      <div className='flex items-center justify-end space-x-2'>
                        <button className='p-1 text-gray-400 hover:text-gray-600'>
                          <Eye className='h-4 w-4' />
                        </button>
                        <button className='p-1 text-gray-400 hover:text-gray-600'>
                          <Edit className='h-4 w-4' />
                        </button>
                        {assessment.status === 'completed' && (
                          <button className='p-1 text-gray-400 hover:text-gray-600'>
                            <Download className='h-4 w-4' />
                          </button>
                        )}
                        <button className='p-1 text-gray-400 hover:text-red-600'>
                          <Trash2 className='h-4 w-4' />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {filteredAssessments.length === 0 && (
            <div className='text-center py-12'>
              <div className='text-gray-500 mb-4'>没有找到匹配的评估</div>
              <Link
                href='/admin/assessments/create'
                className='inline-flex items-center px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary/90 transition-colors'
              >
                <Plus className='h-4 w-4 mr-2' />
                创建第一个评估
              </Link>
            </div>
          )}
        </div>
      </AdminPageContainer>
    </AdminLayout>
  );
}
