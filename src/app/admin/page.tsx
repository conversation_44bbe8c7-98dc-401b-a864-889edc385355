/**
 * OCTI智能评估系统 - 管理端仪表板
 *
 * 管理员主页，展示系统关键指标和快速操作
 */

import React from 'react';
import Link from 'next/link';
import { FileText, Building, Users, BarChart3, CheckCircle, AlertCircle } from 'lucide-react';
import { AdminLayout, AdminPageContainer, AdminStatsCard } from '@/components/layouts/admin-layout';

/**
 * 模拟数据
 */
const mockStats = {
  totalAssessments: 1247,
  activeAssessments: 89,
  completedAssessments: 1158,
  totalOrganizations: 456,
  newOrganizationsThisMonth: 23,
  averageCompletionTime: '42分钟',
  completionRate: '92.8%',
};

const mockRecentAssessments = [
  {
    id: '1',
    organizationName: '绿色地球环保基金会',
    status: 'completed',
    completedAt: '2025-01-30',
    score: 85,
  },
  {
    id: '2',
    organizationName: '希望之光教育机构',
    status: 'in_progress',
    startedAt: '2025-01-29',
    progress: 65,
  },
  {
    id: '3',
    organizationName: '爱心助老服务中心',
    status: 'completed',
    completedAt: '2025-01-28',
    score: 78,
  },
  {
    id: '4',
    organizationName: '青少年发展促进会',
    status: 'analysis',
    completedAt: '2025-01-27',
  },
];

/**
 * 管理端仪表板页面
 */
export default function AdminDashboardPage() {
  const mockUser = {
    name: '管理员',
    email: '<EMAIL>',
  };

  return (
    <AdminLayout user={mockUser}>
      <AdminPageContainer title='仪表板' description='系统概览和关键指标'>
        {/* 统计卡片 */}
        <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8'>
          <AdminStatsCard
            title='总评估数'
            value={mockStats.totalAssessments}
            change='+12% 较上月'
            changeType='positive'
            iconName='FileText'
          />
          <AdminStatsCard
            title='进行中评估'
            value={mockStats.activeAssessments}
            change='+5 较昨日'
            changeType='positive'
          />
          <AdminStatsCard
            title='注册组织'
            value={mockStats.totalOrganizations}
            change={`+${mockStats.newOrganizationsThisMonth} 本月新增`}
            changeType='positive'
            iconName='Building'
          />
          <AdminStatsCard
            title='完成率'
            value={mockStats.completionRate}
            change='+2.3% 较上月'
            changeType='positive'
          />
        </div>

        <div className='grid lg:grid-cols-3 gap-6'>
          {/* 最近评估 */}
          <div className='lg:col-span-2'>
            <div className='bg-white rounded-lg shadow-sm border border-gray-200'>
              <div className='p-6 border-b border-gray-200'>
                <div className='flex items-center justify-between'>
                  <h2 className='text-lg font-semibold text-gray-900'>最近评估</h2>
                  <Link
                    href='/admin/assessments'
                    className='text-sm text-primary hover:text-primary/80'
                  >
                    查看全部
                  </Link>
                </div>
              </div>
              <div className='p-6'>
                <div className='space-y-4'>
                  {mockRecentAssessments.map(assessment => (
                    <div
                      key={assessment.id}
                      className='flex items-center justify-between p-4 bg-gray-50 rounded-lg'
                    >
                      <div className='flex items-center space-x-3'>
                        <div
                          className={`
                          w-3 h-3 rounded-full
                          ${
                            assessment.status === 'completed'
                              ? 'bg-green-500'
                              : assessment.status === 'in_progress'
                                ? 'bg-blue-500'
                                : assessment.status === 'analysis'
                                  ? 'bg-yellow-500'
                                  : 'bg-gray-400'
                          }
                        `}
                        />
                        <div>
                          <div className='font-medium text-gray-900'>
                            {assessment.organizationName}
                          </div>
                          <div className='text-sm text-gray-600'>
                            {assessment.status === 'completed' &&
                              `完成于 ${assessment.completedAt}`}
                            {assessment.status === 'in_progress' && `进度 ${assessment.progress}%`}
                            {assessment.status === 'analysis' && '分析中'}
                          </div>
                        </div>
                      </div>
                      <div className='text-right'>
                        {assessment.status === 'completed' && assessment.score && (
                          <div className='text-lg font-semibold text-gray-900'>
                            {assessment.score}分
                          </div>
                        )}
                        {assessment.status === 'in_progress' && (
                          <div className='w-16 bg-gray-200 rounded-full h-2'>
                            <div
                              className='bg-blue-500 h-2 rounded-full'
                              style={{ width: `${assessment.progress}%` }}
                            />
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>

          {/* 快速操作 */}
          <div className='space-y-6'>
            {/* 快速操作卡片 */}
            <div className='bg-white rounded-lg shadow-sm border border-gray-200 p-6'>
              <h2 className='text-lg font-semibold text-gray-900 mb-4'>快速操作</h2>
              <div className='space-y-3'>
                <Link
                  href='/admin/assessments/create'
                  className='flex items-center p-3 bg-primary text-white rounded-lg hover:bg-primary/90 transition-colors'
                >
                  <FileText className='h-5 w-5 mr-3' />
                  创建新评估
                </Link>
                <Link
                  href='/admin/organizations/create'
                  className='flex items-center p-3 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors'
                >
                  <Building className='h-5 w-5 mr-3' />
                  添加组织
                </Link>
                <Link
                  href='/admin/questionnaires'
                  className='flex items-center p-3 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors'
                >
                  <Users className='h-5 w-5 mr-3' />
                  管理问卷
                </Link>
                <Link
                  href='/admin/reports'
                  className='flex items-center p-3 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors'
                >
                  <BarChart3 className='h-5 w-5 mr-3' />
                  查看报告
                </Link>
              </div>
            </div>

            {/* 系统状态 */}
            <div className='bg-white rounded-lg shadow-sm border border-gray-200 p-6'>
              <h2 className='text-lg font-semibold text-gray-900 mb-4'>系统状态</h2>
              <div className='space-y-3'>
                <div className='flex items-center justify-between'>
                  <div className='flex items-center'>
                    <CheckCircle className='h-5 w-5 text-green-500 mr-2' />
                    <span className='text-sm text-gray-700'>API服务</span>
                  </div>
                  <span className='text-sm text-green-600 font-medium'>正常</span>
                </div>
                <div className='flex items-center justify-between'>
                  <div className='flex items-center'>
                    <CheckCircle className='h-5 w-5 text-green-500 mr-2' />
                    <span className='text-sm text-gray-700'>数据库</span>
                  </div>
                  <span className='text-sm text-green-600 font-medium'>正常</span>
                </div>
                <div className='flex items-center justify-between'>
                  <div className='flex items-center'>
                    <AlertCircle className='h-5 w-5 text-yellow-500 mr-2' />
                    <span className='text-sm text-gray-700'>AI服务</span>
                  </div>
                  <span className='text-sm text-yellow-600 font-medium'>负载较高</span>
                </div>
              </div>
            </div>

            {/* 系统信息 */}
            <div className='bg-white rounded-lg shadow-sm border border-gray-200 p-6'>
              <h2 className='text-lg font-semibold text-gray-900 mb-4'>系统信息</h2>
              <div className='space-y-2 text-sm'>
                <div className='flex justify-between'>
                  <span className='text-gray-600'>版本</span>
                  <span className='text-gray-900'>v4.0.0</span>
                </div>
                <div className='flex justify-between'>
                  <span className='text-gray-600'>平均响应时间</span>
                  <span className='text-gray-900'>{mockStats.averageCompletionTime}</span>
                </div>
                <div className='flex justify-between'>
                  <span className='text-gray-600'>在线用户</span>
                  <span className='text-gray-900'>127</span>
                </div>
                <div className='flex justify-between'>
                  <span className='text-gray-600'>存储使用</span>
                  <span className='text-gray-900'>68%</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </AdminPageContainer>
    </AdminLayout>
  );
}
