/**
 * 专业版双模型OCTI分析API路由
 * MiniMax基础分析 + DeepSeek-Reasoner深度升华
 */

import { NextRequest, NextResponse } from 'next/server';
import { ProfessionalAnalysisService } from '@/services/analysis/professional-analysis-service';
import { z } from 'zod';

// 请求数据验证schema
const ProfessionalAnalysisRequestSchema = z.object({
  profile: z.object({
    organizationType: z.string().optional(),
    serviceArea: z.string().optional(),
    resourceStructure: z.string().optional(),
    developmentStage: z.string().optional(),
    teamSize: z.string().optional(),
    operatingModel: z.string().optional(),
    impactScope: z.string().optional(),
    organizationCulture: z.string().optional(),
    challengesPriorities: z.string().optional(),
    futureVision: z.string().optional(),
  }),
  responses: z.array(
    z.object({
      questionId: z.string(),
      answer: z.union([z.string(), z.number(), z.array(z.string())]),
    })
  ),
  version: z.literal('professional'),
});

export async function POST(request: NextRequest) {
  try {
    console.log('🚀 收到专业版双模型分析请求');

    // 解析请求数据
    const body = await request.json();

    // 验证数据格式
    const validatedData = ProfessionalAnalysisRequestSchema.parse(body);

    console.log('📊 专业版数据验证通过');
    console.log('👤 组织画像:', validatedData.profile);
    console.log('📝 回答数量:', validatedData.responses.length);
    console.log('🎯 分析版本:', validatedData.version);

    // 执行专业版双模型分析
    const analysisService = new ProfessionalAnalysisService();
    const result = await analysisService.analyzeProfessional(validatedData);

    if (result.success) {
      console.log('✅ 专业版双模型分析完成');
      console.log('📋 处理步骤:', result.data.processingSteps);
      return NextResponse.json(result);
    } else {
      console.error('❌ 专业版分析失败:', result.error);
      return NextResponse.json(result, { status: 500 });
    }
  } catch (error) {
    console.error('❌ 专业版API处理失败:', error);

    // 返回友好的错误信息
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          success: false,
          error: '请求数据格式不正确',
          details: error.errors,
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : '服务器内部错误',
      },
      { status: 500 }
    );
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'OCTI专业版双模型分析API',
    version: '1.0.0',
    models: {
      step1: 'MiniMax-M1 (基础分析)',
      step2: 'DeepSeek-Reasoner (深度升华)',
    },
    endpoints: {
      POST: '/api/assessment/professional-analyze - 执行专业版双模型OCTI分析',
    },
  });
}
