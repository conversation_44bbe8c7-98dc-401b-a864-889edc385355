/**
 * 问卷答案存储API
 * 实现答案的持久化存储和恢复
 */

import { NextRequest, NextResponse } from 'next/server';
import fs from 'fs/promises';
import path from 'path';

// 存储目录
const STORAGE_DIR = path.join(process.cwd(), 'data', 'responses');

/**
 * 确保存储目录存在
 */
async function ensureStorageDir() {
  try {
    await fs.access(STORAGE_DIR);
  } catch {
    await fs.mkdir(STORAGE_DIR, { recursive: true });
  }
}

/**
 * 生成存储文件名
 */
function getStorageFileName(sessionId: string): string {
  return `responses_${sessionId}.json`;
}

/**
 * 保存问卷答案
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { sessionId, profile, responses, status = 'DRAFT', metadata = {} } = body;

    if (!sessionId || !responses) {
      return NextResponse.json({ error: '缺少必要参数' }, { status: 400 });
    }

    await ensureStorageDir();

    const storageData = {
      sessionId,
      profile,
      responses,
      status,
      metadata: {
        ...metadata,
        savedAt: new Date().toISOString(),
        totalQuestions: responses.length,
        completedQuestions: responses.filter(
          (r: any) => r.answer !== null && r.answer !== undefined
        ).length,
      },
    };

    const fileName = getStorageFileName(sessionId);
    const filePath = path.join(STORAGE_DIR, fileName);

    await fs.writeFile(filePath, JSON.stringify(storageData, null, 2));

    console.log(`✅ 问卷答案已保存: ${fileName}`);
    console.log(
      `📊 答案统计: ${storageData.metadata.completedQuestions}/${storageData.metadata.totalQuestions}`
    );

    return NextResponse.json({
      success: true,
      message: '答案保存成功',
      metadata: storageData.metadata,
    });
  } catch (error) {
    console.error('❌ 保存问卷答案失败:', error);
    return NextResponse.json({ error: '保存失败' }, { status: 500 });
  }
}

/**
 * 获取问卷答案
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const sessionId = searchParams.get('sessionId');

    if (!sessionId) {
      return NextResponse.json({ error: '缺少sessionId参数' }, { status: 400 });
    }

    await ensureStorageDir();

    const fileName = getStorageFileName(sessionId);
    const filePath = path.join(STORAGE_DIR, fileName);

    try {
      const fileContent = await fs.readFile(filePath, 'utf-8');
      const storageData = JSON.parse(fileContent);

      console.log(`✅ 问卷答案已恢复: ${fileName}`);
      console.log(
        `📊 答案统计: ${storageData.metadata.completedQuestions}/${storageData.metadata.totalQuestions}`
      );

      return NextResponse.json({
        success: true,
        data: storageData,
      });
    } catch (fileError) {
      return NextResponse.json({ error: '未找到保存的答案' }, { status: 404 });
    }
  } catch (error) {
    console.error('❌ 获取问卷答案失败:', error);
    return NextResponse.json({ error: '获取失败' }, { status: 500 });
  }
}

/**
 * 删除问卷答案
 */
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const sessionId = searchParams.get('sessionId');

    if (!sessionId) {
      return NextResponse.json({ error: '缺少sessionId参数' }, { status: 400 });
    }

    const fileName = getStorageFileName(sessionId);
    const filePath = path.join(STORAGE_DIR, fileName);

    try {
      await fs.unlink(filePath);
      console.log(`✅ 问卷答案已删除: ${fileName}`);

      return NextResponse.json({
        success: true,
        message: '答案删除成功',
      });
    } catch (fileError) {
      return NextResponse.json({ error: '未找到要删除的答案' }, { status: 404 });
    }
  } catch (error) {
    console.error('❌ 删除问卷答案失败:', error);
    return NextResponse.json({ error: '删除失败' }, { status: 500 });
  }
}
