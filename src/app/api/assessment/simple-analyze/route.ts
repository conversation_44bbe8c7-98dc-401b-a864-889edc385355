/**
 * 简化的OCTI分析API路由
 * 按照成功案例模式：简单直接，避免复杂处理
 */

import { NextRequest, NextResponse } from 'next/server';
import { SimpleAnalysisService } from '@/services/analysis/simple-analysis-service';
import { z } from 'zod';

// 请求数据验证schema
const AnalysisRequestSchema = z.object({
  profile: z.object({
    organizationType: z.string().optional(),
    serviceArea: z.string().optional(),
    resourceStructure: z.string().optional(),
    developmentStage: z.string().optional(),
    teamSize: z.string().optional(),
    operatingModel: z.string().optional(),
    impactScope: z.string().optional(),
    organizationCulture: z.string().optional(),
    challengesPriorities: z.string().optional(),
    futureVision: z.string().optional(),
  }),
  responses: z.array(
    z.object({
      questionId: z.string(),
      answer: z.union([z.string(), z.number(), z.array(z.string())]),
    })
  ),
  version: z.enum(['standard', 'professional']).default('standard'),
});

export async function POST(request: NextRequest) {
  try {
    console.log('📥 收到简化分析请求');

    // 解析请求数据
    const body = await request.json();

    // 验证数据格式
    const validatedData = AnalysisRequestSchema.parse(body);

    console.log('📊 数据验证通过');
    console.log('👤 组织画像:', validatedData.profile);
    console.log('📝 回答数量:', validatedData.responses.length);
    console.log('🎯 分析版本:', validatedData.version);

    // 执行分析
    const analysisService = new SimpleAnalysisService();
    const result = await analysisService.analyzeOrganization(validatedData);

    if (result.success) {
      console.log('✅ 简化分析完成');
      return NextResponse.json(result);
    } else {
      console.error('❌ 分析失败:', result.error);
      return NextResponse.json(result, { status: 500 });
    }
  } catch (error) {
    console.error('❌ API处理失败:', error);

    // 返回友好的错误信息
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          success: false,
          error: '请求数据格式不正确',
          details: error.errors,
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : '服务器内部错误',
      },
      { status: 500 }
    );
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'OCTI简化分析API',
    version: '1.0.0',
    endpoints: {
      POST: '/api/assessment/simple-analyze - 执行OCTI组织分析',
    },
  });
}
