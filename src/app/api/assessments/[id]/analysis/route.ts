/**
 * OCTI智能评估系统 - 分析API路由
 *
 * 提供评估分析相关接口
 * 支持执行分析、获取分析结果等操作
 */

import { NextRequest } from 'next/server';
import { z } from 'zod';
import { assessmentService } from '@/services/assessment-service';
import { prisma } from '@/lib/prisma';
import { createErrorResponse, createSuccessResponse } from '@/lib/api-utils';

// ============================================================================
// 请求验证Schema
// ============================================================================

const PathParamsSchema = z.object({
  id: z.string().uuid('评估ID格式无效'),
});

const PerformAnalysisSchema = z.object({
  useDualModel: z.boolean().default(false),
  analysisOptions: z
    .object({
      includeRecommendations: z.boolean().default(true),
      includeDevelopmentPriorities: z.boolean().default(true),
      includeNextSteps: z.boolean().default(true),
      confidenceThreshold: z.number().min(0).max(1).default(0.7),
    })
    .optional(),
});

// ============================================================================
// POST /api/assessments/[id]/analysis - 执行分析
// ============================================================================

export async function POST(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    // 验证路径参数
    const { id: assessmentId } = PathParamsSchema.parse(params);

    // 验证请求体
    const body = await request.json();
    const validatedData = PerformAnalysisSchema.parse(body);

    // 检查评估状态
    const assessment = await prisma.assessment.findUnique({
      where: { id: assessmentId },
      include: {
        questionnaire: {
          include: { questions: true },
        },
        responses: true,
        analysisResults: { orderBy: { createdAt: 'desc' }, take: 1 },
      },
    });

    if (!assessment) {
      return createErrorResponse('RESOURCE_NOT_FOUND', '评估不存在', 404);
    }

    if (!assessment.questionnaire) {
      return createErrorResponse('BUSINESS_LOGIC_ERROR', '问卷不存在', 400);
    }

    if (assessment.status !== 'ANALYSIS' && assessment.status !== 'IN_PROGRESS') {
      return createErrorResponse('BUSINESS_LOGIC_ERROR', '当前状态不允许执行分析', 400);
    }

    // 检查是否有足够的回答
    const totalQuestions = assessment.questionnaire.questions.length;
    const answeredQuestions = assessment.responses.length;
    const completionRate = answeredQuestions / totalQuestions;

    if (completionRate < 0.8) {
      // 至少需要完成80%的问题
      return createErrorResponse(
        'BUSINESS_LOGIC_ERROR',
        `问卷完成度不足，需要至少完成80%的问题（当前：${Math.round(completionRate * 100)}%）`,
        400
      );
    }

    // 检查是否已有分析结果
    if (assessment.analysisResults.length > 0) {
      return createErrorResponse('BUSINESS_LOGIC_ERROR', '该评估已有分析结果', 400);
    }

    // 执行分析
    const result = await assessmentService.performAnalysis(
      assessmentId,
      validatedData.useDualModel
    );

    return createSuccessResponse(result, 201);
  } catch (error) {
    console.error('Failed to perform analysis:', error);

    if (error instanceof z.ZodError) {
      return createErrorResponse('VALIDATION_ERROR', '请求参数验证失败', 400, {
        errors: error.errors,
      });
    }

    if (error instanceof Error && error.message.includes('评估数据不完整')) {
      return createErrorResponse('BUSINESS_LOGIC_ERROR', '评估数据不完整，无法执行分析', 400);
    }

    return createErrorResponse('INTERNAL_ERROR', '分析执行失败', 500);
  }
}

// ============================================================================
// GET /api/assessments/[id]/analysis - 获取分析结果
// ============================================================================

export async function GET(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    // 验证路径参数
    const { id: assessmentId } = PathParamsSchema.parse(params);

    // 获取查询参数
    const { searchParams } = new URL(request.url);
    const includeDetails = searchParams.get('includeDetails') === 'true';
    const version = searchParams.get('version'); // 获取特定版本的分析结果

    // 查询分析结果
    const whereClause: any = { assessmentId };
    if (version) {
      whereClause.id = version;
    }

    const analysisResults = await prisma.analysisResult.findMany({
      where: whereClause,
      orderBy: { createdAt: 'desc' },
      take: version ? 1 : 5, // 如果指定版本则只取一个，否则取最近5个
      include: {
        assessment: {
          select: {
            id: true,
            title: true,
            type: true,
            status: true,
            organization: {
              select: {
                id: true,
                name: true,
              },
            },
          },
        },
        reports: {
          select: {
            id: true,
            title: true,
            type: true,
            format: true,
            downloadUrl: true,
            generatedAt: true,
          },
          orderBy: { generatedAt: 'desc' },
        },
      },
    });

    if (analysisResults.length === 0) {
      return createErrorResponse('RESOURCE_NOT_FOUND', '分析结果不存在', 404);
    }

    // 格式化响应数据
    const formatAnalysisResult = (result: any) => {
      const baseData = {
        id: result.id,
        overallScore: result.overallScore,
        capabilityScores: result.capabilityScores,
        keyFindings: result.keyFindings,
        recommendations: result.recommendations,
        developmentPriorities: result.developmentPriorities,
        nextSteps: result.nextSteps,
        confidence: result.confidence,
        analysisModel: result.analysisModel,
        createdAt: result.createdAt,
        updatedAt: result.updatedAt,
        assessment: result.assessment,
        reports: result.reports,
      };

      if (includeDetails) {
        return {
          ...baseData,
          primaryAnalysis: result.primaryAnalysis,
          secondaryAnalysis: result.secondaryAnalysis,
          fusionMetadata: result.fusionMetadata,
        };
      }

      return baseData;
    };

    const responseData = version
      ? formatAnalysisResult(analysisResults[0])
      : {
          latest: formatAnalysisResult(analysisResults[0]),
          history: analysisResults.slice(1).map(formatAnalysisResult),
          total: analysisResults.length,
        };

    return createSuccessResponse(responseData);
  } catch (error) {
    console.error('Failed to get analysis results:', error);

    if (error instanceof z.ZodError) {
      return createErrorResponse('VALIDATION_ERROR', '请求参数验证失败', 400, {
        errors: error.errors,
      });
    }

    return createErrorResponse('INTERNAL_ERROR', '获取分析结果失败', 500);
  }
}

// ============================================================================
// PUT /api/assessments/[id]/analysis - 重新分析
// ============================================================================

export async function PUT(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    // 验证路径参数
    const { id: assessmentId } = PathParamsSchema.parse(params);

    // 验证请求体
    const body = await request.json();
    const reAnalysisSchema = z.object({
      reason: z.string().min(1, '重新分析原因不能为空'),
      useDualModel: z.boolean().default(false),
      preserveHistory: z.boolean().default(true),
    });

    const validatedData = reAnalysisSchema.parse(body);

    // 检查评估状态
    const assessment = await prisma.assessment.findUnique({
      where: { id: assessmentId },
      include: {
        analysisResults: { orderBy: { createdAt: 'desc' }, take: 1 },
      },
    });

    if (!assessment) {
      return createErrorResponse('RESOURCE_NOT_FOUND', '评估不存在', 404);
    }

    if (assessment.status !== 'COMPLETED') {
      return createErrorResponse('BUSINESS_LOGIC_ERROR', '只有已完成的评估才能重新分析', 400);
    }

    // 如果不保留历史，删除旧的分析结果
    if (!validatedData.preserveHistory && assessment.analysisResults.length > 0) {
      await prisma.analysisResult.deleteMany({
        where: { assessmentId },
      });
    }

    // 更新评估状态为分析中
    await prisma.assessment.update({
      where: { id: assessmentId },
      data: { status: 'ANALYSIS' },
    });

    // 执行重新分析
    const result = await assessmentService.performAnalysis(
      assessmentId,
      validatedData.useDualModel
    );

    // 记录重新分析的原因
    await prisma.analysisResult.update({
      where: { id: result.analysisResult.id },
      data: {
        fusionMetadata: {
          ...((result.analysisResult.fusionMetadata as any) || {}),
          reAnalysis: {
            reason: validatedData.reason,
            timestamp: new Date().toISOString(),
            preservedHistory: validatedData.preserveHistory,
          },
        },
      },
    });

    return createSuccessResponse({
      ...result,
      message: '重新分析完成',
      reason: validatedData.reason,
    });
  } catch (error) {
    console.error('Failed to re-analyze assessment:', error);

    if (error instanceof z.ZodError) {
      return createErrorResponse('VALIDATION_ERROR', '请求参数验证失败', 400, {
        errors: error.errors,
      });
    }

    return createErrorResponse('INTERNAL_ERROR', '重新分析失败', 500);
  }
}
