/**
 * OCTI智能评估系统 - 问卷API路由
 *
 * 提供问卷生成、获取、提交等接口
 * 支持个性化问卷的完整生命周期管理
 */

import { NextRequest } from 'next/server';
import { z } from 'zod';
import { assessmentService } from '@/services/assessment-service';
import { prisma } from '@/lib/prisma';
import { createErrorResponse, createSuccessResponse } from '@/lib/api-utils';

// ============================================================================
// 请求验证Schema
// ============================================================================

const PathParamsSchema = z.object({
  id: z.string().uuid('评估ID格式无效'),
});

const SubmitQuestionnaireSchema = z.object({
  responses: z
    .array(
      z.object({
        questionId: z.string().uuid('问题ID格式无效'),
        answer: z.any(), // 支持各种类型的答案
        confidence: z.number().min(0).max(1).optional(),
        timeSpent: z.number().int().min(0).optional(), // 秒
      })
    )
    .min(1, '至少需要回答一个问题'),
});

// ============================================================================
// POST /api/assessments/[id]/questionnaire - 生成问卷
// ============================================================================

export async function POST(_request: NextRequest, { params }: { params: { id: string } }) {
  try {
    // 验证路径参数
    const { id: assessmentId } = PathParamsSchema.parse(params);

    // 检查评估状态
    const assessment = await prisma.assessment.findUnique({
      where: { id: assessmentId },
      include: {
        organization: {
          include: { nonprofitProfile: true },
        },
        questionnaire: true,
      },
    });

    if (!assessment) {
      return createErrorResponse('RESOURCE_NOT_FOUND', '评估不存在', 404);
    }

    if (!assessment.organization.nonprofitProfile) {
      return createErrorResponse('BUSINESS_LOGIC_ERROR', '请先完成组织画像收集', 400);
    }

    if (assessment.questionnaire) {
      return createErrorResponse('BUSINESS_LOGIC_ERROR', '问卷已存在', 400);
    }

    if (
      assessment.status !== 'QUESTIONNAIRE_GENERATION' &&
      assessment.status !== 'PROFILE_COLLECTION'
    ) {
      return createErrorResponse('BUSINESS_LOGIC_ERROR', '当前状态不允许生成问卷', 400);
    }

    // 生成问卷
    const result = await assessmentService.generateQuestionnaire(assessmentId);

    return createSuccessResponse(result, 201);
  } catch (error) {
    console.error('Failed to generate questionnaire:', error);

    if (error instanceof z.ZodError) {
      return createErrorResponse('VALIDATION_ERROR', '请求参数验证失败', 400, {
        errors: error.errors,
      });
    }

    return createErrorResponse('INTERNAL_ERROR', '问卷生成失败', 500);
  }
}

// ============================================================================
// GET /api/assessments/[id]/questionnaire - 获取问卷
// ============================================================================

export async function GET(_request: NextRequest, { params }: { params: { id: string } }) {
  try {
    // 验证路径参数
    const { id: assessmentId } = PathParamsSchema.parse(params);

    // 查询问卷信息
    const assessment = await prisma.assessment.findUnique({
      where: { id: assessmentId },
      include: {
        questionnaire: {
          include: {
            questions: {
              orderBy: { order: 'asc' },
              select: {
                id: true,
                type: true,
                source: true,
                category: true,
                subCategory: true,
                title: true,
                description: true,
                options: true,
                required: true,
                order: true,
                metadata: true,
              },
            },
          },
        },
        responses: {
          select: {
            id: true,
            questionId: true,
            answer: true,
            confidence: true,
            timeSpent: true,
            createdAt: true,
          },
        },
      },
    });

    if (!assessment) {
      return createErrorResponse('RESOURCE_NOT_FOUND', '评估不存在', 404);
    }

    if (!assessment.questionnaire) {
      return createErrorResponse('RESOURCE_NOT_FOUND', '问卷不存在', 404);
    }

    // 构建响应映射
    const responseMap = new Map();
    assessment.responses.forEach(response => {
      responseMap.set(response.questionId, response);
    });

    // 格式化问题数据，包含已有回答
    const questionsWithResponses = assessment.questionnaire.questions.map(question => ({
      ...question,
      response: responseMap.get(question.id) || null,
    }));

    // 计算完成进度
    const totalQuestions = assessment.questionnaire.questions.length;
    const answeredQuestions = assessment.responses.length;
    const completionRate = totalQuestions > 0 ? (answeredQuestions / totalQuestions) * 100 : 0;

    // 按维度分组问题
    const questionsByCategory = assessment.questionnaire.questions.reduce(
      (acc, question) => {
        if (!acc[question.category]) {
          acc[question.category] = [];
        }
        if (!acc[question.category]) {
          acc[question.category] = [];
        }
        acc[question.category]!.push({
          ...question,
          response: responseMap.get(question.id) || null,
        });
        return acc;
      },
      {} as Record<string, any[]>
    );

    const responseData = {
      questionnaire: {
        id: assessment.questionnaire.id,
        title: assessment.questionnaire.title,
        description: assessment.questionnaire.description,
        type: assessment.questionnaire.type,
        status: assessment.questionnaire.status,
        totalQuestions: assessment.questionnaire.totalQuestions,
        presetQuestions: assessment.questionnaire.presetQuestions,
        aiGeneratedQuestions: assessment.questionnaire.aiGeneratedQuestions,
        estimatedDuration: assessment.questionnaire.estimatedDuration,
        createdAt: assessment.questionnaire.createdAt,
      },
      questions: questionsWithResponses,
      questionsByCategory,
      progress: {
        totalQuestions,
        answeredQuestions,
        completionRate: Math.round(completionRate),
        remainingQuestions: totalQuestions - answeredQuestions,
      },
      assessment: {
        id: assessment.id,
        status: assessment.status,
        type: assessment.type,
      },
    };

    return createSuccessResponse(responseData);
  } catch (error) {
    console.error('Failed to get questionnaire:', error);

    if (error instanceof z.ZodError) {
      return createErrorResponse('VALIDATION_ERROR', '请求参数验证失败', 400, {
        errors: error.errors,
      });
    }

    return createErrorResponse('INTERNAL_ERROR', '获取问卷失败', 500);
  }
}

// ============================================================================
// PUT /api/assessments/[id]/questionnaire - 提交问卷回答
// ============================================================================

export async function PUT(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    // 验证路径参数
    const { id: assessmentId } = PathParamsSchema.parse(params);

    // 验证请求体
    const body = await request.json();
    const validatedData = SubmitQuestionnaireSchema.parse(body);

    // 检查评估状态
    const assessment = await prisma.assessment.findUnique({
      where: { id: assessmentId },
      include: {
        questionnaire: {
          include: { questions: true },
        },
      },
    });

    if (!assessment) {
      return createErrorResponse('RESOURCE_NOT_FOUND', '评估不存在', 404);
    }

    if (!assessment.questionnaire) {
      return createErrorResponse('RESOURCE_NOT_FOUND', '问卷不存在', 404);
    }

    if (assessment.status !== 'IN_PROGRESS') {
      return createErrorResponse('BUSINESS_LOGIC_ERROR', '当前状态不允许提交问卷', 400);
    }

    // 验证问题ID的有效性
    const validQuestionIds = new Set(assessment.questionnaire.questions.map(q => q.id));
    const invalidQuestionIds = validatedData.responses
      .map(r => r.questionId)
      .filter(id => !validQuestionIds.has(id));

    if (invalidQuestionIds.length > 0) {
      return createErrorResponse('VALIDATION_ERROR', '包含无效的问题ID', 400, {
        invalidQuestionIds,
      });
    }

    // 提交问卷
    const result = await assessmentService.submitQuestionnaire({
      assessmentId,
      responses: validatedData.responses.map(r => {
        const response: any = {
          questionId: r.questionId,
          answer: r.answer || null,
        };
        if (r.confidence !== undefined) {
          response.confidence = r.confidence;
        }
        if (r.timeSpent !== undefined) {
          response.timeSpent = r.timeSpent;
        }
        return response;
      }),
    });

    return createSuccessResponse(result);
  } catch (error) {
    console.error('Failed to submit questionnaire:', error);

    if (error instanceof z.ZodError) {
      return createErrorResponse('VALIDATION_ERROR', '请求参数验证失败', 400, {
        errors: error.errors,
      });
    }

    const errorMessage = error instanceof Error ? error.message : String(error);
    if (errorMessage.includes('缺少') && errorMessage.includes('题目的回答')) {
      return createErrorResponse('VALIDATION_ERROR', errorMessage, 400);
    }

    return createErrorResponse('INTERNAL_ERROR', '问卷提交失败', 500);
  }
}
