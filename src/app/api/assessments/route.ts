/**
 * OCTI智能评估系统 - 评估API路由
 *
 * 提供评估相关的RESTful API接口
 * 支持创建评估、获取评估列表等操作
 */

import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { assessmentService } from '@/services/assessment-service';
import { prisma } from '@/lib/prisma';
import { createErrorResponse, createSuccessResponse } from '@/lib/api-utils';

// ============================================================================
// 请求验证Schema
// ============================================================================

const CreateAssessmentSchema = z.object({
  organizationId: z.string().uuid('组织ID格式无效'),
  title: z.string().optional(),
  description: z.string().optional(),
  type: z.enum(['STANDARD', 'PROFESSIONAL'], {
    errorMap: () => ({ message: '评估类型必须是STANDARD或PROFESSIONAL' }),
  }),
});

const GetAssessmentsSchema = z.object({
  organizationId: z.string().uuid('组织ID格式无效').optional(),
  status: z
    .enum([
      'DRAFT',
      'PROFILE_COLLECTION',
      'QUESTIONNAIRE_GENERATION',
      'IN_PROGRESS',
      'ANALYSIS',
      'COMPLETED',
      'ARCHIVED',
      'FAILED',
      'CANCELLED',
    ])
    .optional(),
  type: z.enum(['STANDARD', 'PROFESSIONAL']).optional(),
  page: z.coerce.number().int().min(1).default(1),
  limit: z.coerce.number().int().min(1).max(100).default(20),
  sortBy: z.enum(['createdAt', 'completedAt', 'title']).default('createdAt'),
  sortOrder: z.enum(['asc', 'desc']).default('desc'),
});

// ============================================================================
// POST /api/assessments - 创建评估
// ============================================================================

export async function POST(request: NextRequest) {
  try {
    // 验证请求体
    const body = await request.json();
    const validatedData = CreateAssessmentSchema.parse(body);

    // 验证组织存在
    const organization = await prisma.organization.findUnique({
      where: { id: validatedData.organizationId },
      include: { nonprofitProfile: true },
    });

    if (!organization) {
      return createErrorResponse('RESOURCE_NOT_FOUND', '组织不存在', 404);
    }

    // 创建评估
    const assessment = await assessmentService.createAssessment({
      organizationId: validatedData.organizationId,
      title: validatedData.title || '未命名评估',
      description: validatedData.description || '',
      type: validatedData.type,
    });

    return createSuccessResponse(assessment, 201);
  } catch (error) {
    console.error('Failed to create assessment:', error);

    if (error instanceof z.ZodError) {
      return createErrorResponse('VALIDATION_ERROR', '请求参数验证失败', 400, {
        errors: error.errors,
      });
    }

    return createErrorResponse('INTERNAL_ERROR', '评估创建失败', 500);
  }
}

// ============================================================================
// GET /api/assessments - 获取评估列表
// ============================================================================

export async function GET(request: NextRequest) {
  try {
    // 解析查询参数
    const { searchParams } = new URL(request.url);
    const queryParams = Object.fromEntries(searchParams.entries());

    const validatedParams = GetAssessmentsSchema.parse(queryParams);

    // 构建查询条件
    const where: any = {};

    if (validatedParams.organizationId) {
      where.organizationId = validatedParams.organizationId;
    }

    if (validatedParams.status) {
      where.status = validatedParams.status;
    }

    if (validatedParams.type) {
      where.type = validatedParams.type;
    }

    // 计算分页
    const skip = (validatedParams.page - 1) * validatedParams.limit;

    // 查询评估列表
    const [assessments, total] = await Promise.all([
      prisma.assessment.findMany({
        where,
        include: {
          organization: {
            select: {
              id: true,
              name: true,
              industry: true,
              size: true,
            },
          },
          questionnaire: {
            select: {
              id: true,
              title: true,
              totalQuestions: true,
              status: true,
            },
          },
          analysisResults: {
            select: {
              id: true,
              overallScore: true,
              confidence: true,
              analysisModel: true,
            },
            orderBy: { createdAt: 'desc' },
            take: 1,
          },
          reports: {
            select: {
              id: true,
              title: true,
              type: true,
              format: true,
              generatedAt: true,
            },
            orderBy: { generatedAt: 'desc' },
            take: 1,
          },
          _count: {
            select: {
              responses: true,
            },
          },
        },
        orderBy: {
          [validatedParams.sortBy]: validatedParams.sortOrder,
        },
        skip,
        take: validatedParams.limit,
      }),
      prisma.assessment.count({ where }),
    ]);

    // 计算分页信息
    const totalPages = Math.ceil(total / validatedParams.limit);
    const hasNextPage = validatedParams.page < totalPages;
    const hasPreviousPage = validatedParams.page > 1;

    // 格式化响应数据
    const formattedAssessments = assessments.map(assessment => ({
      id: assessment.id,
      title: assessment.title,
      description: assessment.description,
      type: assessment.type,
      status: assessment.status,
      organization: assessment.organization,
      questionnaire: assessment.questionnaire,
      latestAnalysis: assessment.analysisResults?.[0] || null,
      latestReport: assessment.reports?.[0] || null,
      responseCount: assessment._count?.responses || 0,
      createdAt: assessment.createdAt,
      startedAt: assessment.startedAt,
      completedAt: assessment.completedAt,
      updatedAt: assessment.updatedAt,
    }));

    return createSuccessResponse({
      assessments: formattedAssessments,
      pagination: {
        page: validatedParams.page,
        limit: validatedParams.limit,
        total,
        totalPages,
        hasNextPage,
        hasPreviousPage,
      },
      filters: {
        organizationId: validatedParams.organizationId,
        status: validatedParams.status,
        type: validatedParams.type,
      },
      sorting: {
        sortBy: validatedParams.sortBy,
        sortOrder: validatedParams.sortOrder,
      },
    });
  } catch (error) {
    console.error('Failed to get assessments:', error);

    if (error instanceof z.ZodError) {
      return createErrorResponse('VALIDATION_ERROR', '查询参数验证失败', 400, {
        errors: error.errors,
      });
    }

    return createErrorResponse('INTERNAL_ERROR', '获取评估列表失败', 500);
  }
}

// ============================================================================
// OPTIONS - CORS预检请求
// ============================================================================

export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
