/**
 * 后台智能问题生成API路由
 * 用于在用户答题过程中异步生成智能题目
 */

import { NextRequest, NextResponse } from 'next/server';
import { IntelligentQuestionGenerator } from '@/services/intelligent-question-generator';
import { ProfileTransformer } from '@/services/analysis/profile-transformer';
import { LLMClient } from '@/services/llm/llm-client';

// 强化的LLM响应清理函数已移除以避免未使用警告

/**
 * XML解析函数 - 更宽松和可靠
 */
function parseXMLResponse(text: string, dimension: string): any {
  console.log(`🔍 ${dimension}原始响应前200字符:`, text.substring(0, 200));

  try {
    // 清理响应文本
    let cleanedText = text.trim();

    // 移除可能的markdown包装
    cleanedText = cleanedText
      .replace(/^```xml\s*/i, '')
      .replace(/^```\s*/, '')
      .replace(/\s*```$/g, '');

    // 确保有根元素
    if (!cleanedText.includes('<questions>')) {
      // 如果没有根元素，尝试包装
      if (cleanedText.includes('<question')) {
        cleanedText = `<questions>${cleanedText}</questions>`;
      }
    }

    console.log(`🧹 ${dimension}清理后前200字符:`, cleanedText.substring(0, 200));

    // 使用正则表达式提取问题
    const questions = [];
    const questionRegex = /<question[^>]*id="([^"]*)"[^>]*>([\s\S]*?)<\/question>/gi;

    let match;
    while ((match = questionRegex.exec(cleanedText)) !== null) {
      const questionId = match[1];
      const questionContent = match[2];

      // 提取问题文本
      const textMatch = questionContent?.match(/<text>([\s\S]*?)<\/text>/i);
      const questionText = textMatch ? textMatch[1]?.trim() : '';

      console.log(`🔍 ${dimension}解析问题 ${questionId}:`);
      console.log(`  - 问题内容长度: ${questionContent?.length || 0}`);
      console.log(`  - 文本匹配结果: ${textMatch ? '成功' : '失败'}`);
      console.log(`  - 提取的文本: "${questionText}"`);

      // 提取选项
      const options = [];
      const optionRegex = /<option>([\s\S]*?)<\/option>/gi;
      let optionMatch;
      while ((optionMatch = optionRegex.exec(questionContent || '')) !== null) {
        options.push(optionMatch[1]?.trim() || '');
      }

      console.log(`  - 提取的选项数量: ${options.length}`);
      console.log(`  - 选项内容: ${JSON.stringify(options)}`);

      if (questionText && options.length > 0) {
        // 转换选项格式为前端期望的格式
        const formattedOptions = options.map((optionText, index) => ({
          value: String.fromCharCode(65 + index), // A, B, C, D
          text: optionText,
        }));

        questions.push({
          id: questionId,
          title: questionText, // 使用title字段匹配前端期望
          text: questionText, // 保留text字段作为备用
          type: 'SINGLE_CHOICE',
          options: formattedOptions,
        });
      }
    }

    console.log(`✅ ${dimension}XML解析成功，提取到${questions.length}道题目`);

    return { questions };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    console.error(`❌ ${dimension}XML解析失败:`, errorMessage);
    console.error('原始文本:', text.substring(0, 500));

    // 降级到简单的文本解析
    return parseTextFallback(text, dimension);
  }
}

/**
 * 文本降级解析 - 最后的备选方案
 */
function parseTextFallback(_text: string, dimension: string): any {
  console.log(`🔄 ${dimension}尝试文本降级解析...`);

  // 简单的文本模式匹配，生成基础题目
  const questions = [
    {
      id: `${dimension}_FALLBACK_001`,
      title: `请评估您的组织在${dimension}维度的整体表现`,
      text: `请评估您的组织在${dimension}维度的整体表现`,
      type: 'SINGLE_CHOICE',
      options: [
        { value: 'A', text: '需要大幅改进' },
        { value: 'B', text: '有待提升' },
        { value: 'C', text: '基本满足要求' },
        { value: 'D', text: '表现优秀' },
      ],
    },
  ];

  console.log(`⚠️ ${dimension}使用降级解析，生成1道基础题目`);
  return { questions };
}

/**
 * 获取可用的API密钥列表
 */
function getAvailableApiKeys(): string[] {
  const apiKeys = [
    process.env.MINIMAX_API_KEY_1,
    process.env.MINIMAX_API_KEY_2,
    process.env.MINIMAX_API_KEY_3,
    process.env.MINIMAX_API_KEY_4,
    process.env.MINIMAX_API_KEY, // 备用主密钥
  ].filter((key): key is string => key !== undefined && key.length > 50); // 过滤有效密钥

  console.log(`🔑 发现${apiKeys.length}个有效API密钥，支持并发调用`);
  return apiKeys;
}

/**
 * 直接在服务端生成智能题目 - 分批+多API Key并发版本
 */
async function generateIntelligentQuestionsDirectly(profile: any) {
  const dimensions = ['SF', 'IT', 'MV', 'AD'];
  const batchSizes = [2, 2, 2, 1]; // 2+2+2+1分批策略
  const apiKeys = getAvailableApiKeys();

  if (apiKeys.length === 0) {
    throw new Error('没有可用的API密钥');
  }

  console.log(`🚀 开始分批并发生成智能题目 - 4个维度 × 4批次 = 16个并发任务`);
  const startTime = Date.now();

  // 为每个维度创建分批任务
  const allPromises: Promise<any>[] = [];
  let apiKeyIndex = 0;

  dimensions.forEach((dimension, _dimIndex) => {
    batchSizes.forEach((batchSize, batchIndex) => {
      const apiKey = apiKeys[apiKeyIndex % apiKeys.length];
      const batchNumber = batchIndex + 1;
      const totalBatches = batchSizes.length;

      console.log(
        `🎯 ${dimension}维度第${batchNumber}批(${batchSize}题)使用API密钥${(apiKeyIndex % apiKeys.length) + 1}`
      );

      const promise = generateBatchQuestionsDirectly(
        dimension,
        profile,
        batchSize,
        batchNumber,
        totalBatches,
        apiKey
      )
        .then(questions => {
          console.log(`✅ ${dimension}维度第${batchNumber}批生成成功:`, questions.length, '道题目');
          return { dimension, batchNumber, questions, success: true };
        })
        .catch(error => {
          console.warn(
            `⚠️ ${dimension}维度第${batchNumber}批生成失败，使用模板生成:`,
            error.message
          );

          // 降级到模板生成
          const generator = new IntelligentQuestionGenerator();
          const templateQuestions = generator.generateQuestionsFromTemplate(
            dimension as any,
            profile,
            batchSize
          );
          return { dimension, batchNumber, questions: templateQuestions, success: false };
        });

      allPromises.push(promise);
      apiKeyIndex++;
    });
  });

  // 等待所有分批任务完成
  const results = await Promise.allSettled(allPromises);
  const endTime = Date.now();

  console.log(`⚡ 分批并发生成完成，总耗时: ${(endTime - startTime) / 1000}秒`);

  // 按维度和批次整理结果
  const dimensionQuestions: Record<string, any[]> = {};
  let successCount = 0;
  let failureCount = 0;

  results.forEach((result, index) => {
    if (result.status === 'fulfilled') {
      const { dimension, batchNumber: _batchNumber, questions, success } = result.value;

      if (!dimensionQuestions[dimension]) {
        dimensionQuestions[dimension] = [];
      }
      dimensionQuestions[dimension].push(...questions);

      if (success) {
        successCount++;
      } else {
        failureCount++;
      }
    } else {
      console.error(`❌ 分批任务${index + 1}完全失败:`, result.reason);
      failureCount++;
    }
  });

  // 合并所有题目
  const allQuestions: any[] = [];
  Object.values(dimensionQuestions).forEach(questions => {
    allQuestions.push(...questions);
  });

  console.log(
    `📊 分批生成统计: 成功${successCount}批, 失败${failureCount}批, 总题目${allQuestions.length}道`
  );

  return allQuestions;
}

/**
 * 分批生成题目 - 针对性提示词 + 只生成单选题
 */
async function generateBatchQuestionsDirectly(
  dimension: string,
  profile: any,
  count: number,
  batchNumber: number,
  totalBatches: number,
  apiKey?: string
) {
  // 使用指定的API密钥创建LLM客户端
  const llmClient = new LLMClient();

  // 如果提供了特定的API密钥，临时设置环境变量
  const originalApiKey = process.env.MINIMAX_API_KEY;
  if (apiKey) {
    process.env.MINIMAX_API_KEY = apiKey;
    console.log(
      `🔑 ${dimension}维度第${batchNumber}批使用专用API密钥: ${apiKey.substring(0, 20)}...`
    );
  }

  // 根据批次生成不同角度的提示词
  const batchFocus = getBatchFocus(dimension, batchNumber, totalBatches);

  const systemPrompt = `你是一位专业的公益机构组织能力评估专家，专门设计个性化的单选题评估问题。

🎯 本批次专注角度：${batchFocus}

📋 严格格式要求：
1. 只生成单选题
2. 直接返回XML格式，不要任何包装
3. 每道题必须有4个选项
4. 选项要有明显的区分度（从低到高的能力层次）

返回格式示例：
<questions>
  <question id="${dimension}_I${String(batchNumber).padStart(2, '0')}1">
    <text>问题内容</text>
    <options>
      <option>选项1</option>
      <option>选项2</option>
      <option>选项3</option>
      <option>选项4</option>
    </options>
  </question>
</questions>

📝 题目要求：
1. 针对公益机构的实际工作场景
2. 体现${dimension}维度的${batchFocus}
3. 选项从"基础/被动"到"优秀/主动"递进
4. 问题具体、可操作、易理解

⚠️ 重要：直接返回XML，不要任何额外文字或标记`;

  const contextualPrompt = buildContextualPrompt(dimension, profile);
  const userPrompt = `${contextualPrompt}

请生成${count}道专注于"${batchFocus}"的${dimension}维度单选题。`;

  console.log(
    `📏 ${dimension}维度第${batchNumber}批提示词长度:`,
    systemPrompt.length + userPrompt.length,
    '字符'
  );

  try {
    const response = await llmClient.call({
      model: 'MiniMax-M1', // 修复大小写
      messages: [
        { role: 'system', content: systemPrompt },
        { role: 'user', content: userPrompt },
      ],
      temperature: 0.7, // 降低温度提高一致性
      max_tokens: 2000, // 减少token数量
      // 移除 response_format 限制，让LLM自由返回XML
    });

    let responseText = '';
    if (response?.choices?.[0]?.message?.content) {
      responseText = response.choices[0].message.content;
    } else if ((response as any)?.reply) {
      responseText = (response as any).reply;
    } else {
      throw new Error('LLM响应格式异常');
    }

    const parsedResponse = parseXMLResponse(responseText, `${dimension}第${batchNumber}批`);
    const questions = parsedResponse.questions || [];

    return questions.map((q: any, index: number) => {
      const questionId = `${dimension}_I${String(batchNumber).padStart(2, '0')}${String(index + 1)}`;

      console.log(
        `🔧 ${dimension}第${batchNumber}批问题${index + 1}: id="${questionId}", text="${q.text?.substring(0, 50)}..."`
      );

      return {
        id: questionId,
        title: q.text, // 使用title字段匹配前端期望
        text: q.text, // 保留text字段作为备用
        type: 'SINGLE_CHOICE', // 强制单选题
        options: q.options || [
          { value: 'A', text: '选项1' },
          { value: 'B', text: '选项2' },
          { value: 'C', text: '选项3' },
          { value: 'D', text: '选项4' },
        ],
        category: `${dimension}_INTELLIGENT`,
        dimension: dimension,
        weight: q.weight || 1,
        source: 'AI_GENERATED' as const,
        order: 1000 + (batchNumber - 1) * 10 + index,
        required: true,
        tags: [
          `${dimension.toLowerCase()}_dimension`,
          'intelligent_generated',
          `batch_${batchNumber}`,
        ],
        metadata: {
          generatedAt: new Date().toISOString(),
          batchNumber,
          batchFocus,
          organizationType: profile.organizationType,
          serviceArea: profile.serviceArea,
          developmentStage: profile.developmentStage,
        },
      };
    });
  } finally {
    // 恢复原始API密钥
    if (apiKey && originalApiKey) {
      process.env.MINIMAX_API_KEY = originalApiKey;
    }
  }
}

/**
 * 根据批次获取专注角度
 */
function getBatchFocus(dimension: string, batchNumber: number, _totalBatches: number): string {
  const focusMap: Record<string, string[]> = {
    SF: ['战略规划与目标设定', '资源配置与优化', '服务创新与改进', '可持续发展能力'],
    IT: ['信息系统建设', '数据管理与分析', '数字化工具应用', '技术创新应用'],
    MV: ['使命愿景传达', '价值观践行', '文化建设', '品牌影响力'],
    AD: ['组织架构设计', '团队管理能力', '决策机制', '变革适应能力'],
  };

  return focusMap[dimension]?.[batchNumber - 1] || '综合能力评估';
}

/**
 * 为特定维度生成题目 - 支持指定API密钥（保留兼容性）
 * 暂时注释以避免未使用警告
 */
/*
async function generateDimensionQuestionsDirectly(
  dimension: string,
  profile: any,
  count: number,
  apiKey?: string
) {
  // 使用指定的API密钥创建LLM客户端
  const llmClient = new LLMClient();

  // 如果提供了特定的API密钥，临时设置环境变量
  const originalApiKey = process.env.MINIMAX_API_KEY;
  if (apiKey) {
    process.env.MINIMAX_API_KEY = apiKey;
    console.log(`🔑 ${dimension}维度使用专用API密钥: ${apiKey.substring(0, 20)}...`);
  }

  const systemPrompt = `你是一位专业的公益机构组织能力评估专家，专门设计个性化的评估问题。

🎯 JSON格式要求（严格遵守）：
1. 直接返回JSON对象，不要使用markdown代码块包装
2. 不要添加 \`\`\`json 或 \`\`\` 标记
3. 使用双引号，不要使用单引号
4. 最后一个元素后不要添加逗号
5. 确保所有必需字段都存在且格式正确
6. 字符串中的引号要正确转义

📋 返回格式示例：
{
  "questions": [
    {
      "id": "${dimension}_I001",
      "text": "问题内容",
      "type": "SINGLE_CHOICE",
      "options": ["选项1", "选项2", "选项3", "选项4"],
      "category": "${dimension}_INTELLIGENT",
      "dimension": "${dimension}",
      "weight": 1
    }
  ]
}

📝 问题类型说明：
- SINGLE_CHOICE: 单选题（最常用）
- MULTIPLE_CHOICE: 多选题
- SCALE: 量表题（1-5分）
- TEXT: 文本题
- BOOLEAN: 是非题

📝 内容要求：
1. 问题必须针对公益机构的特点和挑战
2. 体现${dimension}维度的核心评估要素
3. 根据组织画像个性化设计
4. 选项设计要有区分度和专业性
5. 问题ID格式：${dimension}_I001, ${dimension}_I002...

⚠️ 重要提醒：
- 直接返回JSON，不要任何额外的文字说明
- 确保JSON格式完全正确，可以直接被JSON.parse()解析`;

  const contextualPrompt = buildContextualPrompt(dimension, profile);
  const userPrompt = `${contextualPrompt}\n\n请生成${count}道针对性的${dimension}维度评估问题。`;

  console.log(`📏 ${dimension}维度提示词长度:`, systemPrompt.length + userPrompt.length, '字符');

  try {
    const response = await llmClient.call({
      model: 'MiniMax-M1', // 修复大小写
      messages: [
        { role: 'system', content: systemPrompt },
        { role: 'user', content: userPrompt },
      ],
      temperature: 0.8,
      max_tokens: 3000,
      response_format: { type: 'json_object' },
    });

    let responseText = '';
    if (response?.choices?.[0]?.message?.content) {
      responseText = response.choices[0].message.content;
    } else if ((response as any)?.reply) {
      responseText = (response as any).reply;
    } else {
      throw new Error('LLM响应格式异常');
    }

    const parsedResponse = JSON.parse(responseText);
    const questions = parsedResponse.questions || [];

    return questions.map((q: any, index: number) => {
      // 确保问题类型格式正确
      let questionType = q.type || 'SINGLE_CHOICE';

      // 转换小写格式到大写格式
      const typeMapping: Record<string, string> = {
        single_choice: 'SINGLE_CHOICE',
        multiple_choice: 'MULTIPLE_CHOICE',
        scale: 'SCALE',
        text: 'TEXT',
        boolean: 'BOOLEAN',
        ranking: 'RANKING',
      };

      const lowerType = questionType.toLowerCase();
      if (typeMapping[lowerType]) {
        questionType = typeMapping[lowerType];
      }

      console.log(
        `🔧 ${dimension}维度问题${index + 1}: type="${questionType}", text="${q.text?.substring(0, 50)}..."`
      );

      return {
        id: q.id || `${dimension}_I${String(index + 1).padStart(3, '0')}`,
        text: q.text,
        type: questionType,
        options: q.options || [],
        category: `${dimension}_INTELLIGENT`,
        dimension: dimension,
        weight: q.weight || 1,
        source: 'AI_GENERATED' as const,
        order: 1000 + index,
        required: true,
        tags: [`${dimension.toLowerCase()}_dimension`, 'intelligent_generated'],
        metadata: {
          generatedAt: new Date().toISOString(),
          organizationType: profile.organizationType,
          serviceArea: profile.serviceArea,
          developmentStage: profile.developmentStage,
        },
      };
    });
  } finally {
    // 恢复原始API密钥
    if (apiKey && originalApiKey) {
      process.env.MINIMAX_API_KEY = originalApiKey;
    }
  }
}
*/

/**
 * 构建上下文化提示词
 */
function buildContextualPrompt(dimension: string, profile: any): string {
  const serviceArea = Array.isArray(profile.serviceArea)
    ? profile.serviceArea
    : [profile.serviceArea || '社会服务'];

  return `
你是一位专业的公益机构组织能力评估专家，正在为以下公益机构设计${dimension}维度的评估问题：

机构背景：
- 组织类型：${profile.organizationType || '公益组织'}
- 服务领域：${serviceArea.join('、')}
- 组织规模：${profile.organizationScale || '中型'}
- 发展阶段：${profile.developmentStage || '成长期'}
- 运营模式：${profile.operatingModel || '直接服务'}
- 影响力定位：${profile.impactPositioning || '区域影响'}
- 组织文化：${profile.organizationalCulture || '使命驱动'}
- 使命：${profile.mission || '致力于社会公益事业'}

请基于以上背景，生成针对性的${dimension}维度评估问题。`;
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    // 兼容不同的请求格式
    const rawProfile = body.profile || body.prompt || body;

    console.log('🤖 后台API: 开始生成28道智能题目...');
    console.log('🔍 接收到的原始数据:', JSON.stringify(body, null, 2));
    console.log('🔍 解析的profile数据:', JSON.stringify(rawProfile, null, 2));

    // 处理简单的prompt请求（用于测试）
    if (typeof rawProfile === 'string') {
      console.log('🧪 检测到简单prompt请求，生成测试响应');
      return NextResponse.json({
        success: true,
        message: 'AI 生成成功',
        data: {
          background: `基于您的输入"${rawProfile}"，我们为您生成了专业的组织能力评估内容。`,
          questions: [
            {
              id: 'test1',
              text: '您的组织在数字化转型方面的准备程度如何？',
              type: 'multiple_choice',
              options: [
                '非常充分 - 已有完整的数字化战略',
                '比较充分 - 有基本的数字化规划',
                '一般 - 正在考虑数字化转型',
                '不太充分 - 缺乏明确方向',
                '完全没有准备 - 尚未开始'
              ]
            }
          ]
        }
      });
    }

    // 转换组织画像数据为标准格式
    const transformedProfile = ProfileTransformer.transformRawAnswers(rawProfile);
    console.log('✅ 转换后的profile数据:', JSON.stringify(transformedProfile, null, 2));

    // 转换为智能生成器需要的格式
    const profile = {
      organizationType: transformedProfile.organizationType,
      serviceArea: [transformedProfile.serviceArea], // 转换为数组格式
      organizationScale: transformedProfile.teamSize,
      developmentStage: transformedProfile.developmentStage,
      operatingModel: transformedProfile.operatingModel,
      impactPositioning: transformedProfile.impactScope,
      organizationalCulture: transformedProfile.organizationCulture,
      mission: '致力于社会公益事业',
      challenges: [transformedProfile.challengesPriorities],
      goals: [transformedProfile.futureVision],
    };

    console.log('🔄 智能生成器格式的profile:', JSON.stringify(profile, null, 2));

    // 检查是否有有效的API密钥
    const hasValidApiKey = process.env.MINIMAX_API_KEY && process.env.MINIMAX_API_KEY.length > 50;

    if (!hasValidApiKey) {
      console.warn('⚠️ 后台API: 未配置LLM API密钥，使用模板生成');
    }

    // 直接在服务端生成智能题目，避免服务端到服务端的API调用
    const intelligentQuestions = await generateIntelligentQuestionsDirectly(profile);

    console.log('✅ 后台API: 智能题目生成完成:', intelligentQuestions.length, '道');

    return NextResponse.json({
      success: true,
      data: {
        questions: intelligentQuestions,
        count: intelligentQuestions.length,
        generatedAt: new Date().toISOString(),
      },
    });
  } catch (error) {
    console.error('❌ 后台API: 智能题目生成失败:', error);

    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : '后台智能题目生成失败',
      },
      { status: 500 }
    );
  }
}
