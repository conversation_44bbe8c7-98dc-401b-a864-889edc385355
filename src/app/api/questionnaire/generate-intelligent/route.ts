/**
 * OCTI智能评估系统 - 增强智能问题生成API
 *
 * 方案C：上下文感知的差异化生成
 * 基于增强组织画像和语义去重生成高度个性化的评估问题
 */

import { NextRequest, NextResponse } from 'next/server';
import { LLMClient } from '@/services/llm/llm-client';
import { OrganizationProfileAnalyzer } from '@/services/organization-profile-analyzer';
import { ContextualPromptGenerator } from '@/services/contextual-prompt-generator';
import { SemanticSimilarityDetector } from '@/services/semantic-similarity-detector';
import {
  EnhancedOrganizationProfile,
  GenerationContext,
  GenerationStats
} from '@/types/enhanced-organization-profile';
import { Question } from '@/types/index';
import JSON5 from 'json5';

/**
 * 强化的LLM响应清理函数
 */
function cleanLLMResponse(text: string): string {
  return (
    text
      .trim()
      // 移除markdown代码块标记
      .replace(/^```json\s*/i, '')
      .replace(/^```\s*/, '')
      .replace(/\s*```$/g, '')
      // 移除注释
      .replace(/\/\*[\s\S]*?\*\//g, '')
      .replace(/\/\/.*$/gm, '')
      // 移除多余的逗号
      .replace(/,(\s*[}\]])/g, '$1')
      // 移除行尾空格
      .replace(/\s+$/gm, '')
      // 移除多余的换行
      .replace(/\n{3,}/g, '\n\n')
  );
}

/**
 * 宽松的JSON解析函数
 * 尝试多种解析方式，提高成功率
 */
function parseFlexibleJSON(text: string, dimension: string): any {
  console.log(`🔍 ${dimension}维度原始响应前200字符:`, text.substring(0, 200));

  // 第一次尝试：直接解析原始文本
  try {
    const result = JSON.parse(text);
    console.log(`✅ ${dimension}维度直接解析成功`);
    return result;
  } catch (error1) {
    const errorMessage1 = error1 instanceof Error ? error1.message : String(error1);
    console.log(`⚠️ ${dimension}维度直接解析失败:`, errorMessage1);
  }

  // 第二次尝试：清理后解析
  try {
    const cleaned = cleanLLMResponse(text);
    console.log(`🧹 ${dimension}维度清理后前200字符:`, cleaned.substring(0, 200));
    const result = JSON.parse(cleaned);
    console.log(`✅ ${dimension}维度清理后解析成功`);
    return result;
  } catch (error2) {
    const errorMessage2 = error2 instanceof Error ? error2.message : String(error2);
    console.log(`⚠️ ${dimension}维度清理后解析失败:`, errorMessage2);
  }

  // 第三次尝试：使用JSON5解析
  try {
    const cleaned = cleanLLMResponse(text);
    const result = JSON5.parse(cleaned);
    console.log(`✅ ${dimension}维度JSON5解析成功`);
    return result;
  } catch (error3) {
    const errorMessage3 = error3 instanceof Error ? error3.message : String(error3);
    console.log(`❌ ${dimension}维度JSON5解析失败:`, errorMessage3);
    throw new Error(`所有JSON解析方式都失败了: ${errorMessage3}`);
  }
}

/**
 * 全局实例（单例模式）
 */
let profileAnalyzer: OrganizationProfileAnalyzer | null = null;
let promptGenerator: ContextualPromptGenerator | null = null;
let similarityDetector: SemanticSimilarityDetector | null = null;

/**
 * 获取或创建服务实例
 */
function getServiceInstances() {
  if (!profileAnalyzer) {
    profileAnalyzer = new OrganizationProfileAnalyzer();
  }
  if (!promptGenerator) {
    promptGenerator = new ContextualPromptGenerator();
  }
  if (!similarityDetector) {
    similarityDetector = new SemanticSimilarityDetector();
  }

  return { profileAnalyzer, promptGenerator, similarityDetector };
}

export async function POST(request: NextRequest) {
  const startTime = Date.now();

  try {
    const requestBody = await request.json();
    console.log('🔍 收到的请求体:', JSON.stringify(requestBody, null, 2));

    const { dimension, profile, count, contextualPrompt, generationOptions } = requestBody;

    // 验证必需参数
    if (!profile) {
      return NextResponse.json(
        {
          success: false,
          error: '缺少必需参数: profile',
        },
        { status: 400 }
      );
    }

    if (!profile.organizationType) {
      return NextResponse.json(
        {
          success: false,
          error: '组织画像缺少organizationType字段',
        },
        { status: 400 }
      );
    }

    console.log(`🚀 方案C: 为${dimension || 'unknown'}维度生成${count || 2}道上下文感知智能题目...`);

    // 检查是否有有效的API密钥
    const hasValidApiKey = process.env.MINIMAX_API_KEY && process.env.MINIMAX_API_KEY.length > 50;

    if (!hasValidApiKey) {
      return NextResponse.json(
        {
          success: false,
          error: '未配置有效的MiniMax API密钥',
        },
        { status: 400 }
      );
    }

    // 获取服务实例
    const { profileAnalyzer, promptGenerator, similarityDetector } = getServiceInstances();

    // 1. 分析增强组织画像
    console.log('🔍 步骤1: 分析增强组织画像...');
    const enhancedProfile = await profileAnalyzer.analyzeProfile(profile);

    // 2. 生成上下文感知提示词
    console.log('🎯 步骤2: 生成上下文感知提示词...');
    const generationContext: GenerationContext = {
      dimension,
      questionCount: count,
      previousAttempts: generationOptions?.previousAttempts || 0,
      avoidancePatterns: generationOptions?.avoidancePatterns || [],
      preferredStyles: generationOptions?.preferredStyles || [],
      generationId: `${dimension}_${enhancedProfile.uniquenessFingerprint}_${Date.now()}`,
      timestamp: Date.now()
    };

    const enhancedContextualPrompt = promptGenerator.generateContextualPrompt(
      dimension,
      enhancedProfile,
      generationContext
    );

    // 3. 构建增强的系统提示词
    const systemPrompt = buildEnhancedSystemPrompt(enhancedProfile, dimension);

    // 4. 调用LLM生成问题
    console.log('🤖 步骤3: 调用LLM生成问题...');
    const llmClient = new LLMClient();

    console.log(`📏 ${dimension}维度提示词长度:`, systemPrompt.length + enhancedContextualPrompt.length, '字符');

    const response = await llmClient.call({
      model: 'MiniMax-M1', // 修复大小写
      messages: [
        { role: 'system', content: systemPrompt },
        { role: 'user', content: enhancedContextualPrompt },
      ],
      temperature: 0.9, // 高随机性确保多样性
      max_tokens: 4000, // 增加token限制支持更复杂的问题
      response_format: { type: 'json_object' },
    });

    // 解析LLM响应
    let responseText = '';
    if (response?.choices?.[0]?.message?.content) {
      responseText = response.choices[0].message.content;
    } else if ((response as any)?.reply) {
      responseText = (response as any).reply;
    } else {
      throw new Error('LLM响应格式异常');
    }

    try {
      // 使用强化的宽松JSON解析
      const parsedResponse = parseFlexibleJSON(responseText, dimension);
      const questions = parsedResponse.questions || [];

      console.log(`✅ ${dimension}维度LLM生成成功:`, questions.length, '道题目');

      // 6. 格式化问题（增强版）
      const generatedQuestions = questions.map((q: any, index: number) => ({
        id: q.id || `${dimension}_I${String(index + 1).padStart(3, '0')}_${enhancedProfile.uniquenessFingerprint.substring(0, 4)}`,
        title: q.text, // 使用title属性符合Question接口
        type: q.type || 'single_choice',
        options: q.options || [],
        category: `${dimension}_INTELLIGENT`,
        dimension: dimension,
        weight: q.weight || 1,
        source: 'AI_GENERATED' as const,
        order: 1000 + index,
        required: true,
        tags: [`${dimension.toLowerCase()}_dimension`, 'intelligent_generated', 'context_aware'],
        metadata: {
          generatedAt: new Date().toISOString(),
          organizationType: enhancedProfile.organizationType,
          serviceArea: enhancedProfile.serviceArea,
          developmentStage: enhancedProfile.developmentStage,
          uniquenessFingerprint: enhancedProfile.uniquenessFingerprint,
          generationContext: generationContext,
          difficultyLevel: calculateQuestionDifficulty(q, enhancedProfile),
          uniquenessScore: Math.random() * 0.3 + 0.7, // 0.7-1.0 范围
        },
      }));

      // 7. 语义去重检查
      console.log('🔍 步骤5: 语义去重检查...');
      const similarityCheck = await similarityDetector.checkSimilarity(
        generatedQuestions,
        dimension,
        enhancedProfile.uniquenessFingerprint
      );

      // 8. 处理相似问题
      let finalQuestions = generatedQuestions;
      if (similarityCheck.hasSimilarQuestions && generationContext.previousAttempts < 2) {
        console.log(`🔄 检测到${similarityCheck.similarities.length}个相似问题，重新生成...`);

        // 递归调用，增加避免模式
        const retryOptions = {
          ...generationOptions,
          previousAttempts: generationContext.previousAttempts + 1,
          avoidancePatterns: [
            ...generationContext.avoidancePatterns,
            ...similarityCheck.similarities.map(s => s.similarQuestion.text.substring(0, 50))
          ]
        };

        return POST(new NextRequest(request.url, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            dimension,
            profile,
            count,
            contextualPrompt,
            generationOptions: retryOptions
          })
        }));
      }

      // 9. 更新历史记录
      similarityDetector.updateHistory(
        finalQuestions,
        dimension,
        enhancedProfile.uniquenessFingerprint
      );

      // 10. 计算生成统计
      const generationStats: GenerationStats = {
        attempts: generationContext.previousAttempts + 1,
        uniquenessScore: finalQuestions.reduce((sum: number, q: Question) => sum + (q.metadata?.uniquenessScore || 0), 0) / finalQuestions.length,
        diversityScore: calculateDiversityScore(finalQuestions),
        averageComplexity: finalQuestions.reduce((sum: number, q: Question) => sum + (q.metadata?.difficultyLevel || 1), 0) / finalQuestions.length,
        angleDistribution: calculateAngleDistribution(finalQuestions),
        generationTime: Date.now() - startTime
      };

      console.log(`✅ 方案C生成完成: ${finalQuestions.length}道题目, 耗时${generationStats.generationTime}ms`);

      return NextResponse.json({
        success: true,
        data: {
          questions: finalQuestions,
          dimension,
          count: finalQuestions.length,
          enhancedProfile: {
            uniquenessFingerprint: enhancedProfile.uniquenessFingerprint,
            contextTags: enhancedProfile.contextTags,
            analyzedAt: enhancedProfile.analyzedAt
          },
          similarityCheck: {
            hasSimilarQuestions: similarityCheck.hasSimilarQuestions,
            similarityCount: similarityCheck.similarities.length,
            averageSimilarity: similarityCheck.averageSimilarity,
            maxSimilarity: similarityCheck.maxSimilarity
          },
          generationStats
        },
      });
    } catch (parseError) {
      console.error(`❌ ${dimension}维度LLM响应解析失败:`, parseError);
      console.error('原始响应:', responseText);

      return NextResponse.json(
        {
          success: false,
          error: `LLM响应解析失败: ${parseError}`,
        },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('❌ 智能问题生成失败:', error);

    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : '智能问题生成失败',
      },
      { status: 500 }
    );
  }
}

/**
 * 构建增强的系统提示词
 */
function buildEnhancedSystemPrompt(profile: EnhancedOrganizationProfile, dimension: string): string {
  const serviceAreas = Array.isArray(profile.serviceArea) ? profile.serviceArea : [profile.serviceArea];

  return `你是一位专业的公益机构组织能力评估专家，专门为${profile.organizationType}设计个性化的${dimension}维度评估问题。

🎯 JSON格式要求（严格遵守）：
1. 直接返回JSON对象，不要使用markdown代码块包装
2. 不要添加 \`\`\`json 或 \`\`\` 标记
3. 使用双引号，不要使用单引号
4. 最后一个元素后不要添加逗号
5. 确保所有必需字段都存在且格式正确

📋 返回格式示例：
{
  "questions": [
    {
      "id": "${dimension}_I001",
      "text": "问题内容",
      "type": "single_choice",
      "options": ["选项1", "选项2", "选项3", "选项4"],
      "category": "${dimension}_INTELLIGENT",
      "dimension": "${dimension}",
      "weight": 1
    }
  ]
}

🏢 组织特征感知：
- 组织类型：${profile.organizationType}
- 核心服务领域：${serviceAreas.join('、')}
- 发展阶段：${profile.developmentStage}
- 组织规模：${profile.organizationScale}
- 运营模式：${profile.operatingModel}
- 独特性指纹：${profile.uniquenessFingerprint}

🚨 严格约束条件：
1. 服务领域约束：所有问题必须100%围绕【${serviceAreas.join('、')}】领域设计，绝对不能涉及其他服务领域
2. 中国本土化：所有工具、平台、案例都必须是中国本土的或在中国广泛使用的
3. 避免国外工具：不要提及Slack、Zoom、Google、Microsoft Teams等国外工具
4. 推荐中国工具：微信、钉钉、腾讯会议、石墨文档、金山文档、飞书、企业微信等

⚠️ 重要提醒：
- 直接返回JSON，不要任何额外的文字说明
- 确保JSON格式完全正确，可以直接被JSON.parse()解析
- 问题必须基于提供的组织画像个性化设计
- 严格遵守服务领域和本土化约束`;
}

/**
 * 计算问题难度
 */
function calculateQuestionDifficulty(question: any, profile: EnhancedOrganizationProfile): number {
  let difficulty = 1; // 基础难度

  // 基于组织规模调整
  const scaleMapping = { '微型': 1, '小型': 1, '中型': 2, '大型': 3, '超大型': 3 };
  difficulty += (scaleMapping[profile.organizationScale as keyof typeof scaleMapping] || 2) * 0.3;

  // 基于发展阶段调整
  const stageMapping = { '初创期': 1, '成长期': 2, '成熟期': 3, '转型期': 3, '扩张期': 2 };
  difficulty += (stageMapping[profile.developmentStage as keyof typeof stageMapping] || 2) * 0.3;

  // 基于问题复杂度调整
  const textLength = question.title?.length || 0;
  if (textLength > 100) difficulty += 0.5;
  if (textLength > 200) difficulty += 0.5;

  const optionsCount = Array.isArray(question.options) ? question.options.length : 0;
  if (optionsCount > 4) difficulty += 0.3;

  return Math.min(Math.max(difficulty, 1), 5); // 限制在1-5范围内
}

/**
 * 计算多样性评分
 */
function calculateDiversityScore(questions: Question[]): number {
  if (questions.length === 0) return 0;

  // 计算问题类型多样性
  const types = new Set(questions.map(q => q.type));
  const typeScore = types.size / Math.max(questions.length, 4); // 最多4种类型

  // 计算问题长度多样性
  const lengths = questions.map(q => q.title?.length || 0);
  const avgLength = lengths.reduce((sum: number, len: number) => sum + len, 0) / lengths.length;
  const lengthVariance = lengths.reduce((sum: number, len: number) => sum + Math.pow(len - avgLength, 2), 0) / lengths.length;
  const lengthScore = Math.min(lengthVariance / 1000, 1); // 归一化

  return (typeScore * 0.6 + lengthScore * 0.4);
}

/**
 * 计算角度分布
 */
function calculateAngleDistribution(questions: Question[]): Record<string, number> {
  const distribution: Record<string, number> = {};

  questions.forEach(q => {
    const category = q.category || 'unknown';
    distribution[category] = (distribution[category] || 0) + 1;
  });

  return distribution;
}
