/**
 * OCTI智能评估系统 - 测试API
 * 用于验证系统基本功能
 */

import { NextRequest, NextResponse } from 'next/server';

/**
 * 测试API接口
 * GET /api/test
 */
export async function GET() {
  try {
    const testData = {
      success: true,
      message: 'OCTI系统测试API正常',
      timestamp: new Date().toISOString(),
      system: {
        node_version: process.version,
        uptime: process.uptime(),
        memory: process.memoryUsage(),
      },
      environment: {
        node_env: process.env.NODE_ENV,
        has_database_url: !!process.env.DATABASE_URL,
        has_redis_url: !!process.env.REDIS_URL,
        has_minimax_key: !!process.env.MINIMAX_API_KEY,
        has_deepseek_key: !!process.env.DEEPSEEK_API_KEY,
      }
    };

    return NextResponse.json(testData);
  } catch (error) {
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

/**
 * 测试POST请求
 * POST /api/test
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    return NextResponse.json({
      success: true,
      message: 'POST请求测试成功',
      received_data: body,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'POST request failed',
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}
