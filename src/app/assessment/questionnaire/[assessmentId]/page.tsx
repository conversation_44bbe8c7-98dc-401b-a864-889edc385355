/**
 * OCTI智能评估系统 - 问卷答题页面
 *
 * 用户填写评估问卷的主要界面
 */

'use client';

import React, { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { UserLayout, UserPageContainer, StepIndicator } from '@/components/layouts/user-layout';
import { QuestionnaireWizard } from '@/components/questionnaire/questionnaire-wizard';
import { Question, QuestionResponse } from '@/components/questionnaire/question-renderer';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { getAllQuestions, getQuestionnaireStats } from '@/data/questionnaire-bank';

// ============================================================================
// 模拟数据
// ============================================================================

// 保留模拟数据以备将来使用 - 用于测试用户认证功能
/*
const mockUser = {
  id: '1',
  name: '张三',
  email: '<EMAIL>',
  role: 'USER',
};
*/

// 获取问卷统计信息
const questionnaireStats = getQuestionnaireStats();

// 模拟问卷数据
const mockQuestionnaire = {
  id: 'questionnaire-1',
  title: '公益机构组织能力评估问卷',
  description: `基于您的组织画像生成的个性化评估问卷，包含${questionnaireStats.preset}道预设标准题目和${questionnaireStats.intelligent}道AI智能生成题目，共${questionnaireStats.total}道题目。`,
  totalQuestions: questionnaireStats.total,
  estimatedDuration: Math.ceil(questionnaireStats.total * 1.5), // 每题约1.5分钟
  dimensions: questionnaireStats.byDimension,
};

// 模拟已有回答
const mockInitialResponses: QuestionResponse[] = [];

// ============================================================================
// 问卷答题页面组件
// ============================================================================

export default function QuestionnairePage() {
  const params = useParams();
  const router = useRouter();
  const assessmentId = params.assessmentId as string;

  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');
  const [questionnaire, setQuestionnaire] = useState(mockQuestionnaire);
  const [questions, setQuestions] = useState<Question[]>([]);
  const [initialResponses, setInitialResponses] = useState<QuestionResponse[]>([]);

  // 加载问卷数据
  useEffect(() => {
    const loadQuestionnaire = async () => {
      try {
        setIsLoading(true);

        // TODO: 实际的API调用
        // const response = await fetch(`/api/assessments/${assessmentId}/questionnaire`);
        // const data = await response.json();

        console.log('🔄 开始加载问卷...');

        // 🎯 优化用户体验：先展示32道预设题目，后台异步生成28道智能题目

        // 1. 立即加载并展示32道预设题目
        const presetQuestions = getAllQuestions().filter(q => q.source === 'PRESET');
        console.log('📊 立即展示预设题目:', presetQuestions.length, '道');

        setQuestionnaire(mockQuestionnaire);
        setQuestions(presetQuestions); // 先展示预设题目
        setInitialResponses(mockInitialResponses);

        // 2. 后台异步生成智能题目
        console.log('🤖 后台开始生成智能题目...');
        generateIntelligentQuestionsInBackground(presetQuestions);
      } catch (err) {
        setError('加载问卷失败，请刷新页面重试');
        console.error('加载问卷失败:', err);
      } finally {
        setIsLoading(false);
      }
    };

    if (assessmentId) {
      loadQuestionnaire();
    }
  }, [assessmentId]);

  /**
   * 后台异步生成智能题目
   */
  const generateIntelligentQuestionsInBackground = async (presetQuestions: Question[]) => {
    try {
      console.log('🤖 后台异步生成28道智能题目...');

      // 获取组织画像
      const profileData = localStorage.getItem('profileAnswers');
      if (!profileData) {
        console.warn('⚠️ 未找到组织画像，无法生成智能题目');
        return;
      }

      const profile = JSON.parse(profileData);

      // 创建AbortController用于超时控制
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 360000); // 6分钟超时（与智能问题生成器保持一致）

      try {
        // 调用后台API生成智能题目
        const response = await fetch('/api/questionnaire/generate-background', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ profile }),
          signal: controller.signal,
        });

        clearTimeout(timeoutId);

        if (!response.ok) {
          const errorText = await response.text();
          throw new Error(`后台API调用失败: ${response.status} - ${errorText}`);
        }

        const result = await response.json();

        if (!result.success) {
          throw new Error(result.error || '后台智能题目生成失败');
        }

        const intelligentQuestions = result.data.questions;

        console.log('✅ 后台智能题目生成成功:', intelligentQuestions.length, '道');

        // 合并预设题目和智能题目
        const allQuestions = [...presetQuestions, ...intelligentQuestions].sort(
          (a, b) => a.order - b.order
        );

        console.log('📊 问卷更新完成:', {
          总题目: allQuestions.length,
          预设题目: presetQuestions.length,
          智能生成: intelligentQuestions.length,
        });

        // 重新计算问卷统计信息
        const updatedDimensions = {
          SF: allQuestions.filter(q => q.dimension === 'SF').length,
          IT: allQuestions.filter(q => q.dimension === 'IT').length,
          MV: allQuestions.filter(q => q.dimension === 'MV').length,
          AD: allQuestions.filter(q => q.dimension === 'AD').length,
        };

        const updatedQuestionnaire = {
          ...questionnaire,
          totalQuestions: allQuestions.length,
          dimensions: updatedDimensions,
          estimatedDuration: Math.ceil(allQuestions.length * 1.5), // 每题1.5分钟
        };

        // 更新问卷题目和统计信息
        setQuestions(allQuestions);
        setQuestionnaire(updatedQuestionnaire);

        console.log('✅ 问卷界面已更新，新的题目统计:', updatedDimensions);
      } catch (fetchError) {
        clearTimeout(timeoutId);
        throw fetchError;
      }
    } catch (error) {
      console.warn('⚠️ 后台智能题目生成失败，继续使用预设题目:', error);

      // 根据错误类型提供更详细的日志
      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          console.warn('⏰ 智能题目生成超时（3分钟），使用预设题目');
        } else if (error.message.includes('Failed to fetch')) {
          console.warn('🌐 网络连接问题，无法生成智能题目');
        } else {
          console.warn('❌ 智能题目生成错误:', error.message);
        }
      }

      // 不影响用户答题，继续使用预设题目
    }
  };

  // 保存草稿
  const handleSave = async (responses: QuestionResponse[]) => {
    try {
      const sessionId = assessmentId || 'default';
      const profileData = localStorage.getItem('profileAnswers');
      const profile = profileData ? JSON.parse(profileData) : null;

      await fetch('/api/assessment/responses', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          sessionId,
          profile,
          responses,
          status: 'DRAFT',
          metadata: {
            totalQuestions: questions.length,
            presetQuestions: questions.filter(q => q.source === 'PRESET').length,
            intelligentQuestions: questions.filter(q => q.source === 'AI_GENERATED').length,
          },
        }),
      });

      console.log('✅ 草稿保存成功:', responses.length, '道题目');
    } catch (err) {
      console.error('❌ 保存失败:', err);
      // 不抛出错误，避免影响用户体验
    }
  };

  // 提交问卷
  const handleSubmit = async (responses: QuestionResponse[]) => {
    try {
      console.log('🚀 开始提交问卷...');
      console.log('📝 回答数量:', responses.length);
      console.log('📊 回答详情:', responses);

      const sessionId = assessmentId || 'default';
      const profileData = localStorage.getItem('profileAnswers');
      const profile = profileData ? JSON.parse(profileData) : null;

      // 保存到服务器
      await fetch('/api/assessment/responses', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          sessionId,
          profile,
          responses,
          status: 'COMPLETED',
          metadata: {
            totalQuestions: questions.length,
            presetQuestions: questions.filter(q => q.source === 'PRESET').length,
            intelligentQuestions: questions.filter(q => q.source === 'AI_GENERATED').length,
            completedAt: new Date().toISOString(),
          },
        }),
      });

      // 同时保存到本地存储（作为备份）
      localStorage.setItem('assessmentResponses', JSON.stringify(responses));
      localStorage.setItem('assessmentCompletedAt', new Date().toISOString());
      localStorage.setItem('assessmentSessionId', sessionId);

      console.log('✅ 问卷提交成功，准备跳转到结果页面...');

      // 根据选择的版本跳转到不同结果页面
      const selectedVersion = localStorage.getItem('selectedVersion');
      if (selectedVersion === 'professional') {
        console.log('🚀 跳转到专业版结果页面');
        router.push('/assessment/results/professional');
      } else {
        console.log('🚀 跳转到标准版结果页面');
        router.push('/assessment/results/standard');
      }
    } catch (err) {
      console.error('❌ 提交失败:', err);
      alert('提交失败，请重试');
      throw err;
    }
  };

  // 退出问卷
  const handleExit = () => {
    if (confirm('确定要退出问卷吗？未保存的答案将会丢失。')) {
      router.push('/assessment/start');
    }
  };

  if (isLoading) {
    return (
      <UserLayout>
        <UserPageContainer>
          <div className='flex items-center justify-center py-12'>
            <div className='text-center'>
              <div className='animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4'></div>
              <p className='text-gray-600'>加载问卷中...</p>
            </div>
          </div>
        </UserPageContainer>
      </UserLayout>
    );
  }

  if (error) {
    return (
      <UserLayout showBackButton backHref='/assessment/start'>
        <UserPageContainer>
          <Alert className='max-w-2xl mx-auto'>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        </UserPageContainer>
      </UserLayout>
    );
  }

  return (
    <UserLayout
      showBackButton
      backHref='/assessment/profile'
      title='组织能力评估'
      subtitle='请根据您组织的实际情况如实回答以下问题'
    >
      <UserPageContainer>
        {/* 步骤指示器 */}
        <StepIndicator
          currentStep={2}
          totalSteps={4}
          steps={['组织画像', '能力评估', '结果分析', '发展建议']}
        />

        {/* 问卷信息展示 */}
        <div className='mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg'>
          <div className='flex items-center justify-between'>
            <div>
              <h3 className='font-semibold text-blue-900'>📊 OCTI混合问卷系统 v4.0</h3>
              <p className='text-sm text-blue-700 mt-1'>{questionnaire.description}</p>
            </div>
            <div className='text-right'>
              <div className='text-2xl font-bold text-blue-600'>{questionnaire.totalQuestions}</div>
              <div className='text-xs text-blue-500'>道题目</div>
              <div className='text-xs text-blue-500 mt-1'>
                约{questionnaire.estimatedDuration}分钟
              </div>
            </div>
          </div>
          <div className='mt-3 grid grid-cols-4 gap-2 text-xs'>
            <div className='bg-white px-2 py-1 rounded text-center'>
              <div className='font-semibold text-orange-600'>SF维度</div>
              <div className='text-orange-500'>{questionnaire.dimensions.SF}题</div>
            </div>
            <div className='bg-white px-2 py-1 rounded text-center'>
              <div className='font-semibold text-green-600'>IT维度</div>
              <div className='text-green-500'>{questionnaire.dimensions.IT}题</div>
            </div>
            <div className='bg-white px-2 py-1 rounded text-center'>
              <div className='font-semibold text-purple-600'>MV维度</div>
              <div className='text-purple-500'>{questionnaire.dimensions.MV}题</div>
            </div>
            <div className='bg-white px-2 py-1 rounded text-center'>
              <div className='font-semibold text-blue-600'>AD维度</div>
              <div className='text-blue-500'>{questionnaire.dimensions.AD}题</div>
            </div>
          </div>
        </div>

        <QuestionnaireWizard
          questionnaire={questionnaire}
          questions={questions}
          initialResponses={initialResponses}
          onSave={handleSave}
          onSubmit={handleSubmit}
          onExit={handleExit}
          autoSave={true}
        />
      </UserPageContainer>
    </UserLayout>
  );
}
