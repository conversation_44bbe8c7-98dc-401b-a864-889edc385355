'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { UserLayout, UserPageContainer, StepIndicator } from '@/components/layouts/user-layout';
import { Button } from '@/components/ui/button';

import { Download, Share2, <PERSON>ader2, <PERSON><PERSON>, Brain } from 'lucide-react';
import { AdvancedAnalysisRenderer } from '@/components/ui/advanced-analysis-renderer';
import jsPDF from 'jspdf';

// 专业版分析结果接口
interface ProfessionalAnalysisResult {
  organizationType: {
    code: string;
    name: string;
    description: string;
    characteristics: string[];
    strengths: string[];
    challenges: string[];
  };
  basicAnalysis: string;
  enhancedAnalysis: string;
  timestamp: string;
  version: string;
  processingSteps: string[];
}

/**
 * 专业版评估结果页面
 */
export default function ProfessionalResultsPage() {
  const [activeTab, setActiveTab] = useState<'overview' | 'basic' | 'enhanced'>('overview');
  const [analysisResult, setAnalysisResult] = useState<ProfessionalAnalysisResult | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isDownloading, setIsDownloading] = useState(false);
  const [analysisProgress, setAnalysisProgress] = useState<string>('准备分析...');

  // 获取分析结果
  useEffect(() => {
    const fetchAnalysisResult = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // 优先从localStorage获取数据
        let profileData = localStorage.getItem('profileAnswers');
        let responsesData = localStorage.getItem('assessmentResponses');

        // 如果本地没有数据，尝试从服务器恢复
        if (!profileData || !responsesData) {
          console.log('🔄 本地数据缺失，尝试从服务器恢复...');
          const sessionId = localStorage.getItem('assessmentSessionId') || 'default';

          try {
            const response = await fetch(`/api/assessment/responses?sessionId=${sessionId}`);
            if (response.ok) {
              const serverData = await response.json();
              if (serverData.success && serverData.data) {
                profileData = JSON.stringify(serverData.data.profile);
                responsesData = JSON.stringify(serverData.data.responses);
                console.log('✅ 从服务器恢复数据成功');

                // 更新本地存储
                localStorage.setItem('profileAnswers', profileData);
                localStorage.setItem('assessmentResponses', responsesData);
              }
            }
          } catch (serverError) {
            console.warn('⚠️ 服务器数据恢复失败:', serverError);
          }
        }

        if (!profileData || !responsesData) {
          throw new Error('缺少评估数据，请重新完成评估');
        }

        const profile = JSON.parse(profileData);
        const responses = JSON.parse(responsesData);

        console.log('📊 开始请求专业版分析结果...');
        setAnalysisProgress('正在进行组织类型判断...');

        // 创建带超时的AbortController
        const controller = new AbortController();
        const timeoutId = setTimeout(() => {
          controller.abort();
          console.warn('⚠️ 专业版分析请求超时（8分钟）');
        }, 480000); // 8分钟超时，比后端稍长

        // 模拟进度更新
        const progressUpdates = [
          { delay: 2000, message: '正在进行组织能力评估师基础分析...' },
          { delay: 120000, message: '基础分析进行中，请耐心等待...' },
          { delay: 240000, message: '正在进行组织能力分析师深度推理...' },
          { delay: 360000, message: '深度分析即将完成...' },
        ];

        progressUpdates.forEach(({ delay, message }) => {
          setTimeout(() => {
            if (!controller.signal.aborted) {
              setAnalysisProgress(message);
            }
          }, delay);
        });

        try {
          // 调用专业版双模型分析API
          const response = await fetch('/api/assessment/professional-analyze', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              profile,
              responses,
              version: 'professional',
            }),
            signal: controller.signal, // 添加超时控制
          });

          clearTimeout(timeoutId); // 清除超时定时器

          if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error || '分析服务暂时不可用');
          }

          const result = await response.json();

          if (!result.success) {
            throw new Error(result.error || '分析失败');
          }

          // 成功获取结果，立即清除超时定时器
          clearTimeout(timeoutId);

          setAnalysisResult(result.data);
          setError(null); // 清除任何之前的错误状态
          console.log('✅ 专业版分析结果获取成功:', result.data);
          console.log('🔄 状态更新: analysisResult已设置, error已清除');

        } catch (fetchError) {
          clearTimeout(timeoutId); // 确保清除超时定时器
          throw fetchError; // 重新抛出错误供外层catch处理
        }
      } catch (err) {
        console.error('❌ 获取分析结果失败:', err);

        // 根据错误类型提供更具体的错误信息
        let errorMessage = '获取分析结果失败';
        if (err instanceof Error) {
          if (err.name === 'AbortError') {
            errorMessage = '专业版分析超时，请稍后重试（分析时间较长，约需5-8分钟）';
          } else if (err.message.includes('Failed to fetch')) {
            errorMessage = '网络连接失败，请检查网络连接后重试';
          } else {
            errorMessage = err.message;
          }
        }

        setError(errorMessage);
      } finally {
        setIsLoading(false);
      }
    };

    fetchAnalysisResult();
  }, []);

  // 下载PDF报告功能
  const handleDownloadReport = async () => {
    if (!analysisResult) return;

    setIsDownloading(true);
    try {
      // 生成专业版PDF报告
      const pdf = generateProfessionalPDFReport(analysisResult);

      // 下载PDF文件
      const fileName = `OCTI专业版组织能力评估报告_${new Date().toLocaleDateString().replace(/\//g, '-')}.pdf`;
      pdf.save(fileName);

      console.log('✅ 专业版PDF报告下载成功');
    } catch (error) {
      console.error('❌ 专业版PDF报告下载失败:', error);
      alert('报告下载失败，请重试');
    } finally {
      setIsDownloading(false);
    }
  };

  // 生成专业版PDF报告
  const generateProfessionalPDFReport = (result: ProfessionalAnalysisResult): jsPDF => {
    const pdf = new jsPDF();
    const timestamp = new Date().toLocaleString();
    let yPosition = 20;

    // 设置字体
    pdf.setFont('helvetica');

    // 标题
    pdf.setFontSize(20);
    pdf.text('OCTI专业版组织能力评估报告', 20, yPosition);
    yPosition += 15;

    // 分隔线
    pdf.setLineWidth(0.5);
    pdf.line(20, yPosition, 190, yPosition);
    yPosition += 15;

    // 基本信息
    pdf.setFontSize(12);
    pdf.text(`生成时间: ${timestamp}`, 20, yPosition);
    yPosition += 8;
    pdf.text(
      `组织类型: ${result.organizationType.name} (${result.organizationType.code})`,
      20,
      yPosition
    );
    yPosition += 8;
    pdf.text(`分析版本: ${result.version}`, 20, yPosition);
    yPosition += 15;

    // 组织类型描述
    pdf.setFontSize(14);
    pdf.text('组织类型描述', 20, yPosition);
    yPosition += 10;
    pdf.setFontSize(10);
    const descriptionLines = pdf.splitTextToSize(result.organizationType.description, 170);
    pdf.text(descriptionLines, 20, yPosition);
    yPosition += descriptionLines.length * 5 + 10;

    // 组织特征
    pdf.setFontSize(14);
    pdf.text('组织特征', 20, yPosition);
    yPosition += 10;
    pdf.setFontSize(10);
    result.organizationType.characteristics.forEach(characteristic => {
      if (yPosition > 270) {
        pdf.addPage();
        yPosition = 20;
      }
      pdf.text(`• ${characteristic}`, 25, yPosition);
      yPosition += 7;
    });
    yPosition += 5;

    // 组织优势
    pdf.setFontSize(14);
    pdf.text('组织优势', 20, yPosition);
    yPosition += 10;
    pdf.setFontSize(10);
    result.organizationType.strengths.forEach(strength => {
      if (yPosition > 270) {
        pdf.addPage();
        yPosition = 20;
      }
      pdf.text(`• ${strength}`, 25, yPosition);
      yPosition += 7;
    });
    yPosition += 5;

    // 面临挑战
    pdf.setFontSize(14);
    pdf.text('面临挑战', 20, yPosition);
    yPosition += 10;
    pdf.setFontSize(10);
    result.organizationType.challenges.forEach(challenge => {
      if (yPosition > 270) {
        pdf.addPage();
        yPosition = 20;
      }
      pdf.text(`• ${challenge}`, 25, yPosition);
      yPosition += 7;
    });
    yPosition += 10;

    // 基础分析（组织能力评估师）
    if (yPosition > 250) {
      pdf.addPage();
      yPosition = 20;
    }
    pdf.setFontSize(14);
    pdf.text('组织能力评估师 基础分析', 20, yPosition);
    yPosition += 10;
    pdf.setFontSize(10);
    const basicAnalysisLines = pdf.splitTextToSize(result.basicAnalysis, 170);
    basicAnalysisLines.forEach((line: string) => {
      if (yPosition > 270) {
        pdf.addPage();
        yPosition = 20;
      }
      pdf.text(line, 20, yPosition);
      yPosition += 5;
    });
    yPosition += 10;

    // 深度推理分析（组织能力分析师）
    if (yPosition > 250) {
      pdf.addPage();
      yPosition = 20;
    }
    pdf.setFontSize(14);
    pdf.text('组织能力分析师 深度推理分析', 20, yPosition);
    yPosition += 10;
    pdf.setFontSize(10);
    const enhancedAnalysisLines = pdf.splitTextToSize(result.enhancedAnalysis, 170);
    enhancedAnalysisLines.forEach((line: string) => {
      if (yPosition > 270) {
        pdf.addPage();
        yPosition = 20;
      }
      pdf.text(line, 20, yPosition);
      yPosition += 5;
    });
    yPosition += 10;

    // 处理步骤
    if (yPosition > 250) {
      pdf.addPage();
      yPosition = 20;
    }
    pdf.setFontSize(14);
    pdf.text('处理步骤', 20, yPosition);
    yPosition += 10;
    pdf.setFontSize(10);
    result.processingSteps.forEach(step => {
      if (yPosition > 270) {
        pdf.addPage();
        yPosition = 20;
      }
      pdf.text(`• ${step}`, 25, yPosition);
      yPosition += 7;
    });

    // 页脚
    const pageCount = pdf.getNumberOfPages();
    for (let i = 1; i <= pageCount; i++) {
      pdf.setPage(i);
      pdf.setFontSize(8);
      pdf.text('报告由OCTI智能评估系统专业版生成', 20, 285);
      pdf.text('双智能体分析：组织能力评估师 + 组织能力分析师', 20, 290);
      pdf.text(`第 ${i} 页，共 ${pageCount} 页`, 170, 290);
    }

    return pdf;
  };

  // 调试信息
  console.log('🔍 渲染状态检查:', {
    isLoading,
    hasError: !!error,
    hasAnalysisResult: !!analysisResult,
    errorMessage: error
  });

  // 加载状态
  if (isLoading) {
    return (
      <UserLayout
        showBackButton
        backHref='/assessment/questionnaire/assessment-1'
        title='专业版分析结果'
        subtitle='双模型AI正在为您生成深度分析报告...'
      >
        <UserPageContainer>
          <div className='flex items-center justify-center py-20'>
            <div className='text-center space-y-4'>
              <Loader2 className='h-12 w-12 animate-spin text-primary mx-auto' />
              <div>
                <h3 className='text-lg font-semibold'>双智能体协作分析中</h3>
                <p className='text-muted-foreground mb-2'>
                  {analysisProgress}
                </p>
                <p className='text-sm text-muted-foreground'>
                  专业版分析需要5-8分钟，请耐心等待...
                </p>
              </div>
            </div>
          </div>
        </UserPageContainer>
      </UserLayout>
    );
  }

  // 错误状态
  if (error) {
    return (
      <UserLayout
        showBackButton
        backHref='/assessment/questionnaire/assessment-1'
        title='专业版分析结果'
        subtitle='获取分析结果时出现问题'
      >
        <UserPageContainer>
          <div className='text-center py-20'>
            <div className='text-red-500 text-6xl mb-4'>⚠️</div>
            <h3 className='text-lg font-semibold mb-2'>分析失败</h3>
            <p className='text-muted-foreground mb-6'>{error}</p>
            <div className='space-x-4'>
              <Button onClick={() => window.location.reload()}>重试</Button>
              <Link href='/assessment/start'>
                <Button variant='outline'>重新开始评估</Button>
              </Link>
            </div>
          </div>
        </UserPageContainer>
      </UserLayout>
    );
  }

  // 没有数据
  if (!analysisResult) {
    return (
      <UserLayout
        showBackButton
        backHref='/assessment/start'
        title='专业版分析结果'
        subtitle='没有找到分析结果'
      >
        <UserPageContainer>
          <div className='text-center py-20'>
            <div className='text-gray-400 text-6xl mb-4'>📊</div>
            <h3 className='text-lg font-semibold mb-2'>暂无分析结果</h3>
            <p className='text-muted-foreground mb-6'>请先完成组织画像和能力评估问卷</p>
            <Link href='/assessment/start'>
              <Button>开始评估</Button>
            </Link>
          </div>
        </UserPageContainer>
      </UserLayout>
    );
  }

  return (
    <UserLayout
      showBackButton
      backHref='/assessment/questionnaire/assessment-1'
      title='专业版分析结果'
      subtitle='恭喜您完成评估！以下是您的深度组织能力分析报告'
    >
      <UserPageContainer>
        {/* 版本标识 */}
        <div className='mb-6'>
          <div className='inline-flex items-center px-4 py-2 bg-gradient-to-r from-purple-100 to-pink-100 text-purple-700 rounded-full text-sm font-semibold border border-purple-200'>
            <Zap className='h-4 w-4 mr-2' />
            专业版分析 - 双智能体协作分析
          </div>
        </div>

        {/* 步骤指示器 */}
        <StepIndicator
          currentStep={4}
          totalSteps={4}
          steps={['组织信息', '能力评估', '结果分析', '发展建议']}
        />

        {/* 分析进度展示 */}
        <div className='bg-white rounded-2xl shadow-lg p-6 mb-8'>
          <h2 className='text-xl font-bold text-gray-900 mb-4'>分析处理步骤</h2>
          <div className='space-y-2'>
            {analysisResult.processingSteps.map((step, index) => (
              <div key={index} className='flex items-center text-sm'>
                <div className='w-6 h-6 bg-green-100 text-green-600 rounded-full flex items-center justify-center text-xs font-bold mr-3'>
                  ✓
                </div>
                <span className='text-gray-700'>{step}</span>
              </div>
            ))}
          </div>
        </div>

        {/* 标签页导航 */}
        <div className='bg-white rounded-2xl shadow-lg mb-8'>
          <div className='border-b border-gray-200'>
            <nav className='flex space-x-8 px-6'>
              {[
                { key: 'overview', label: '组织类型', icon: '🎯' },
                { key: 'basic', label: '组织能力评估师', icon: '🤖' },
                { key: 'enhanced', label: '组织能力分析师', icon: '🧠' },
              ].map(tab => (
                <button
                  key={tab.key}
                  onClick={() => setActiveTab(tab.key as any)}
                  className={`py-4 px-2 border-b-2 font-medium text-sm ${
                    activeTab === tab.key
                      ? 'border-primary text-primary'
                      : 'border-transparent text-gray-500 hover:text-gray-700'
                  }`}
                >
                  <span className='mr-2'>{tab.icon}</span>
                  {tab.label}
                </button>
              ))}
            </nav>
          </div>

          <div className='p-6'>
            {/* 组织类型概览 */}
            {activeTab === 'overview' && (
              <div>
                <div className='bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-6 border border-blue-200'>
                  <div className='flex items-center mb-4'>
                    <div className='w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mr-4'>
                      <span className='text-blue-600 font-bold text-lg'>
                        {analysisResult.organizationType.code}
                      </span>
                    </div>
                    <div>
                      <h3 className='text-xl font-bold text-gray-900'>
                        {analysisResult.organizationType.name}
                      </h3>
                      <p className='text-gray-600'>{analysisResult.organizationType.description}</p>
                    </div>
                  </div>

                  <div className='grid md:grid-cols-2 gap-6'>
                    <div>
                      <h4 className='font-semibold text-gray-800 mb-2'>核心优势</h4>
                      <ul className='space-y-1'>
                        {analysisResult.organizationType.strengths.map((strength, index) => (
                          <li key={index} className='text-sm text-gray-600 flex items-start'>
                            <span className='text-green-500 mr-2'>•</span>
                            {strength}
                          </li>
                        ))}
                      </ul>
                    </div>

                    <div>
                      <h4 className='font-semibold text-gray-800 mb-2'>发展挑战</h4>
                      <ul className='space-y-1'>
                        {analysisResult.organizationType.challenges.map((challenge, index) => (
                          <li key={index} className='text-sm text-gray-600 flex items-start'>
                            <span className='text-orange-500 mr-2'>•</span>
                            {challenge}
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* 组织能力评估师分析 */}
            {activeTab === 'basic' && (
              <div>
                <div className='flex items-center mb-6'>
                  <Brain className='h-5 w-5 text-blue-600 mr-2' />
                  <h3 className='text-lg font-semibold text-gray-900'>组织能力评估师 基础分析</h3>
                </div>
                <AdvancedAnalysisRenderer
                  content={analysisResult.basicAnalysis}
                  type='basic'
                  className='text-gray-700'
                />
              </div>
            )}

            {/* 组织能力分析师深度分析 */}
            {activeTab === 'enhanced' && (
              <div>
                <div className='flex items-center mb-6'>
                  <Zap className='h-5 w-5 text-purple-600 mr-2' />
                  <h3 className='text-lg font-semibold text-gray-900'>
                    组织能力分析师 深度推理分析
                  </h3>
                </div>
                <AdvancedAnalysisRenderer
                  content={analysisResult.enhancedAnalysis}
                  type='enhanced'
                  className='text-gray-700'
                />
              </div>
            )}
          </div>
        </div>

        {/* 操作按钮 */}
        <div className='bg-white rounded-2xl shadow-lg p-8 text-center'>
          <div className='flex justify-center space-x-4 mb-6'>
            <Button
              className='flex items-center space-x-2'
              onClick={handleDownloadReport}
              disabled={isDownloading}
            >
              {isDownloading ? (
                <Loader2 className='h-4 w-4 animate-spin' />
              ) : (
                <Download className='h-4 w-4' />
              )}
              <span>{isDownloading ? '下载中...' : '下载完整报告'}</span>
            </Button>
            <Button variant='outline' className='flex items-center space-x-2'>
              <Share2 className='h-4 w-4' />
              <span>分享结果</span>
            </Button>
          </div>

          <p className='text-gray-600 text-sm'>
            完成时间：{new Date(analysisResult.timestamp).toLocaleString('zh-CN')} | 分析版本：
            {analysisResult.version}
          </p>
        </div>
      </UserPageContainer>
    </UserLayout>
  );
}
