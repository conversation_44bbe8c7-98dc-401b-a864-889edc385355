/**
 * OCTI智能评估系统 - 评估开始页面
 *
 * 用户端评估流程的起始页面
 * 收集组织基本信息，开始评估流程
 */

'use client';

import React, { useState, useEffect } from 'react';

import { useSearchParams } from 'next/navigation';
import { ArrowRight, Building, Brain, Zap } from 'lucide-react';
import { UserLayout, UserPageContainer, StepIndicator } from '@/components/layouts/user-layout';

/**
 * 组织类型选项
 */
const ORGANIZATION_TYPES = [
  { value: 'charity', label: '慈善机构', description: '专注于慈善救助和公益服务' },
  { value: 'foundation', label: '基金会', description: '资助型或运作型基金会' },
  { value: 'ngo', label: '民间组织', description: '非政府组织或社会团体' },
  { value: 'social_enterprise', label: '社会企业', description: '以社会目标为导向的企业' },
  { value: 'other', label: '其他', description: '其他类型的公益组织' },
];

/**
 * 组织规模选项
 */
const ORGANIZATION_SCALES = [
  { value: 'small', label: '小型组织', description: '员工人数 1-10 人' },
  { value: 'medium', label: '中型组织', description: '员工人数 11-50 人' },
  { value: 'large', label: '大型组织', description: '员工人数 51-200 人' },
  { value: 'xlarge', label: '超大型组织', description: '员工人数 200+ 人' },
];

/**
 * 评估开始页面组件
 */
export default function AssessmentStartPage() {
  const searchParams = useSearchParams();
  const [selectedVersion, setSelectedVersion] = useState<'standard' | 'professional'>('standard');
  const [formData, setFormData] = useState({
    organizationName: '',
    organizationType: '',
    organizationScale: '',
    location: '',
    establishedYear: '',
    contactPerson: '',
    contactEmail: '',
    mainFocus: '',
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  // 获取URL参数中的版本信息
  useEffect(() => {
    const version = searchParams.get('version') as 'standard' | 'professional';
    if (version === 'standard' || version === 'professional') {
      setSelectedVersion(version);
      // 保存版本选择到localStorage
      localStorage.setItem('selectedVersion', version);
    }
  }, [searchParams]);

  /**
   * 处理表单输入变化
   */
  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // 清除该字段的错误信息
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  /**
   * 验证表单数据
   */
  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.organizationName.trim()) {
      newErrors.organizationName = '请输入组织名称';
    }

    if (!formData.organizationType) {
      newErrors.organizationType = '请选择组织类型';
    }

    if (!formData.organizationScale) {
      newErrors.organizationScale = '请选择组织规模';
    }

    if (!formData.contactEmail.trim()) {
      newErrors.contactEmail = '请输入联系邮箱';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.contactEmail)) {
      newErrors.contactEmail = '请输入有效的邮箱地址';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  /**
   * 处理表单提交
   */
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (validateForm()) {
      // 保存组织信息到本地存储或发送到服务器
      localStorage.setItem('octi_organization_info', JSON.stringify(formData));

      // 跳转到组织画像收集页面
      window.location.href = '/assessment/profile';
    }
  };

  return (
    <UserLayout
      showBackButton
      backHref='/'
      title='开始评估'
      subtitle='请填写您的组织基本信息，我们将为您定制专业的评估问卷'
    >
      <UserPageContainer>
        {/* 版本显示 */}
        <div className='mb-6'>
          <div
            className={`inline-flex items-center px-4 py-2 rounded-full text-sm font-semibold ${
              selectedVersion === 'professional'
                ? 'bg-gradient-to-r from-purple-100 to-pink-100 text-purple-700 border border-purple-200'
                : 'bg-blue-100 text-blue-700 border border-blue-200'
            }`}
          >
            {selectedVersion === 'professional' ? (
              <>
                <Zap className='h-4 w-4 mr-2' />
                专业版分析 - 双智能体协作分析
              </>
            ) : (
              <>
                <Brain className='h-4 w-4 mr-2' />
                标准版分析 - 组织能力评估师
              </>
            )}
          </div>
        </div>

        {/* 步骤指示器 */}
        <StepIndicator
          currentStep={1}
          totalSteps={4}
          steps={['组织信息', '能力评估', '结果分析', '发展建议']}
        />

        {/* 表单区域 */}
        <div className='max-w-2xl mx-auto'>
          <form onSubmit={handleSubmit} className='space-y-6'>
            {/* 组织基本信息 */}
            <div className='octi-card p-6'>
              <h2 className='text-xl font-semibold text-foreground mb-4 flex items-center'>
                <Building className='h-5 w-5 text-primary mr-2' />
                组织基本信息
              </h2>

              <div className='grid md:grid-cols-2 gap-4'>
                <div>
                  <label className='block text-sm font-medium text-foreground mb-2'>
                    组织名称 <span className='text-red-500'>*</span>
                  </label>
                  <input
                    type='text'
                    value={formData.organizationName}
                    onChange={e => handleInputChange('organizationName', e.target.value)}
                    className={`octi-input ${
                      errors.organizationName ? 'border-red-500' : ''
                    }`}
                    placeholder='请输入组织全称'
                  />
                  {errors.organizationName && (
                    <p className='text-red-500 text-sm mt-1'>{errors.organizationName}</p>
                  )}
                </div>

                <div>
                  <label className='block text-sm font-medium text-foreground mb-2'>成立年份</label>
                  <input
                    type='number'
                    value={formData.establishedYear}
                    onChange={e => handleInputChange('establishedYear', e.target.value)}
                    className='octi-input'
                    placeholder='如：2020'
                    min='1900'
                    max={new Date().getFullYear()}
                  />
                </div>

                <div>
                  <label className='block text-sm font-medium text-foreground mb-2'>所在地区</label>
                  <input
                    type='text'
                    value={formData.location}
                    onChange={e => handleInputChange('location', e.target.value)}
                    className='octi-input'
                    placeholder='如：北京市朝阳区'
                  />
                </div>

                <div>
                  <label className='block text-sm font-medium text-foreground mb-2'>
                    主要关注领域
                  </label>
                  <input
                    type='text'
                    value={formData.mainFocus}
                    onChange={e => handleInputChange('mainFocus', e.target.value)}
                    className='octi-input'
                    placeholder='如：教育、环保、扶贫等'
                  />
                </div>
              </div>
            </div>

            {/* 组织类型选择 */}
            <div className='octi-card p-6'>
              <h2 className='text-xl font-semibold text-foreground mb-4'>
                组织类型 <span className='text-red-500'>*</span>
              </h2>
              <div className='grid md:grid-cols-2 gap-3'>
                {ORGANIZATION_TYPES.map(type => (
                  <label
                    key={type.value}
                    className={`flex items-start p-3 border rounded-lg cursor-pointer transition-colors ${
                      formData.organizationType === type.value
                        ? 'border-primary bg-primary/5'
                        : 'border-border hover:border-muted-foreground'
                    }`}
                  >
                    <input
                      type='radio'
                      name='organizationType'
                      value={type.value}
                      checked={formData.organizationType === type.value}
                      onChange={e => handleInputChange('organizationType', e.target.value)}
                      className='mt-1 mr-3'
                    />
                    <div>
                      <div className='font-medium text-foreground'>{type.label}</div>
                      <div className='text-sm text-muted-foreground'>{type.description}</div>
                    </div>
                  </label>
                ))}
              </div>
              {errors.organizationType && (
                <p className='text-red-500 text-sm mt-2'>{errors.organizationType}</p>
              )}
            </div>

            {/* 组织规模选择 */}
            <div className='octi-card p-6'>
              <h2 className='text-xl font-semibold text-foreground mb-4'>
                组织规模 <span className='text-red-500'>*</span>
              </h2>
              <div className='grid md:grid-cols-2 gap-3'>
                {ORGANIZATION_SCALES.map(scale => (
                  <label
                    key={scale.value}
                    className={`flex items-start p-3 border rounded-lg cursor-pointer transition-colors ${
                      formData.organizationScale === scale.value
                        ? 'border-primary bg-primary/5'
                        : 'border-border hover:border-muted-foreground'
                    }`}
                  >
                    <input
                      type='radio'
                      name='organizationScale'
                      value={scale.value}
                      checked={formData.organizationScale === scale.value}
                      onChange={e => handleInputChange('organizationScale', e.target.value)}
                      className='mt-1 mr-3'
                    />
                    <div>
                      <div className='font-medium text-foreground'>{scale.label}</div>
                      <div className='text-sm text-muted-foreground'>{scale.description}</div>
                    </div>
                  </label>
                ))}
              </div>
              {errors.organizationScale && (
                <p className='text-red-500 text-sm mt-2'>{errors.organizationScale}</p>
              )}
            </div>

            {/* 联系信息 */}
            <div className='octi-card p-6'>
              <h2 className='text-xl font-semibold text-foreground mb-4'>联系信息</h2>
              <div className='grid md:grid-cols-2 gap-4'>
                <div>
                  <label className='block text-sm font-medium text-foreground mb-2'>联系人姓名</label>
                  <input
                    type='text'
                    value={formData.contactPerson}
                    onChange={e => handleInputChange('contactPerson', e.target.value)}
                    className='octi-input'
                    placeholder='请输入联系人姓名'
                  />
                </div>

                <div>
                  <label className='block text-sm font-medium text-foreground mb-2'>
                    联系邮箱 <span className='text-red-500'>*</span>
                  </label>
                  <input
                    type='email'
                    value={formData.contactEmail}
                    onChange={e => handleInputChange('contactEmail', e.target.value)}
                    className={`octi-input ${
                      errors.contactEmail ? 'border-red-500' : ''
                    }`}
                    placeholder='用于接收评估报告'
                  />
                  {errors.contactEmail && (
                    <p className='text-red-500 text-sm mt-1'>{errors.contactEmail}</p>
                  )}
                </div>
              </div>
            </div>

            {/* 提交按钮 */}
            <div className='text-center pt-6'>
              <button
                type='submit'
                className='inline-flex items-center justify-center gap-3 bg-primary text-white px-8 py-3 rounded-xl text-lg font-semibold hover:bg-primary/90 transition-colors shadow-lg hover:shadow-xl'
              >
                开始能力评估
                <ArrowRight className='h-5 w-5' />
              </button>
            </div>
          </form>
        </div>
      </UserPageContainer>
    </UserLayout>
  );
}
