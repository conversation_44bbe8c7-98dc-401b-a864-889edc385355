/**
 * OCTI智能评估系统 - 创建评估页面
 *
 * 引导用户创建新的组织能力评估
 */

'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { MainLayout, PageContainer, CardContainer } from '@/components/layout/main-layout';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Progress } from '@/components/ui/progress';

// ============================================================================
// 模拟数据
// ============================================================================

const mockUser = {
  id: '1',
  name: '张三',
  email: '<EMAIL>',
  role: 'USER',
};

const mockOrganizations = [
  { id: '1', name: '北京环保基金会', type: 'FOUNDATION' },
  { id: '2', name: '上海教育发展中心', type: 'NGO' },
  { id: '3', name: '深圳科技创新协会', type: 'SOCIAL_ENTERPRISE' },
];

// ============================================================================
// 创建评估页面组件
// ============================================================================

export default function CreateAssessmentPage() {
  const router = useRouter();
  const [currentStep, setCurrentStep] = useState(1);
  const [isLoading, setIsLoading] = useState(false);
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    type: '',
    organizationId: '',
    organizationName: '',
    organizationType: '',
    isNewOrganization: false,
  });
  const [error, setError] = useState('');

  const totalSteps = 3;
  const progress = (currentStep / totalSteps) * 100;

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }));
    if (error) setError('');
  };

  const handleSelectChange = (name: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }));
    if (error) setError('');
  };

  const handleOrganizationChange = (organizationId: string) => {
    if (organizationId === 'new') {
      setFormData(prev => ({
        ...prev,
        organizationId: '',
        isNewOrganization: true,
      }));
    } else {
      const org = mockOrganizations.find(o => o.id === organizationId);
      setFormData(prev => ({
        ...prev,
        organizationId,
        organizationName: org?.name || '',
        organizationType: org?.type || '',
        isNewOrganization: false,
      }));
    }
  };

  const validateStep = (step: number) => {
    switch (step) {
      case 1:
        if (!formData.type) {
          setError('请选择评估类型');
          return false;
        }
        break;
      case 2:
        if (formData.isNewOrganization) {
          if (!formData.organizationName.trim()) {
            setError('请输入组织名称');
            return false;
          }
          if (!formData.organizationType) {
            setError('请选择组织类型');
            return false;
          }
        } else {
          if (!formData.organizationId) {
            setError('请选择组织');
            return false;
          }
        }
        break;
      case 3:
        if (!formData.title.trim()) {
          setError('请输入评估标题');
          return false;
        }
        break;
    }
    return true;
  };

  const handleNext = () => {
    if (validateStep(currentStep)) {
      setCurrentStep(prev => Math.min(prev + 1, totalSteps));
    }
  };

  const handlePrevious = () => {
    setCurrentStep(prev => Math.max(prev - 1, 1));
  };

  const handleSubmit = async () => {
    if (!validateStep(currentStep)) {
      return;
    }

    setIsLoading(true);
    try {
      // TODO: 实现实际的创建逻辑
      await new Promise(resolve => setTimeout(resolve, 2000));

      // 模拟创建成功，跳转到评估详情页
      router.push('/assessments/new-assessment-id');
    } catch (err) {
      setError('创建评估失败，请重试');
    } finally {
      setIsLoading(false);
    }
  };

  const renderStep1 = () => (
    <CardContainer title='选择评估类型' description='选择适合您组织的评估类型'>
      <div className='space-y-4'>
        <div className='grid gap-4'>
          <div
            className={`p-4 border-2 rounded-lg cursor-pointer transition-colors ${
              formData.type === 'STANDARD'
                ? 'border-primary bg-primary/5'
                : 'border-muted hover:border-primary/50'
            }`}
            onClick={() => handleSelectChange('type', 'STANDARD')}
          >
            <div className='flex items-start justify-between'>
              <div className='space-y-2'>
                <div className='flex items-center space-x-2'>
                  <h3 className='text-lg font-semibold'>标准评估</h3>
                  <Badge variant='secondary'>推荐</Badge>
                </div>
                <p className='text-sm text-muted-foreground'>
                  适合大多数公益机构的基础能力评估，包含45道核心问题，预计用时30-45分钟。
                </p>
                <div className='flex items-center space-x-4 text-xs text-muted-foreground'>
                  <span>• 45道问题</span>
                  <span>• 30-45分钟</span>
                  <span>• 基础分析报告</span>
                </div>
              </div>
              <div className='text-2xl'>📊</div>
            </div>
          </div>

          <div
            className={`p-4 border-2 rounded-lg cursor-pointer transition-colors ${
              formData.type === 'PROFESSIONAL'
                ? 'border-primary bg-primary/5'
                : 'border-muted hover:border-primary/50'
            }`}
            onClick={() => handleSelectChange('type', 'PROFESSIONAL')}
          >
            <div className='flex items-start justify-between'>
              <div className='space-y-2'>
                <div className='flex items-center space-x-2'>
                  <h3 className='text-lg font-semibold'>专业评估</h3>
                  <Badge>高级</Badge>
                </div>
                <p className='text-sm text-muted-foreground'>
                  深度全面的组织能力评估，包含60道问题，双模型AI分析，预计用时60-90分钟。
                </p>
                <div className='flex items-center space-x-4 text-xs text-muted-foreground'>
                  <span>• 60道问题</span>
                  <span>• 60-90分钟</span>
                  <span>• 双模型AI分析</span>
                  <span>• 详细发展建议</span>
                </div>
              </div>
              <div className='text-2xl'>🎯</div>
            </div>
          </div>
        </div>
      </div>
    </CardContainer>
  );

  const renderStep2 = () => (
    <CardContainer title='选择组织' description='选择要评估的组织或添加新组织'>
      <div className='space-y-4'>
        <div className='space-y-2'>
          <Label>选择组织</Label>
          <Select
            value={formData.isNewOrganization ? 'new' : formData.organizationId}
            onValueChange={handleOrganizationChange}
          >
            <SelectTrigger>
              <SelectValue placeholder='请选择组织' />
            </SelectTrigger>
            <SelectContent>
              {mockOrganizations.map(org => (
                <SelectItem key={org.id} value={org.id}>
                  {org.name}
                </SelectItem>
              ))}
              <SelectItem value='new'>+ 添加新组织</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {formData.isNewOrganization && (
          <div className='space-y-4 p-4 border rounded-lg bg-muted/50'>
            <h4 className='font-medium'>新组织信息</h4>
            <div className='grid grid-cols-2 gap-4'>
              <div className='space-y-2'>
                <Label htmlFor='organizationName'>组织名称</Label>
                <Input
                  id='organizationName'
                  name='organizationName'
                  value={formData.organizationName}
                  onChange={handleInputChange}
                  placeholder='请输入组织名称'
                />
              </div>
              <div className='space-y-2'>
                <Label>组织类型</Label>
                <Select
                  value={formData.organizationType}
                  onValueChange={value => handleSelectChange('organizationType', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder='选择组织类型' />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value='NGO'>公益组织/NGO</SelectItem>
                    <SelectItem value='FOUNDATION'>基金会</SelectItem>
                    <SelectItem value='SOCIAL_ENTERPRISE'>社会企业</SelectItem>
                    <SelectItem value='CHARITY'>慈善机构</SelectItem>
                    <SelectItem value='COMMUNITY_ORG'>社区组织</SelectItem>
                    <SelectItem value='OTHER'>其他</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>
        )}
      </div>
    </CardContainer>
  );

  const renderStep3 = () => (
    <CardContainer title='评估信息' description='设置评估的基本信息'>
      <div className='space-y-4'>
        <div className='space-y-2'>
          <Label htmlFor='title'>评估标题</Label>
          <Input
            id='title'
            name='title'
            value={formData.title}
            onChange={handleInputChange}
            placeholder='例如：2024年度组织能力评估'
          />
        </div>

        <div className='space-y-2'>
          <Label htmlFor='description'>评估描述（可选）</Label>
          <Textarea
            id='description'
            name='description'
            value={formData.description}
            onChange={handleInputChange}
            placeholder='简要描述本次评估的目的和背景...'
            rows={3}
          />
        </div>

        {/* 评估预览 */}
        <div className='p-4 border rounded-lg bg-muted/50'>
          <h4 className='font-medium mb-3'>评估预览</h4>
          <div className='space-y-2 text-sm'>
            <div className='flex justify-between'>
              <span className='text-muted-foreground'>评估类型：</span>
              <span>{formData.type === 'PROFESSIONAL' ? '专业评估' : '标准评估'}</span>
            </div>
            <div className='flex justify-between'>
              <span className='text-muted-foreground'>目标组织：</span>
              <span>{formData.organizationName || '未选择'}</span>
            </div>
            <div className='flex justify-between'>
              <span className='text-muted-foreground'>预计时长：</span>
              <span>{formData.type === 'PROFESSIONAL' ? '60-90分钟' : '30-45分钟'}</span>
            </div>
            <div className='flex justify-between'>
              <span className='text-muted-foreground'>问题数量：</span>
              <span>{formData.type === 'PROFESSIONAL' ? '60道' : '45道'}</span>
            </div>
          </div>
        </div>
      </div>
    </CardContainer>
  );

  return (
    <MainLayout user={mockUser}>
      <PageContainer
        title='创建新评估'
        description='为您的组织创建一个新的能力评估项目'
        breadcrumb={[
          { title: '首页', href: '/' },
          { title: '评估管理', href: '/assessments' },
          { title: '创建评估' },
        ]}
      >
        {/* 进度指示器 */}
        <div className='mb-8'>
          <div className='flex items-center justify-between mb-2'>
            <span className='text-sm font-medium'>
              步骤 {currentStep} / {totalSteps}
            </span>
            <span className='text-sm text-muted-foreground'>{Math.round(progress)}% 完成</span>
          </div>
          <Progress value={progress} className='h-2' />

          <div className='flex justify-between mt-4'>
            <div
              className={`text-xs ${currentStep >= 1 ? 'text-primary' : 'text-muted-foreground'}`}
            >
              选择类型
            </div>
            <div
              className={`text-xs ${currentStep >= 2 ? 'text-primary' : 'text-muted-foreground'}`}
            >
              选择组织
            </div>
            <div
              className={`text-xs ${currentStep >= 3 ? 'text-primary' : 'text-muted-foreground'}`}
            >
              评估信息
            </div>
          </div>
        </div>

        {/* 错误提示 */}
        {error && (
          <Alert className='mb-6'>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {/* 步骤内容 */}
        <div className='mb-8'>
          {currentStep === 1 && renderStep1()}
          {currentStep === 2 && renderStep2()}
          {currentStep === 3 && renderStep3()}
        </div>

        {/* 导航按钮 */}
        <div className='flex justify-between'>
          <Button variant='outline' onClick={handlePrevious} disabled={currentStep === 1}>
            上一步
          </Button>

          <div className='flex space-x-2'>
            <Button variant='outline' onClick={() => router.push('/assessments')}>
              取消
            </Button>

            {currentStep === totalSteps ? (
              <Button onClick={handleSubmit} disabled={isLoading}>
                {isLoading ? '创建中...' : '创建评估'}
              </Button>
            ) : (
              <Button onClick={handleNext}>下一步</Button>
            )}
          </div>
        </div>
      </PageContainer>
    </MainLayout>
  );
}
