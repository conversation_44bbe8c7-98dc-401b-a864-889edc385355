/**
 * OCTI智能评估系统 - 评估列表页面
 *
 * 展示用户的所有评估项目
 */

'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import {
  MainLayout,
  PageContainer,
  CardContainer,
  EmptyState,
} from '@/components/layout/main-layout';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';

// ============================================================================
// 模拟数据
// ============================================================================

const mockUser = {
  id: '1',
  name: '张三',
  email: '<EMAIL>',
  role: 'USER',
};

const mockAssessments = [
  {
    id: '1',
    title: '北京环保基金会 - 组织能力评估',
    description: '基于OCTI框架的全面组织能力评估',
    organization: {
      id: '1',
      name: '北京环保基金会',
      type: 'FOUNDATION',
    },
    type: 'PROFESSIONAL',
    status: 'COMPLETED',
    progress: 100,
    score: 78.5,
    createdAt: '2024-01-15T10:00:00Z',
    completedAt: '2024-01-20T16:30:00Z',
    questionnaire: {
      totalQuestions: 60,
      answeredQuestions: 60,
    },
  },
  {
    id: '2',
    title: '上海教育发展中心 - 能力评估',
    description: '专注于教育领域的组织能力评估',
    organization: {
      id: '2',
      name: '上海教育发展中心',
      type: 'NGO',
    },
    type: 'STANDARD',
    status: 'IN_PROGRESS',
    progress: 65,
    score: null,
    createdAt: '2024-01-12T09:00:00Z',
    completedAt: null,
    questionnaire: {
      totalQuestions: 45,
      answeredQuestions: 29,
    },
  },
  {
    id: '3',
    title: '深圳科技创新协会 - 初步评估',
    description: '科技创新类组织的能力评估',
    organization: {
      id: '3',
      name: '深圳科技创新协会',
      type: 'SOCIAL_ENTERPRISE',
    },
    type: 'STANDARD',
    status: 'DRAFT',
    progress: 15,
    score: null,
    createdAt: '2024-01-10T14:00:00Z',
    completedAt: null,
    questionnaire: {
      totalQuestions: 45,
      answeredQuestions: 7,
    },
  },
  {
    id: '4',
    title: '广州慈善总会 - 年度评估',
    description: '年度组织发展评估',
    organization: {
      id: '4',
      name: '广州慈善总会',
      type: 'CHARITY',
    },
    type: 'PROFESSIONAL',
    status: 'ANALYSIS',
    progress: 95,
    score: null,
    createdAt: '2024-01-08T11:00:00Z',
    completedAt: null,
    questionnaire: {
      totalQuestions: 60,
      answeredQuestions: 60,
    },
  },
];

// ============================================================================
// 评估列表页面组件
// ============================================================================

export default function AssessmentsPage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [typeFilter, setTypeFilter] = useState('all');

  // 过滤评估
  const filteredAssessments = mockAssessments.filter(assessment => {
    const matchesSearch =
      assessment.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      assessment.organization.name.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || assessment.status === statusFilter;
    const matchesType = typeFilter === 'all' || assessment.type === typeFilter;

    return matchesSearch && matchesStatus && matchesType;
  });

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      DRAFT: { label: '草稿', variant: 'secondary' as const },
      PROFILE_COLLECTION: { label: '画像收集', variant: 'default' as const },
      QUESTIONNAIRE_GENERATION: { label: '问卷生成', variant: 'default' as const },
      IN_PROGRESS: { label: '进行中', variant: 'default' as const },
      ANALYSIS: { label: '分析中', variant: 'default' as const },
      COMPLETED: { label: '已完成', variant: 'default' as const },
      ARCHIVED: { label: '已归档', variant: 'outline' as const },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || {
      label: status,
      variant: 'secondary' as const,
    };
    return <Badge variant={config.variant}>{config.label}</Badge>;
  };

  const getTypeBadge = (type: string) => {
    return <Badge variant='outline'>{type === 'PROFESSIONAL' ? '专业版' : '标准版'}</Badge>;
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN');
  };

  return (
    <MainLayout user={mockUser}>
      <PageContainer
        title='评估管理'
        description='管理您的所有组织能力评估项目'
        breadcrumb={[{ title: '首页', href: '/' }, { title: '评估管理' }]}
        actions={
          <Link href='/assessments/create'>
            <Button>创建新评估</Button>
          </Link>
        }
      >
        {/* 筛选和搜索 */}
        <CardContainer>
          <div className='flex flex-col sm:flex-row gap-4'>
            <div className='flex-1'>
              <Input
                placeholder='搜索评估项目或组织名称...'
                value={searchTerm}
                onChange={e => setSearchTerm(e.target.value)}
              />
            </div>
            <div className='flex gap-2'>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className='w-32'>
                  <SelectValue placeholder='状态' />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value='all'>全部状态</SelectItem>
                  <SelectItem value='DRAFT'>草稿</SelectItem>
                  <SelectItem value='IN_PROGRESS'>进行中</SelectItem>
                  <SelectItem value='ANALYSIS'>分析中</SelectItem>
                  <SelectItem value='COMPLETED'>已完成</SelectItem>
                </SelectContent>
              </Select>

              <Select value={typeFilter} onValueChange={setTypeFilter}>
                <SelectTrigger className='w-32'>
                  <SelectValue placeholder='类型' />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value='all'>全部类型</SelectItem>
                  <SelectItem value='STANDARD'>标准版</SelectItem>
                  <SelectItem value='PROFESSIONAL'>专业版</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContainer>

        {/* 评估列表 */}
        {filteredAssessments.length === 0 ? (
          <EmptyState
            title='暂无评估项目'
            description='您还没有创建任何评估项目，点击下方按钮开始您的第一个评估。'
            action={
              <Link href='/assessments/create'>
                <Button>创建新评估</Button>
              </Link>
            }
          />
        ) : (
          <div className='grid gap-6 md:grid-cols-2 lg:grid-cols-3'>
            {filteredAssessments.map(assessment => (
              <Card key={assessment.id} className='hover:shadow-md transition-shadow'>
                <CardHeader>
                  <div className='flex items-start justify-between'>
                    <div className='space-y-1'>
                      <CardTitle className='text-lg'>{assessment.title}</CardTitle>
                      <CardDescription>{assessment.organization.name}</CardDescription>
                    </div>
                    <div className='flex flex-col items-end space-y-1'>
                      {getStatusBadge(assessment.status)}
                      {getTypeBadge(assessment.type)}
                    </div>
                  </div>
                </CardHeader>

                <CardContent className='space-y-4'>
                  <p className='text-sm text-muted-foreground line-clamp-2'>
                    {assessment.description}
                  </p>

                  {/* 进度信息 */}
                  <div className='space-y-2'>
                    <div className='flex justify-between text-sm'>
                      <span>完成进度</span>
                      <span>{assessment.progress}%</span>
                    </div>
                    <Progress value={assessment.progress} className='h-2' />
                    <div className='flex justify-between text-xs text-muted-foreground'>
                      <span>
                        {assessment.questionnaire.answeredQuestions}/
                        {assessment.questionnaire.totalQuestions} 题已完成
                      </span>
                      {assessment.score && <span>评分: {assessment.score}分</span>}
                    </div>
                  </div>

                  {/* 时间信息 */}
                  <div className='text-xs text-muted-foreground'>
                    <div>创建时间: {formatDate(assessment.createdAt)}</div>
                    {assessment.completedAt && (
                      <div>完成时间: {formatDate(assessment.completedAt)}</div>
                    )}
                  </div>

                  {/* 操作按钮 */}
                  <div className='flex space-x-2 pt-2'>
                    <Link href={`/assessments/${assessment.id}`} className='flex-1'>
                      <Button variant='outline' className='w-full'>
                        查看详情
                      </Button>
                    </Link>
                    {assessment.status === 'IN_PROGRESS' && (
                      <Link href={`/assessments/${assessment.id}/questionnaire`} className='flex-1'>
                        <Button className='w-full'>继续填写</Button>
                      </Link>
                    )}
                    {assessment.status === 'COMPLETED' && (
                      <Link href={`/assessments/${assessment.id}/report`} className='flex-1'>
                        <Button className='w-full'>查看报告</Button>
                      </Link>
                    )}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}

        {/* 统计信息 */}
        <CardContainer title='统计概览'>
          <div className='grid grid-cols-2 md:grid-cols-4 gap-4'>
            <div className='text-center'>
              <div className='text-2xl font-bold text-primary'>{mockAssessments.length}</div>
              <div className='text-sm text-muted-foreground'>总评估数</div>
            </div>
            <div className='text-center'>
              <div className='text-2xl font-bold text-green-600'>
                {mockAssessments.filter(a => a.status === 'COMPLETED').length}
              </div>
              <div className='text-sm text-muted-foreground'>已完成</div>
            </div>
            <div className='text-center'>
              <div className='text-2xl font-bold text-blue-600'>
                {mockAssessments.filter(a => a.status === 'IN_PROGRESS').length}
              </div>
              <div className='text-sm text-muted-foreground'>进行中</div>
            </div>
            <div className='text-center'>
              <div className='text-2xl font-bold text-orange-600'>
                {mockAssessments.filter(a => a.status === 'DRAFT').length}
              </div>
              <div className='text-sm text-muted-foreground'>草稿</div>
            </div>
          </div>
        </CardContainer>
      </PageContainer>
    </MainLayout>
  );
}
