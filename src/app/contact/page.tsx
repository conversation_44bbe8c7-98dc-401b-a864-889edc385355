'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { ArrowLeft, Mail, Phone, MapPin, Clock, Send } from 'lucide-react';
import Link from 'next/link';

/**
 * 联系我们页面
 * 提供多种联系方式和在线表单
 */
export default function ContactPage() {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    subject: '',
    message: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  const contactInfo = [
    {
      icon: <Mail className="h-5 w-5" />,
      title: '邮箱联系',
      content: '<EMAIL>',
      description: '我们会在24小时内回复您的邮件'
    },
    {
      icon: <Phone className="h-5 w-5" />,
      title: '电话咨询',
      content: '18612268419',
      description: '工作日 9:00-18:00 提供电话支持'
    },
    {
      icon: <MapPin className="h-5 w-5" />,
      title: '公司地址',
      content: '北京市东城区东环大厦7楼',
      description: '欢迎预约到访，我们提供面对面咨询'
    },
    {
      icon: <Clock className="h-5 w-5" />,
      title: '工作时间',
      content: '周一至周五 9:00-18:00',
      description: '节假日我们也会及时处理紧急问题'
    }
  ];

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    
    // 模拟表单提交
    try {
      await new Promise(resolve => setTimeout(resolve, 2000));
      alert('感谢您的留言！我们会尽快与您联系。');
      setFormData({ name: '', email: '', subject: '', message: '' });
    } catch (error) {
      alert('提交失败，请稍后重试。');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      <div className="container mx-auto px-4 py-8">
        {/* 页面头部 */}
        <div className="mb-8">
          <Link href="/">
            <Button variant="ghost" className="mb-4">
              <ArrowLeft className="h-4 w-4 mr-2" />
              返回首页
            </Button>
          </Link>
          <h1 className="text-4xl font-bold text-gray-900 mb-4">联系我们</h1>
          <p className="text-xl text-gray-600">
            我们随时为您提供专业的支持和服务
          </p>
        </div>

        <div className="grid lg:grid-cols-2 gap-8">
          {/* 联系信息 */}
          <div>
            <h2 className="text-2xl font-semibold text-gray-900 mb-6">联系方式</h2>
            <div className="grid gap-6 mb-8">
              {contactInfo.map((info, index) => (
                <Card key={index} className="hover:shadow-md transition-shadow">
                  <CardContent className="p-6">
                    <div className="flex items-start gap-4">
                      <div className="p-2 bg-blue-100 rounded-lg text-blue-600">
                        {info.icon}
                      </div>
                      <div>
                        <h3 className="font-semibold text-gray-900 mb-1">
                          {info.title}
                        </h3>
                        <p className="text-lg text-gray-800 mb-1">
                          {info.content}
                        </p>
                        <p className="text-sm text-gray-600">
                          {info.description}
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>

            {/* 常见问题快速链接 */}
            <Card>
              <CardHeader>
                <CardTitle>快速帮助</CardTitle>
                <CardDescription>
                  在联系我们之前，您可能想查看这些资源
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <Link href="/help" className="block">
                    <Button variant="outline" className="w-full justify-start">
                      帮助中心
                    </Button>
                  </Link>
                  <Button variant="outline" className="w-full justify-start">
                    常见问题
                  </Button>
                  <Button variant="outline" className="w-full justify-start">
                    使用指南
                  </Button>
                  <Button variant="outline" className="w-full justify-start">
                    视频教程
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* 联系表单 */}
          <div>
            <Card>
              <CardHeader>
                <CardTitle>在线留言</CardTitle>
                <CardDescription>
                  请填写以下表单，我们会尽快与您联系
                </CardDescription>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleSubmit} className="space-y-6">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="name">姓名 *</Label>
                      <Input
                        id="name"
                        name="name"
                        value={formData.name}
                        onChange={handleInputChange}
                        required
                        placeholder="请输入您的姓名"
                      />
                    </div>
                    <div>
                      <Label htmlFor="email">邮箱 *</Label>
                      <Input
                        id="email"
                        name="email"
                        type="email"
                        value={formData.email}
                        onChange={handleInputChange}
                        required
                        placeholder="请输入您的邮箱"
                      />
                    </div>
                  </div>

                  <div>
                    <Label htmlFor="subject">主题 *</Label>
                    <Input
                      id="subject"
                      name="subject"
                      value={formData.subject}
                      onChange={handleInputChange}
                      required
                      placeholder="请简要描述您的问题"
                    />
                  </div>

                  <div>
                    <Label htmlFor="message">详细描述 *</Label>
                    <Textarea
                      id="message"
                      name="message"
                      value={formData.message}
                      onChange={handleInputChange}
                      required
                      rows={6}
                      placeholder="请详细描述您的问题或需求，我们会根据您的描述提供更准确的帮助"
                    />
                  </div>

                  <Button 
                    type="submit" 
                    className="w-full" 
                    disabled={isSubmitting}
                  >
                    {isSubmitting ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                        提交中...
                      </>
                    ) : (
                      <>
                        <Send className="h-4 w-4 mr-2" />
                        发送消息
                      </>
                    )}
                  </Button>
                </form>
              </CardContent>
            </Card>

            {/* 响应时间说明 */}
            <Card className="mt-6">
              <CardContent className="p-4">
                <div className="flex items-center gap-2 text-sm text-gray-600">
                  <Clock className="h-4 w-4" />
                  <span>我们通常在2小时内回复您的消息，复杂问题可能需要24小时</span>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}
