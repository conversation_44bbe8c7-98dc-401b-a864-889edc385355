/**
 * OCTI智能评估系统 - 仪表板页面
 *
 * 展示系统概览、统计数据、快捷操作等
 */

'use client';

import React from 'react';
import { MainLayout, PageContainer, CardContainer } from '@/components/layout/main-layout';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

// ============================================================================
// 模拟数据
// ============================================================================

const mockUser = {
  id: '1',
  name: '张三',
  email: 'zhang<PERSON>@example.com',
  role: 'USER',
  avatar: '',
};

const dashboardStats = [
  {
    title: '总评估数',
    value: '24',
    change: '+12%',
    changeType: 'positive' as const,
    icon: '📊',
  },
  {
    title: '活跃组织',
    value: '8',
    change: '+3',
    changeType: 'positive' as const,
    icon: '🏢',
  },
  {
    title: '完成率',
    value: '85%',
    change: '+5%',
    changeType: 'positive' as const,
    icon: '✅',
  },
  {
    title: '平均分数',
    value: '72.5',
    change: '-2.1',
    changeType: 'negative' as const,
    icon: '📈',
  },
];

const recentAssessments = [
  {
    id: '1',
    title: '北京环保基金会 - 能力评估',
    organization: '北京环保基金会',
    status: 'completed',
    score: 78,
    date: '2024-01-15',
  },
  {
    id: '2',
    title: '上海教育发展中心 - 能力评估',
    organization: '上海教育发展中心',
    status: 'in_progress',
    score: null,
    date: '2024-01-12',
  },
  {
    id: '3',
    title: '深圳科技创新协会 - 能力评估',
    organization: '深圳科技创新协会',
    status: 'draft',
    score: null,
    date: '2024-01-10',
  },
];

const quickActions = [
  {
    title: '创建新评估',
    description: '为组织开始新的能力评估',
    href: '/assessments/create',
    icon: '🚀',
    color: 'bg-blue-500',
  },
  {
    title: '添加组织',
    description: '添加新的公益组织',
    href: '/organizations/create',
    icon: '➕',
    color: 'bg-green-500',
  },
  {
    title: '查看报告',
    description: '浏览评估分析报告',
    href: '/reports',
    icon: '📊',
    color: 'bg-purple-500',
  },
  {
    title: '配置智能体',
    description: '管理AI智能体设置',
    href: '/agents/config',
    icon: '🤖',
    color: 'bg-orange-500',
  },
];

// ============================================================================
// 仪表板页面组件
// ============================================================================

export default function DashboardPage() {
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'completed':
        return <Badge className='bg-green-100 text-green-800'>已完成</Badge>;
      case 'in_progress':
        return <Badge className='bg-blue-100 text-blue-800'>进行中</Badge>;
      case 'draft':
        return <Badge className='bg-gray-100 text-gray-800'>草稿</Badge>;
      default:
        return <Badge variant='secondary'>{status}</Badge>;
    }
  };

  return (
    <MainLayout user={mockUser}>
      <PageContainer
        title='仪表板'
        description='欢迎回到OCTI智能评估系统，查看您的评估概览和系统状态'
        breadcrumb={[{ title: '首页', href: '/' }, { title: '仪表板' }]}
      >
        {/* 统计卡片 */}
        <div className='grid gap-4 md:grid-cols-2 lg:grid-cols-4'>
          {dashboardStats.map((stat, index) => (
            <Card key={index}>
              <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
                <CardTitle className='text-sm font-medium'>{stat.title}</CardTitle>
                <span className='text-2xl'>{stat.icon}</span>
              </CardHeader>
              <CardContent>
                <div className='text-2xl font-bold'>{stat.value}</div>
                <p
                  className={`text-xs ${
                    stat.changeType === 'positive' ? 'text-green-600' : 'text-red-600'
                  }`}
                >
                  {stat.change} 较上月
                </p>
              </CardContent>
            </Card>
          ))}
        </div>

        <div className='grid gap-6 md:grid-cols-2 lg:grid-cols-3'>
          {/* 快捷操作 */}
          <CardContainer title='快捷操作' description='常用功能快速入口' className='lg:col-span-2'>
            <div className='grid gap-4 sm:grid-cols-2'>
              {quickActions.map((action, index) => (
                <div
                  key={index}
                  className='flex items-center space-x-4 rounded-lg border p-4 hover:bg-accent transition-colors cursor-pointer'
                >
                  <div
                    className={`flex h-10 w-10 items-center justify-center rounded-lg text-white ${action.color}`}
                  >
                    <span className='text-lg'>{action.icon}</span>
                  </div>
                  <div className='flex-1'>
                    <h4 className='text-sm font-medium'>{action.title}</h4>
                    <p className='text-xs text-muted-foreground'>{action.description}</p>
                  </div>
                </div>
              ))}
            </div>
          </CardContainer>

          {/* 系统状态 */}
          <CardContainer title='系统状态' description='当前系统运行状态'>
            <div className='space-y-4'>
              <div className='flex items-center justify-between'>
                <span className='text-sm'>API服务</span>
                <Badge className='bg-green-100 text-green-800'>正常</Badge>
              </div>
              <div className='flex items-center justify-between'>
                <span className='text-sm'>数据库</span>
                <Badge className='bg-green-100 text-green-800'>正常</Badge>
              </div>
              <div className='flex items-center justify-between'>
                <span className='text-sm'>AI模型</span>
                <Badge className='bg-green-100 text-green-800'>正常</Badge>
              </div>
              <div>
                <div className='flex items-center justify-between text-sm mb-2'>
                  <span>存储使用</span>
                  <span className='text-muted-foreground'>68%</span>
                </div>
                <Progress value={68} className='h-2' />
              </div>
              <div>
                <div className='flex items-center justify-between text-sm mb-2'>
                  <span>本月配额</span>
                  <span className='text-muted-foreground'>24/100</span>
                </div>
                <Progress value={24} className='h-2' />
              </div>
            </div>
          </CardContainer>
        </div>

        {/* 最近评估 */}
        <CardContainer
          title='最近评估'
          description='最新的评估项目和进度'
          actions={
            <Button variant='outline' size='sm'>
              查看全部
            </Button>
          }
        >
          <div className='space-y-4'>
            {recentAssessments.map(assessment => (
              <div
                key={assessment.id}
                className='flex items-center justify-between rounded-lg border p-4'
              >
                <div className='flex-1'>
                  <h4 className='text-sm font-medium'>{assessment.title}</h4>
                  <p className='text-xs text-muted-foreground'>{assessment.organization}</p>
                  <p className='text-xs text-muted-foreground'>{assessment.date}</p>
                </div>
                <div className='flex items-center space-x-4'>
                  {assessment.score && (
                    <div className='text-right'>
                      <div className='text-sm font-medium'>{assessment.score}分</div>
                      <div className='text-xs text-muted-foreground'>评估分数</div>
                    </div>
                  )}
                  {getStatusBadge(assessment.status)}
                </div>
              </div>
            ))}
          </div>
        </CardContainer>
      </PageContainer>
    </MainLayout>
  );
}
