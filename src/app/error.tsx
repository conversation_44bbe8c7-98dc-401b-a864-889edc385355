/**
 * OCTI智能评估系统 - 全局错误页面
 *
 * 处理应用级别的错误，提供友好的错误界面和恢复选项
 */

'use client';

import { useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';

interface ErrorProps {
  error: Error & { digest?: string };
  reset: () => void;
}

export default function Error({ error, reset }: ErrorProps) {
  useEffect(() => {
    // 记录错误到控制台
    console.error('Application error:', error);

    // 这里可以添加错误上报逻辑
    // 例如发送到错误监控服务
  }, [error]);

  // 检查是否是已知的外部错误
  const isExternalError =
    error.message?.includes('MetaMask') ||
    error.message?.includes('chrome-extension') ||
    error.stack?.includes('chrome-extension');

  // 对于外部错误，自动重试
  useEffect(() => {
    if (isExternalError) {
      const timer = setTimeout(() => {
        reset();
      }, 1000);
      return () => clearTimeout(timer);
    }
    // 显式返回undefined
    return undefined;
  }, [isExternalError, reset]);

  if (isExternalError) {
    return (
      <div className='min-h-screen flex items-center justify-center p-4'>
        <div className='max-w-md w-full text-center space-y-4'>
          <div className='text-4xl'>🔄</div>
          <h2 className='text-lg font-semibold'>正在恢复...</h2>
          <p className='text-sm text-muted-foreground'>
            检测到外部扩展冲突，正在自动恢复应用状态。
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className='min-h-screen flex items-center justify-center p-4'>
      <div className='max-w-md w-full space-y-6'>
        <div className='text-center space-y-4'>
          <div className='text-6xl'>😵</div>
          <h1 className='text-2xl font-bold'>出现了一些问题</h1>
          <p className='text-muted-foreground'>
            很抱歉，应用遇到了意外错误。请尝试重新加载或联系技术支持。
          </p>
        </div>

        {/* 错误详情 */}
        <Alert>
          <AlertDescription>
            <details className='text-sm'>
              <summary className='cursor-pointer font-medium mb-2'>错误详情</summary>
              <div className='mt-2 space-y-2'>
                <div>
                  <strong>错误消息:</strong>
                  <pre className='whitespace-pre-wrap text-xs bg-muted p-2 rounded mt-1 overflow-auto'>
                    {error.message}
                  </pre>
                </div>
                {error.digest && (
                  <div>
                    <strong>错误ID:</strong>
                    <code className='text-xs bg-muted px-1 py-0.5 rounded ml-1'>
                      {error.digest}
                    </code>
                  </div>
                )}
                {process.env.NODE_ENV === 'development' && error.stack && (
                  <div>
                    <strong>堆栈跟踪:</strong>
                    <pre className='whitespace-pre-wrap text-xs bg-muted p-2 rounded mt-1 overflow-auto max-h-32'>
                      {error.stack}
                    </pre>
                  </div>
                )}
              </div>
            </details>
          </AlertDescription>
        </Alert>

        {/* 操作按钮 */}
        <div className='space-y-3'>
          <Button onClick={reset} className='w-full'>
            重试
          </Button>

          <div className='flex space-x-2'>
            <Button variant='outline' onClick={() => window.location.reload()} className='flex-1'>
              刷新页面
            </Button>
            <Button
              variant='outline'
              onClick={() => (window.location.href = '/')}
              className='flex-1'
            >
              返回首页
            </Button>
          </div>
        </div>

        {/* 帮助信息 */}
        <div className='text-center space-y-2'>
          <p className='text-sm text-muted-foreground'>如果问题持续存在，请尝试：</p>
          <ul className='text-xs text-muted-foreground space-y-1'>
            <li>• 清除浏览器缓存和Cookie</li>
            <li>• 禁用浏览器扩展</li>
            <li>• 使用无痕模式访问</li>
            <li>• 联系技术支持团队</li>
          </ul>
        </div>

        {/* 联系支持 */}
        <div className='text-center pt-4 border-t'>
          <Button variant='ghost' size='sm'>
            联系技术支持
          </Button>
        </div>
      </div>
    </div>
  );
}
