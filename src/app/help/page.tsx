'use client';

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ArrowLeft, HelpCircle, BookOpen, MessageCircle, Mail } from 'lucide-react';
import Link from 'next/link';

/**
 * 帮助中心页面
 * 提供系统使用指南和常见问题解答
 */
export default function HelpPage() {
  const helpSections = [
    {
      title: '快速开始',
      icon: <BookOpen className="h-5 w-5" />,
      items: [
        '如何创建第一个评估',
        '如何填写组织信息',
        '如何查看评估结果',
        '如何导出报告'
      ]
    },
    {
      title: '评估指南',
      icon: <HelpCircle className="h-5 w-5" />,
      items: [
        '评估类型说明',
        '问卷填写技巧',
        '结果解读方法',
        '改进建议理解'
      ]
    },
    {
      title: '常见问题',
      icon: <MessageCircle className="h-5 w-5" />,
      items: [
        '评估需要多长时间？',
        '如何修改已提交的答案？',
        '评估结果的准确性如何？',
        '如何分享评估结果？'
      ]
    }
  ];

  const faqs = [
    {
      question: '评估需要多长时间？',
      answer: '标准评估通常需要15-30分钟，专业版评估可能需要30-60分钟，具体时间取决于组织规模和复杂度。'
    },
    {
      question: '评估结果的准确性如何？',
      answer: 'OCTI评估基于国际先进的组织能力评估框架，结合AI智能分析，准确率达到85%以上。'
    },
    {
      question: '如何获得更详细的分析？',
      answer: '可以选择专业版评估，将获得AI深度分析、改进建议和行动计划等详细内容。'
    },
    {
      question: '评估数据是否安全？',
      answer: '我们采用企业级安全措施，所有数据都经过加密存储，严格遵守数据保护法规。'
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      <div className="container mx-auto px-4 py-8">
        {/* 页面头部 */}
        <div className="mb-8">
          <Link href="/">
            <Button variant="ghost" className="mb-4">
              <ArrowLeft className="h-4 w-4 mr-2" />
              返回首页
            </Button>
          </Link>
          <h1 className="text-4xl font-bold text-gray-900 mb-4">帮助中心</h1>
          <p className="text-xl text-gray-600">
            欢迎使用OCTI智能评估系统，这里有您需要的所有帮助信息
          </p>
        </div>

        {/* 帮助分类 */}
        <div className="grid md:grid-cols-3 gap-6 mb-12">
          {helpSections.map((section, index) => (
            <Card key={index} className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  {section.icon}
                  {section.title}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2">
                  {section.items.map((item, itemIndex) => (
                    <li key={itemIndex} className="text-gray-600 hover:text-blue-600 cursor-pointer">
                      • {item}
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* 常见问题 */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle>常见问题解答</CardTitle>
            <CardDescription>
              以下是用户最常询问的问题和解答
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              {faqs.map((faq, index) => (
                <div key={index} className="border-b border-gray-200 pb-4 last:border-b-0">
                  <h3 className="font-semibold text-gray-900 mb-2">
                    Q: {faq.question}
                  </h3>
                  <p className="text-gray-600">
                    A: {faq.answer}
                  </p>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* 联系支持 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Mail className="h-5 w-5" />
              需要更多帮助？
            </CardTitle>
            <CardDescription>
              如果您没有找到需要的信息，请联系我们的支持团队
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col sm:flex-row gap-4">
              <Link href="/contact">
                <Button className="w-full sm:w-auto">
                  联系客服
                </Button>
              </Link>
              <Button variant="outline" className="w-full sm:w-auto">
                在线文档
              </Button>
              <Button variant="outline" className="w-full sm:w-auto">
                视频教程
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
