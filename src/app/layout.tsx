import type { Metadata } from 'next';
import { Inter, JetBrains_Mono } from 'next/font/google';
import './globals.css';
import { cn } from '@/lib/utils';
import { ErrorBoundary } from '@/components/error-boundary';
import { ClientErrorHandler } from './client-error-handler';

const inter = Inter({
  subsets: ['latin'],
  variable: '--font-sans',
});

const jetbrainsMono = JetBrains_Mono({
  subsets: ['latin'],
  variable: '--font-mono',
});

export const metadata: Metadata = {
  title: {
    default: 'OCTI智能评估系统',
    template: '%s | OCTI智能评估系统',
  },
  description: '基于配置驱动和智能体模块化的组织能力评估平台，专为公益机构设计',
  keywords: ['OCTI', '组织能力评估', '公益机构', '智能评估', '人工智能', '组织发展', '能力建设'],
  authors: [{ name: 'OCTI Team' }],
  creator: 'OCTI Team',
  publisher: 'OCTI Team',
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL('https://octi.example.com'),
  alternates: {
    canonical: '/',
  },
  openGraph: {
    type: 'website',
    locale: 'zh_CN',
    url: 'https://octi.example.com',
    title: 'OCTI智能评估系统',
    description: '基于配置驱动和智能体模块化的组织能力评估平台',
    siteName: 'OCTI智能评估系统',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'OCTI智能评估系统',
    description: '基于配置驱动和智能体模块化的组织能力评估平台',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  verification: {
    google: 'google-site-verification-code',
  },
};

interface RootLayoutProps {
  children: React.ReactNode;
}

/**
 * OCTI智能评估系统根布局组件
 *
 * 提供全局样式、字体配置和元数据设置
 * 支持响应式设计和可访问性优化
 */
export default function RootLayout({ children }: RootLayoutProps) {
  return (
    <html lang='zh-CN' suppressHydrationWarning>
      <head>
        <link rel='icon' href='/favicon.ico' sizes='any' />
        <link rel='icon' href='/icon.svg' type='image/svg+xml' />
        <link rel='apple-touch-icon' href='/apple-touch-icon.png' />
        <link rel='manifest' href='/manifest.json' />
        <meta name='theme-color' content='#0ea5e9' />
        <meta name='color-scheme' content='light dark' />
      </head>
      <body
        className={cn(
          'min-h-screen bg-background font-sans antialiased',
          inter.variable,
          jetbrainsMono.variable
        )}
        suppressHydrationWarning
      >
        <ClientErrorHandler />
        <ErrorBoundary>
          <div className='relative flex min-h-screen flex-col'>
            <div className='flex-1'>{children}</div>
          </div>
        </ErrorBoundary>
      </body>
    </html>
  );
}
