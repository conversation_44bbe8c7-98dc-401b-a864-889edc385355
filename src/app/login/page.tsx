/**
 * OCTI智能评估系统 - 登录页面
 *
 * 用户登录界面
 */

'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { AuthLayout, AuthDivider, AuthError } from '@/components/layout/auth-layout';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';

// ============================================================================
// 登录页面组件
// ============================================================================

export default function LoginPage() {
  const router = useRouter();
  const [formData, setFormData] = useState({
    email: '',
    password: '',
  });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }));
    // 清除错误信息
    if (error) setError('');
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');

    try {
      // TODO: 实现实际的登录逻辑
      // 这里模拟登录过程
      await new Promise(resolve => setTimeout(resolve, 1000));

      // 模拟登录验证
      if (formData.email === '<EMAIL>' && formData.password === 'password') {
        // 登录成功，跳转到仪表板
        router.push('/dashboard');
      } else {
        setError('邮箱或密码错误，请重试');
      }
    } catch (err) {
      setError('登录失败，请稍后重试');
    } finally {
      setIsLoading(false);
    }
  };

  const handleDemoLogin = () => {
    setFormData({
      email: '<EMAIL>',
      password: 'password',
    });
  };

  return (
    <AuthLayout
      title='登录到OCTI'
      description='欢迎回到OCTI智能评估系统'
      footer={
        <p className='text-muted-foreground'>
          还没有账户？{' '}
          <Link href='/register' className='text-primary hover:underline'>
            立即注册
          </Link>
        </p>
      }
    >
      <form onSubmit={handleSubmit} className='space-y-4'>
        {/* 错误消息 */}
        {error && <AuthError message={error} />}

        {/* 邮箱输入 */}
        <div className='space-y-2'>
          <Label htmlFor='email'>邮箱地址</Label>
          <Input
            id='email'
            name='email'
            type='email'
            placeholder='请输入您的邮箱地址'
            value={formData.email}
            onChange={handleInputChange}
            required
            disabled={isLoading}
          />
        </div>

        {/* 密码输入 */}
        <div className='space-y-2'>
          <div className='flex items-center justify-between'>
            <Label htmlFor='password'>密码</Label>
            <Link href='/forgot-password' className='text-sm text-primary hover:underline'>
              忘记密码？
            </Link>
          </div>
          <Input
            id='password'
            name='password'
            type='password'
            placeholder='请输入您的密码'
            value={formData.password}
            onChange={handleInputChange}
            required
            disabled={isLoading}
          />
        </div>

        {/* 登录按钮 */}
        <Button type='submit' className='w-full' disabled={isLoading}>
          {isLoading ? '登录中...' : '登录'}
        </Button>

        {/* 分隔线 */}
        <AuthDivider />

        {/* 演示登录 */}
        <div className='space-y-2'>
          <Button
            type='button'
            variant='outline'
            className='w-full'
            onClick={handleDemoLogin}
            disabled={isLoading}
          >
            🚀 使用演示账户登录
          </Button>
          <p className='text-xs text-muted-foreground text-center'>
            演示账户：<EMAIL> / password
          </p>
        </div>

        {/* 其他登录方式 */}
        <div className='space-y-2'>
          <Button type='button' variant='outline' className='w-full' disabled={isLoading}>
            <span className='mr-2'>🔗</span>
            使用企业SSO登录
          </Button>
        </div>
      </form>

      {/* 系统信息 */}
      <div className='mt-6 rounded-lg bg-muted/50 p-4'>
        <h3 className='text-sm font-medium mb-2'>系统特性</h3>
        <ul className='space-y-1 text-xs text-muted-foreground'>
          <li>• 基于AI的个性化问卷生成</li>
          <li>• 双模型协作分析评估</li>
          <li>• 专业的公益机构能力评估</li>
          <li>• 智能对话收集组织画像</li>
        </ul>
      </div>
    </AuthLayout>
  );
}
