'use client';

import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ArrowLeft, Shield, Lock, Eye, FileText } from 'lucide-react';
import Link from 'next/link';

/**
 * 隐私政策页面
 * 详细说明数据收集、使用和保护政策
 */
export default function PrivacyPage() {
  const privacySections = [
    {
      title: '信息收集',
      icon: <FileText className='h-5 w-5' />,
      content: [
        '我们收集您主动提供的组织信息，包括组织名称、规模、行业等基本信息',
        '评估过程中的问卷回答和选择，用于生成个性化的评估报告',
        '系统使用日志，包括访问时间、IP地址等技术信息，用于系统优化',
        '不会收集与评估无关的个人敏感信息',
      ],
    },
    {
      title: '信息使用',
      icon: <Eye className='h-5 w-5' />,
      content: [
        '生成个性化的组织能力评估报告和改进建议',
        '改进和优化评估算法，提升评估准确性',
        '提供技术支持和客户服务',
        '发送重要的系统更新和安全通知',
        '进行匿名化的数据分析，改进产品功能',
      ],
    },
    {
      title: '信息保护',
      icon: <Lock className='h-5 w-5' />,
      content: [
        '采用企业级加密技术保护数据传输和存储',
        '实施严格的访问控制，只有授权人员可以访问数据',
        '定期进行安全审计和漏洞扫描',
        '建立完善的数据备份和恢复机制',
        '遵循国际数据保护标准和最佳实践',
      ],
    },
    {
      title: '用户权利',
      icon: <Shield className='h-5 w-5' />,
      content: [
        '您有权查看、修改或删除您的个人信息',
        '您可以随时撤回对数据处理的同意',
        '您有权要求数据可携带性',
        '您可以对数据处理提出异议',
        '我们将在合理时间内响应您的请求',
      ],
    },
  ];

  return (
    <div className='min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100'>
      <div className='container mx-auto px-4 py-8'>
        {/* 页面头部 */}
        <div className='mb-8'>
          <Link href='/'>
            <Button variant='ghost' className='mb-4'>
              <ArrowLeft className='h-4 w-4 mr-2' />
              返回首页
            </Button>
          </Link>
          <h1 className='text-4xl font-bold text-gray-900 mb-4'>隐私政策</h1>
          <p className='text-xl text-gray-600 mb-4'>我们重视您的隐私，致力于保护您的个人信息安全</p>
          <p className='text-sm text-gray-500'>最后更新时间：2025年8月14日</p>
        </div>

        {/* 政策概述 */}
        <Card className='mb-8'>
          <CardHeader>
            <CardTitle>政策概述</CardTitle>
          </CardHeader>
          <CardContent>
            <p className='text-gray-700 leading-relaxed'>
              OCTI智能评估系统（以下简称“我们”）深知个人信息对您的重要性，并会尽全力保护您的个人信息安全可靠。
              我们致力于维持您对我们的信任，恪守以下原则，保护您的个人信息：权责一致原则、目的明确原则、
              选择同意原则、最少够用原则、确保安全原则、主体参与原则、公开透明原则等。
            </p>
          </CardContent>
        </Card>

        {/* 详细政策 */}
        <div className='grid gap-6 mb-8'>
          {privacySections.map((section, index) => (
            <Card key={index}>
              <CardHeader>
                <CardTitle className='flex items-center gap-2'>
                  {section.icon}
                  {section.title}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ul className='space-y-3'>
                  {section.content.map((item, itemIndex) => (
                    <li key={itemIndex} className='text-gray-700 leading-relaxed'>
                      • {item}
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Cookie政策 */}
        <Card className='mb-8'>
          <CardHeader>
            <CardTitle>Cookie和类似技术</CardTitle>
          </CardHeader>
          <CardContent>
            <div className='space-y-4 text-gray-700'>
              <p>我们使用Cookie和类似技术来改善用户体验、提供个性化内容和分析网站使用情况。</p>
              <div>
                <h4 className='font-semibold mb-2'>我们使用的Cookie类型：</h4>
                <ul className='space-y-2 ml-4'>
                  <li>
                    • <strong>必要Cookie</strong>：确保网站正常运行的基本功能
                  </li>
                  <li>
                    • <strong>功能Cookie</strong>：记住您的偏好设置和选择
                  </li>
                  <li>
                    • <strong>分析Cookie</strong>：帮助我们了解网站使用情况
                  </li>
                  <li>
                    • <strong>安全Cookie</strong>：保护您的账户和数据安全
                  </li>
                </ul>
              </div>
              <p>您可以通过浏览器设置管理Cookie偏好，但请注意，禁用某些Cookie可能影响网站功能。</p>
            </div>
          </CardContent>
        </Card>

        {/* 政策更新 */}
        <Card className='mb-8'>
          <CardHeader>
            <CardTitle>政策更新</CardTitle>
          </CardHeader>
          <CardContent>
            <p className='text-gray-700 leading-relaxed'>
              我们可能会不时更新本隐私政策。当我们对隐私政策进行重大更改时，我们会通过网站通知、
              电子邮件或其他适当方式告知您。我们建议您定期查看本政策，以了解我们如何保护您的信息。
            </p>
          </CardContent>
        </Card>

        {/* 联系我们 */}
        <Card>
          <CardHeader>
            <CardTitle>联系我们</CardTitle>
          </CardHeader>
          <CardContent>
            <p className='text-gray-700 mb-4'>
              如果您对本隐私政策有任何疑问、意见或建议，请通过以下方式联系我们：
            </p>
            <div className='space-y-2 text-gray-700'>
              <p>• 邮箱：<EMAIL></p>
              <p>• 电话：18612268419</p>
              <p>• 地址：北京市东城区东环大厦7楼</p>
            </div>
            <div className='mt-6'>
              <Link href='/contact'>
                <Button>联系客服</Button>
              </Link>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
