/**
 * OCTI智能评估系统 - 问卷答题页面
 *
 * 用户填写评估问卷的主要界面
 */

'use client';

import React, { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { MainLayout, PageContainer, LoadingState } from '@/components/layout/main-layout';
import { QuestionnaireWizard } from '@/components/questionnaire/questionnaire-wizard';
import { Question, QuestionResponse } from '@/components/questionnaire/question-renderer';
import { Alert, AlertDescription } from '@/components/ui/alert';

// ============================================================================
// 模拟数据
// ============================================================================

const mockUser = {
  id: '1',
  name: '张三',
  email: '<EMAIL>',
  role: 'USER',
};

// 模拟问卷数据
const mockQuestionnaire = {
  id: 'questionnaire-1',
  title: '北京环保基金会 - 组织能力评估问卷',
  description: '基于您的组织画像生成的个性化评估问卷，包含32道预设标准题目和28道AI智能生成题目。',
  totalQuestions: 60,
  estimatedDuration: 45,
};

// 模拟问题数据
const mockQuestions: Question[] = [
  // 预设题目示例
  {
    id: 'q1',
    type: 'SINGLE_CHOICE',
    source: 'PRESET',
    category: '战略规划',
    subCategory: '使命愿景',
    title: '您的组织是否有明确的使命陈述？',
    description: '使命陈述是组织存在的根本目的和价值主张的简洁表达。',
    options: [
      { text: '有，且全体成员都清楚了解', value: 'clear_mission' },
      { text: '有，但部分成员不够了解', value: 'partial_understanding' },
      { text: '有，但表述不够清晰', value: 'unclear_mission' },
      { text: '正在制定中', value: 'developing' },
      { text: '没有明确的使命陈述', value: 'no_mission' },
    ],
    required: true,
    order: 1,
  },
  {
    id: 'q2',
    type: 'SCALE',
    source: 'PRESET',
    category: '治理结构',
    subCategory: '决策机制',
    title: '您认为组织的决策过程透明度如何？',
    description: '评估组织内部决策过程的公开性和可理解性。',
    options: {
      min: 1,
      max: 5,
      labels: ['非常不透明', '不太透明', '一般', '比较透明', '非常透明'],
    },
    required: true,
    order: 2,
  },
  {
    id: 'q3',
    type: 'MULTIPLE_CHOICE',
    source: 'AI_GENERATED',
    category: '环保项目',
    subCategory: '项目类型',
    title: '基于您的组织特点，请选择目前正在开展的环保项目类型：',
    description: '根据您之前提到的环保基金会背景，我们想了解您具体的项目领域。',
    options: [
      { text: '大气污染治理', value: 'air_pollution' },
      { text: '水资源保护', value: 'water_protection' },
      { text: '生物多样性保护', value: 'biodiversity' },
      { text: '可持续发展教育', value: 'sustainability_education' },
      { text: '绿色能源推广', value: 'green_energy' },
      { text: '废物回收利用', value: 'waste_recycling' },
    ],
    required: true,
    order: 3,
  },
  {
    id: 'q4',
    type: 'TEXT',
    source: 'AI_GENERATED',
    category: '资源管理',
    subCategory: '资金筹集',
    title: '请描述您的组织在资金筹集方面面临的主要挑战：',
    description: '基于您提到的资金来源多样化需求，请详细说明具体困难。',
    options: {
      maxLength: 500,
      placeholder: '请详细描述资金筹集过程中遇到的困难、挑战或瓶颈...',
    },
    required: false,
    order: 4,
  },
  {
    id: 'q5',
    type: 'BOOLEAN',
    source: 'PRESET',
    category: '信息技术',
    subCategory: '数字化程度',
    title: '您的组织是否使用专门的项目管理软件？',
    description: '了解组织在项目管理数字化方面的应用情况。',
    options: [
      { text: '是', value: true },
      { text: '否', value: false },
    ],
    required: true,
    order: 5,
  },
];

// 模拟已有回答
const mockInitialResponses: QuestionResponse[] = [
  {
    questionId: 'q1',
    answer: 'clear_mission',
    timeSpent: 15,
  },
];

// ============================================================================
// 问卷答题页面组件
// ============================================================================

export default function QuestionnairePage() {
  const params = useParams();
  const router = useRouter();
  const assessmentId = params.assessmentId as string;

  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');
  const [questionnaire, setQuestionnaire] = useState(mockQuestionnaire);
  const [questions, setQuestions] = useState<Question[]>([]);
  const [initialResponses, setInitialResponses] = useState<QuestionResponse[]>([]);

  // 加载问卷数据
  useEffect(() => {
    const loadQuestionnaire = async () => {
      try {
        setIsLoading(true);

        // TODO: 实际的API调用
        // const response = await fetch(`/api/assessments/${assessmentId}/questionnaire`);
        // const data = await response.json();

        // 模拟加载延迟
        await new Promise(resolve => setTimeout(resolve, 1500));

        setQuestionnaire(mockQuestionnaire);
        setQuestions(mockQuestions);
        setInitialResponses(mockInitialResponses);
      } catch (err) {
        setError('加载问卷失败，请刷新页面重试');
        console.error('加载问卷失败:', err);
      } finally {
        setIsLoading(false);
      }
    };

    if (assessmentId) {
      loadQuestionnaire();
    }
  }, [assessmentId]);

  // 保存草稿
  const handleSave = async (responses: QuestionResponse[]) => {
    try {
      // TODO: 实际的API调用
      // await fetch(`/api/assessments/${assessmentId}/responses`, {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify({ responses, status: 'DRAFT' }),
      // });

      console.log('保存草稿:', responses);
    } catch (err) {
      console.error('保存失败:', err);
      throw err;
    }
  };

  // 提交问卷
  const handleSubmit = async (responses: QuestionResponse[]) => {
    try {
      // TODO: 实际的API调用
      // await fetch(`/api/assessments/${assessmentId}/responses`, {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify({ responses, status: 'COMPLETED' }),
      // });

      console.log('提交问卷:', responses);

      // 跳转到分析页面
      router.push(`/assessments/${assessmentId}/analysis`);
    } catch (err) {
      console.error('提交失败:', err);
      throw err;
    }
  };

  // 退出问卷
  const handleExit = () => {
    if (confirm('确定要退出问卷吗？未保存的答案将会丢失。')) {
      router.push(`/assessments/${assessmentId}`);
    }
  };

  if (isLoading) {
    return (
      <MainLayout user={mockUser}>
        <PageContainer>
          <LoadingState
            title='正在加载问卷...'
            description='请稍候，我们正在为您准备个性化的评估问卷'
          />
        </PageContainer>
      </MainLayout>
    );
  }

  if (error) {
    return (
      <MainLayout user={mockUser}>
        <PageContainer>
          <Alert className='max-w-2xl mx-auto'>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        </PageContainer>
      </MainLayout>
    );
  }

  return (
    <MainLayout user={mockUser} showSidebar={false}>
      <div className='min-h-screen bg-muted/30'>
        <div className='container mx-auto py-8'>
          <QuestionnaireWizard
            questionnaire={questionnaire}
            questions={questions}
            initialResponses={initialResponses}
            onSave={handleSave}
            onSubmit={handleSubmit}
            onExit={handleExit}
            autoSave={true}
          />
        </div>
      </div>
    </MainLayout>
  );
}
