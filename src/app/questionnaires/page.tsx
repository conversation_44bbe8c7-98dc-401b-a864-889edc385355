/**
 * OCTI智能评估系统 - 问卷管理页面
 *
 * 管理问卷模板、预设题目、AI生成配置等
 */

'use client';

import React, { useState } from 'react';

import {
  MainLayout,
  PageContainer,
  CardContainer,
  EmptyState,
} from '@/components/layout/main-layout';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { SimpleProgressIndicator } from '@/components/questionnaire/progress-indicator';

// ============================================================================
// 模拟数据
// ============================================================================

const mockUser = {
  id: '1',
  name: '张三',
  email: '<EMAIL>',
  role: 'USER',
};

const mockQuestionnaireTemplates = [
  {
    id: 'template-1',
    name: '标准组织能力评估模板',
    description: '适用于大多数公益机构的基础能力评估模板',
    type: 'STANDARD',
    totalQuestions: 45,
    categories: ['战略规划', '治理结构', '资源管理', '项目执行', '影响评估'],
    usage: 156,
    lastUpdated: '2024-01-15',
    status: 'ACTIVE',
  },
  {
    id: 'template-2',
    name: '专业组织能力评估模板',
    description: '深度全面的组织能力评估，包含AI智能生成题目',
    type: 'PROFESSIONAL',
    totalQuestions: 60,
    categories: ['战略规划', '治理结构', '资源管理', '项目执行', '影响评估', '创新发展'],
    usage: 89,
    lastUpdated: '2024-01-12',
    status: 'ACTIVE',
  },
  {
    id: 'template-3',
    name: '环保机构专用模板',
    description: '专门为环保类公益机构设计的评估模板',
    type: 'SPECIALIZED',
    totalQuestions: 52,
    categories: ['环保项目', '可持续发展', '社区参与', '政策倡导', '科学研究'],
    usage: 34,
    lastUpdated: '2024-01-10',
    status: 'DRAFT',
  },
];

const mockPresetQuestions = [
  {
    id: 'preset-1',
    category: '战略规划',
    subCategory: '使命愿景',
    title: '您的组织是否有明确的使命陈述？',
    type: 'SINGLE_CHOICE',
    required: true,
    usage: 245,
    effectiveness: 4.8,
  },
  {
    id: 'preset-2',
    category: '治理结构',
    subCategory: '决策机制',
    title: '您认为组织的决策过程透明度如何？',
    type: 'SCALE',
    required: true,
    usage: 238,
    effectiveness: 4.6,
  },
  {
    id: 'preset-3',
    category: '资源管理',
    subCategory: '财务管理',
    title: '组织是否建立了完善的财务管理制度？',
    type: 'BOOLEAN',
    required: true,
    usage: 221,
    effectiveness: 4.7,
  },
];

const mockAIGenerationStats = {
  totalGenerated: 1247,
  successRate: 94.2,
  averageQuality: 4.3,
  topCategories: [
    { name: '项目执行', count: 234 },
    { name: '资源管理', count: 198 },
    { name: '社区参与', count: 167 },
  ],
  recentActivity: [
    { date: '2024-01-15', count: 23, quality: 4.5 },
    { date: '2024-01-14', count: 18, quality: 4.2 },
    { date: '2024-01-13', count: 31, quality: 4.6 },
  ],
};

// ============================================================================
// 问卷管理页面组件
// ============================================================================

export default function QuestionnairesPage() {
  const [activeTab, setActiveTab] = useState<'templates' | 'presets' | 'ai-config'>('templates');
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');

  const filteredTemplates = mockQuestionnaireTemplates.filter(template => {
    const matchesSearch =
      template.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      template.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || template.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      ACTIVE: { label: '启用', variant: 'default' as const },
      DRAFT: { label: '草稿', variant: 'secondary' as const },
      ARCHIVED: { label: '归档', variant: 'outline' as const },
    };
    const config = statusConfig[status as keyof typeof statusConfig] || {
      label: status,
      variant: 'secondary' as const,
    };
    return <Badge variant={config.variant}>{config.label}</Badge>;
  };

  const getTypeBadge = (type: string) => {
    const typeConfig = {
      STANDARD: { label: '标准版', color: 'bg-blue-100 text-blue-800' },
      PROFESSIONAL: { label: '专业版', color: 'bg-purple-100 text-purple-800' },
      SPECIALIZED: { label: '专用版', color: 'bg-green-100 text-green-800' },
    };
    const config = typeConfig[type as keyof typeof typeConfig] || {
      label: type,
      color: 'bg-gray-100 text-gray-800',
    };
    return <Badge className={config.color}>{config.label}</Badge>;
  };

  const renderTemplatesTab = () => (
    <div className='space-y-6'>
      {/* 搜索和筛选 */}
      <CardContainer>
        <div className='flex flex-col sm:flex-row gap-4'>
          <div className='flex-1'>
            <Input
              placeholder='搜索问卷模板...'
              value={searchTerm}
              onChange={e => setSearchTerm(e.target.value)}
            />
          </div>
          <div className='flex gap-2'>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className='w-32'>
                <SelectValue placeholder='状态' />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value='all'>全部状态</SelectItem>
                <SelectItem value='ACTIVE'>启用</SelectItem>
                <SelectItem value='DRAFT'>草稿</SelectItem>
                <SelectItem value='ARCHIVED'>归档</SelectItem>
              </SelectContent>
            </Select>
            <Button>创建模板</Button>
          </div>
        </div>
      </CardContainer>

      {/* 模板列表 */}
      {filteredTemplates.length === 0 ? (
        <EmptyState
          title='暂无问卷模板'
          description='您还没有创建任何问卷模板，点击下方按钮创建您的第一个模板。'
          action={<Button>创建模板</Button>}
        />
      ) : (
        <div className='grid gap-6 md:grid-cols-2 lg:grid-cols-3'>
          {filteredTemplates.map(template => (
            <Card key={template.id} className='hover:shadow-md transition-shadow'>
              <CardHeader>
                <div className='flex items-start justify-between'>
                  <div className='space-y-1'>
                    <CardTitle className='text-lg'>{template.name}</CardTitle>
                    <CardDescription className='line-clamp-2'>
                      {template.description}
                    </CardDescription>
                  </div>
                  <div className='flex flex-col items-end space-y-1'>
                    {getStatusBadge(template.status)}
                    {getTypeBadge(template.type)}
                  </div>
                </div>
              </CardHeader>

              <CardContent className='space-y-4'>
                <div className='grid grid-cols-2 gap-4 text-sm'>
                  <div>
                    <span className='text-muted-foreground'>题目数量</span>
                    <div className='font-medium'>{template.totalQuestions}</div>
                  </div>
                  <div>
                    <span className='text-muted-foreground'>使用次数</span>
                    <div className='font-medium'>{template.usage}</div>
                  </div>
                </div>

                <div>
                  <span className='text-sm text-muted-foreground'>评估维度</span>
                  <div className='flex flex-wrap gap-1 mt-1'>
                    {template.categories.slice(0, 3).map((category, index) => (
                      <Badge key={index} variant='outline' className='text-xs'>
                        {category}
                      </Badge>
                    ))}
                    {template.categories.length > 3 && (
                      <Badge variant='outline' className='text-xs'>
                        +{template.categories.length - 3}
                      </Badge>
                    )}
                  </div>
                </div>

                <div className='text-xs text-muted-foreground'>
                  最后更新: {template.lastUpdated}
                </div>

                <div className='flex space-x-2 pt-2'>
                  <Button variant='outline' className='flex-1' size='sm'>
                    编辑
                  </Button>
                  <Button className='flex-1' size='sm'>
                    使用
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );

  const renderPresetsTab = () => (
    <div className='space-y-6'>
      <CardContainer
        title='预设题库管理'
        description='管理32道标准预设题目，这些题目将在所有评估中使用'
        actions={<Button>添加题目</Button>}
      >
        <div className='space-y-4'>
          {mockPresetQuestions.map(question => (
            <div key={question.id} className='border rounded-lg p-4'>
              <div className='flex items-start justify-between'>
                <div className='flex-1'>
                  <div className='flex items-center space-x-2 mb-2'>
                    <Badge variant='outline'>{question.category}</Badge>
                    <Badge variant='outline'>{question.subCategory}</Badge>
                    <Badge variant='outline'>{question.type}</Badge>
                    {question.required && <Badge variant='destructive'>必答</Badge>}
                  </div>
                  <h4 className='font-medium mb-2'>{question.title}</h4>
                  <div className='flex items-center space-x-4 text-sm text-muted-foreground'>
                    <span>使用次数: {question.usage}</span>
                    <span>效果评分: {question.effectiveness}/5.0</span>
                  </div>
                </div>
                <div className='flex space-x-2'>
                  <Button variant='outline' size='sm'>
                    编辑
                  </Button>
                  <Button variant='outline' size='sm'>
                    预览
                  </Button>
                </div>
              </div>
            </div>
          ))}
        </div>
      </CardContainer>
    </div>
  );

  const renderAIConfigTab = () => (
    <div className='space-y-6'>
      <div className='grid md:grid-cols-2 gap-6'>
        {/* AI生成统计 */}
        <CardContainer title='AI生成统计' description='智能问题生成的整体表现'>
          <div className='space-y-4'>
            <div className='grid grid-cols-2 gap-4'>
              <div className='text-center p-3 border rounded-lg'>
                <div className='text-2xl font-bold text-primary'>
                  {mockAIGenerationStats.totalGenerated}
                </div>
                <div className='text-sm text-muted-foreground'>总生成数</div>
              </div>
              <div className='text-center p-3 border rounded-lg'>
                <div className='text-2xl font-bold text-green-600'>
                  {mockAIGenerationStats.successRate}%
                </div>
                <div className='text-sm text-muted-foreground'>成功率</div>
              </div>
            </div>

            <SimpleProgressIndicator
              current={mockAIGenerationStats.averageQuality}
              total={5}
              label='平均质量评分'
              showPercentage={false}
            />
          </div>
        </CardContainer>

        {/* 热门分类 */}
        <CardContainer title='热门生成分类' description='AI最常生成的问题类别'>
          <div className='space-y-3'>
            {mockAIGenerationStats.topCategories.map((category, index) => (
              <div key={index} className='flex items-center justify-between'>
                <span className='font-medium'>{category.name}</span>
                <div className='flex items-center space-x-2'>
                  <div className='w-20 bg-muted rounded-full h-2'>
                    <div
                      className='bg-primary h-2 rounded-full'
                      style={{
                        width: `${(category.count / (mockAIGenerationStats.topCategories[0]?.count || 1)) * 100}%`,
                      }}
                    ></div>
                  </div>
                  <span className='text-sm text-muted-foreground'>{category.count}</span>
                </div>
              </div>
            ))}
          </div>
        </CardContainer>
      </div>

      {/* AI配置管理 */}
      <CardContainer
        title='AI配置管理'
        description='管理问卷设计师和组织评估导师的配置参数'
        actions={<Button>编辑配置</Button>}
      >
        <div className='grid md:grid-cols-2 gap-6'>
          <div className='space-y-3'>
            <h4 className='font-medium'>问卷设计师配置</h4>
            <div className='space-y-2 text-sm'>
              <div className='flex justify-between'>
                <span className='text-muted-foreground'>配置版本:</span>
                <span>v2.1.0</span>
              </div>
              <div className='flex justify-between'>
                <span className='text-muted-foreground'>最后更新:</span>
                <span>2024-01-15</span>
              </div>
              <div className='flex justify-between'>
                <span className='text-muted-foreground'>生成模式:</span>
                <Badge variant='outline'>智能适配</Badge>
              </div>
            </div>
          </div>

          <div className='space-y-3'>
            <h4 className='font-medium'>组织评估导师配置</h4>
            <div className='space-y-2 text-sm'>
              <div className='flex justify-between'>
                <span className='text-muted-foreground'>配置版本:</span>
                <span>v1.8.2</span>
              </div>
              <div className='flex justify-between'>
                <span className='text-muted-foreground'>最后更新:</span>
                <span>2024-01-12</span>
              </div>
              <div className='flex justify-between'>
                <span className='text-muted-foreground'>分析模式:</span>
                <Badge variant='outline'>双模型协作</Badge>
              </div>
            </div>
          </div>
        </div>
      </CardContainer>
    </div>
  );

  return (
    <MainLayout user={mockUser}>
      <PageContainer
        title='问卷管理'
        description='管理问卷模板、预设题目和AI生成配置'
        breadcrumb={[{ title: '首页', href: '/' }, { title: '问卷管理' }]}
      >
        {/* 标签页导航 */}
        <div className='flex space-x-1 p-1 bg-muted rounded-lg w-fit'>
          <button
            className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
              activeTab === 'templates'
                ? 'bg-background text-foreground shadow-sm'
                : 'text-muted-foreground hover:text-foreground'
            }`}
            onClick={() => setActiveTab('templates')}
          >
            问卷模板
          </button>
          <button
            className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
              activeTab === 'presets'
                ? 'bg-background text-foreground shadow-sm'
                : 'text-muted-foreground hover:text-foreground'
            }`}
            onClick={() => setActiveTab('presets')}
          >
            预设题库
          </button>
          <button
            className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
              activeTab === 'ai-config'
                ? 'bg-background text-foreground shadow-sm'
                : 'text-muted-foreground hover:text-foreground'
            }`}
            onClick={() => setActiveTab('ai-config')}
          >
            AI配置
          </button>
        </div>

        {/* 标签页内容 */}
        {activeTab === 'templates' && renderTemplatesTab()}
        {activeTab === 'presets' && renderPresetsTab()}
        {activeTab === 'ai-config' && renderAIConfigTab()}
      </PageContainer>
    </MainLayout>
  );
}
