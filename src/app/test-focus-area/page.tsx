'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

/**
 * 关注领域优先级测试页面
 * 用于验证用户指定的关注领域是否被正确使用
 */
export default function TestFocusAreaPage() {
  const [testData, setTestData] = useState({
    organizationType: '民间组织',
    mainFocus: '公益咨询',
    organizationScale: '中型',
    developmentStage: '成长期',
  });

  const [testResult, setTestResult] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);

  // 模拟保存用户输入
  const saveTestData = () => {
    const organizationInfo = {
      organizationName: '测试公益咨询机构',
      organizationType: testData.organizationType,
      organizationScale: testData.organizationScale,
      mainFocus: testData.mainFocus,
      location: '北京',
      establishedYear: '2020',
      contactPerson: '测试联系人',
      contactEmail: '<EMAIL>',
    };

    // 保存到localStorage
    localStorage.setItem('octi_organization_info', JSON.stringify(organizationInfo));
    
    // 模拟画像答案
    const profileAnswers = {
      1: 'C', // 成长期
      2: 'A', // 直接服务
      3: 'C', // 公益组织
      4: 'B', // 中型
      5: 'B', // 区域影响
      6: 'F', // 使命驱动
      7: 'F', // 使命驱动文化
    };
    localStorage.setItem('profileAnswers', JSON.stringify(profileAnswers));

    console.log('✅ 测试数据已保存到localStorage');
    alert('测试数据已保存！');
  };

  // 测试组织画像提取
  const testProfileExtraction = async () => {
    setIsLoading(true);
    try {
      // 动态导入服务
      const { extractOrganizationProfile } = await import('@/services/questionnaire-service');
      
      const profile = extractOrganizationProfile();
      setTestResult({
        type: 'profile',
        data: profile,
        timestamp: new Date().toISOString(),
      });

      console.log('📊 提取的组织画像:', profile);
    } catch (error) {
      console.error('❌ 测试失败:', error);
      alert('测试失败: ' + (error as Error).message);
    } finally {
      setIsLoading(false);
    }
  };

  // 测试智能问题生成
  const testQuestionGeneration = async () => {
    setIsLoading(true);
    try {
      // 先提取组织画像
      const { extractOrganizationProfile } = await import('@/services/questionnaire-service');
      const profile = extractOrganizationProfile();

      // 生成智能问题
      const { intelligentQuestionGenerator } = await import('@/services/intelligent-question-generator');
      const questions = await intelligentQuestionGenerator.generateIntelligentQuestions(profile);

      setTestResult({
        type: 'questions',
        data: {
          profile,
          questions: questions.slice(0, 5), // 只显示前5个问题
          totalCount: questions.length,
        },
        timestamp: new Date().toISOString(),
      });

      console.log('🎯 生成的智能问题:', questions);
    } catch (error) {
      console.error('❌ 测试失败:', error);
      alert('测试失败: ' + (error as Error).message);
    } finally {
      setIsLoading(false);
    }
  };

  // 清除测试数据
  const clearTestData = () => {
    localStorage.removeItem('octi_organization_info');
    localStorage.removeItem('profileAnswers');
    localStorage.removeItem('userInputs');
    setTestResult(null);
    console.log('🧹 测试数据已清除');
    alert('测试数据已清除！');
  };

  return (
    <div className="container mx-auto py-8 space-y-6">
      <div className="text-center">
        <h1 className="text-3xl font-bold">关注领域优先级测试</h1>
        <p className="text-muted-foreground mt-2">
          验证用户指定的关注领域是否被正确使用
        </p>
      </div>

      {/* 测试数据设置 */}
      <Card>
        <CardHeader>
          <CardTitle>测试数据设置</CardTitle>
          <CardDescription>
            设置测试用的组织信息，特别是"主要关注领域"
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium mb-2">组织类型</label>
              <Select
                value={testData.organizationType}
                onValueChange={(value) => setTestData(prev => ({ ...prev, organizationType: value }))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="民间组织">民间组织</SelectItem>
                  <SelectItem value="基金会">基金会</SelectItem>
                  <SelectItem value="社会企业">社会企业</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">主要关注领域</label>
              <Input
                value={testData.mainFocus}
                onChange={(e) => setTestData(prev => ({ ...prev, mainFocus: e.target.value }))}
                placeholder="如：公益咨询、教育、环保等"
              />
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">组织规模</label>
              <Select
                value={testData.organizationScale}
                onValueChange={(value) => setTestData(prev => ({ ...prev, organizationScale: value }))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="小型">小型</SelectItem>
                  <SelectItem value="中型">中型</SelectItem>
                  <SelectItem value="大型">大型</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">发展阶段</label>
              <Select
                value={testData.developmentStage}
                onValueChange={(value) => setTestData(prev => ({ ...prev, developmentStage: value }))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="初创期">初创期</SelectItem>
                  <SelectItem value="成长期">成长期</SelectItem>
                  <SelectItem value="成熟期">成熟期</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="flex gap-2">
            <Button onClick={saveTestData}>保存测试数据</Button>
            <Button variant="outline" onClick={clearTestData}>清除测试数据</Button>
          </div>
        </CardContent>
      </Card>

      {/* 测试操作 */}
      <Card>
        <CardHeader>
          <CardTitle>测试操作</CardTitle>
          <CardDescription>
            执行各种测试来验证关注领域优先级逻辑
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-2">
            <Button 
              onClick={testProfileExtraction}
              disabled={isLoading}
            >
              测试组织画像提取
            </Button>
            <Button 
              onClick={testQuestionGeneration}
              disabled={isLoading}
              variant="outline"
            >
              测试智能问题生成
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* 测试结果 */}
      {testResult && (
        <Card>
          <CardHeader>
            <CardTitle>测试结果</CardTitle>
            <CardDescription>
              {testResult.type === 'profile' ? '组织画像提取结果' : '智能问题生成结果'}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="bg-gray-50 p-4 rounded-lg">
              <pre className="text-sm overflow-auto">
                {JSON.stringify(testResult.data, null, 2)}
              </pre>
            </div>
            
            {testResult.type === 'profile' && (
              <div className="mt-4 p-4 border rounded-lg">
                <h4 className="font-semibold mb-2">关键验证点：</h4>
                <ul className="space-y-1 text-sm">
                  <li>
                    <strong>用户指定关注领域：</strong> 
                    <span className={testResult.data.userSpecifiedFocusArea ? 'text-green-600' : 'text-red-600'}>
                      {testResult.data.userSpecifiedFocusArea || '未检测到'}
                    </span>
                  </li>
                  <li>
                    <strong>推断的服务领域：</strong> 
                    <span className="text-blue-600">
                      {testResult.data.serviceArea?.join(', ') || '无'}
                    </span>
                  </li>
                  <li>
                    <strong>优先级逻辑：</strong> 
                    <span className="text-purple-600">
                      {testResult.data.userSpecifiedFocusArea ? '✅ 使用用户指定' : '⚠️ 使用系统推断'}
                    </span>
                  </li>
                </ul>
              </div>
            )}

            {testResult.type === 'questions' && (
              <div className="mt-4 p-4 border rounded-lg">
                <h4 className="font-semibold mb-2">问题相关性验证：</h4>
                <p className="text-sm mb-2">
                  <strong>目标关注领域：</strong> 
                  <span className="text-green-600">{testData.mainFocus}</span>
                </p>
                <p className="text-sm mb-2">
                  <strong>生成问题数量：</strong> {testResult.data.totalCount}
                </p>
                <div className="text-sm">
                  <strong>前5个问题预览：</strong>
                  <ul className="mt-2 space-y-1">
                    {testResult.data.questions.map((q: any, index: number) => (
                      <li key={index} className="pl-4 border-l-2 border-gray-200">
                        {q.title}
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
}
