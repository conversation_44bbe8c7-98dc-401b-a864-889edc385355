/**
 * OCTI智能评估系统 - OCTI雷达图组件
 *
 * 展示组织能力四维度评估结果的雷达图
 */

'use client';

import React from 'react';
import {
  Radar,
  RadarChart,
  PolarGrid,
  PolarAngleAxis,
  PolarRadiusAxis,
  ResponsiveContainer,
  Legend,
  Tooltip,
} from 'recharts';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';

// ============================================================================
// 类型定义
// ============================================================================

export interface OCTIDimension {
  dimension: string;
  code: string;
  score: number;
  maxScore: number;
  level: 'EXCELLENT' | 'GOOD' | 'AVERAGE' | 'NEEDS_IMPROVEMENT' | 'POOR';
  description: string;
  subDimensions?: {
    name: string;
    score: number;
    maxScore: number;
  }[];
}

interface OCTIRadarChartProps {
  data: OCTIDimension[];
  title?: string;
  showLegend?: boolean;
  showTooltip?: boolean;
  showSubDimensions?: boolean;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

// ============================================================================
// OCTI雷达图组件
// ============================================================================

export function OCTIRadarChart({
  data,
  title = 'OCTI组织能力评估雷达图',
  showLegend = true,
  showTooltip = true,
  showSubDimensions = false,
  size = 'md',
  className,
}: OCTIRadarChartProps) {
  // 转换数据格式为雷达图所需格式
  const chartData = data.map(item => ({
    dimension: item.dimension,
    code: item.code,
    score: item.score,
    maxScore: item.maxScore,
    percentage: Math.round((item.score / item.maxScore) * 100),
    level: item.level,
    fullMark: 100, // 雷达图满分设为100%
  }));

  // 获取等级颜色
  const getLevelColor = (level: string) => {
    const colors = {
      EXCELLENT: '#10b981', // green-500
      GOOD: '#3b82f6', // blue-500
      AVERAGE: '#f59e0b', // amber-500
      NEEDS_IMPROVEMENT: '#ef4444', // red-500
      POOR: '#dc2626', // red-600
    };
    return colors[level as keyof typeof colors] || '#6b7280';
  };

  // 获取等级标签
  const getLevelLabel = (level: string) => {
    const labels = {
      EXCELLENT: '优秀',
      GOOD: '良好',
      AVERAGE: '一般',
      NEEDS_IMPROVEMENT: '待改进',
      POOR: '较差',
    };
    return labels[level as keyof typeof labels] || level;
  };

  // 尺寸配置
  const sizeConfig = {
    sm: { height: 300, fontSize: 12 },
    md: { height: 400, fontSize: 14 },
    lg: { height: 500, fontSize: 16 },
  };

  const config = sizeConfig[size];

  // 自定义Tooltip
  const CustomTooltip = ({ active, payload, label: _label }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className='bg-background border rounded-lg shadow-lg p-3'>
          <h4 className='font-semibold mb-2'>{data.dimension}</h4>
          <div className='space-y-1 text-sm'>
            <div className='flex justify-between'>
              <span>得分:</span>
              <span className='font-medium'>
                {data.score}/{data.maxScore}
              </span>
            </div>
            <div className='flex justify-between'>
              <span>百分比:</span>
              <span className='font-medium'>{data.percentage}%</span>
            </div>
            <div className='flex justify-between items-center'>
              <span>等级:</span>
              <Badge style={{ backgroundColor: getLevelColor(data.level) }} className='text-white'>
                {getLevelLabel(data.level)}
              </Badge>
            </div>
          </div>
        </div>
      );
    }
    return null;
  };

  return (
    <div className={cn('space-y-4', className)}>
      {/* 标题 */}
      {title && (
        <div className='text-center'>
          <h3 className='text-lg font-semibold'>{title}</h3>
        </div>
      )}

      {/* 雷达图 */}
      <div style={{ height: config.height }}>
        <ResponsiveContainer width='100%' height='100%'>
          <RadarChart data={chartData} margin={{ top: 20, right: 30, bottom: 20, left: 30 }}>
            <PolarGrid gridType='polygon' />
            <PolarAngleAxis
              dataKey='dimension'
              tick={{ fontSize: config.fontSize, fill: '#6b7280' }}
              className='text-muted-foreground'
            />
            <PolarRadiusAxis
              angle={90}
              domain={[0, 100]}
              tick={{ fontSize: config.fontSize - 2, fill: '#9ca3af' }}
              tickCount={6}
            />
            <Radar
              name='能力评分'
              dataKey='percentage'
              stroke='#3b82f6'
              fill='#3b82f6'
              fillOpacity={0.1}
              strokeWidth={2}
              dot={{ fill: '#3b82f6', strokeWidth: 2, r: 4 }}
            />
            {showTooltip && <Tooltip content={<CustomTooltip />} />}
            {showLegend && (
              <Legend wrapperStyle={{ fontSize: config.fontSize }} iconType='circle' />
            )}
          </RadarChart>
        </ResponsiveContainer>
      </div>

      {/* 维度详情 */}
      <div className='grid grid-cols-2 md:grid-cols-4 gap-4'>
        {data.map((item, index) => (
          <div key={index} className='text-center p-3 border rounded-lg'>
            <div className='text-sm font-medium mb-1'>{item.code}</div>
            <div className='text-lg font-bold mb-1'>
              {item.score}/{item.maxScore}
            </div>
            <Badge
              style={{ backgroundColor: getLevelColor(item.level) }}
              className='text-white text-xs'
            >
              {getLevelLabel(item.level)}
            </Badge>
          </div>
        ))}
      </div>

      {/* 子维度详情 */}
      {showSubDimensions && (
        <div className='space-y-4'>
          <h4 className='font-medium'>子维度详情</h4>
          <div className='grid gap-4'>
            {data.map(
              (dimension, index) =>
                dimension.subDimensions &&
                dimension.subDimensions.length > 0 && (
                  <div key={index} className='border rounded-lg p-4'>
                    <h5 className='font-medium mb-3'>{dimension.dimension}</h5>
                    <div className='grid md:grid-cols-2 gap-3'>
                      {dimension.subDimensions.map((sub, subIndex) => (
                        <div
                          key={subIndex}
                          className='flex items-center justify-between p-2 bg-muted/50 rounded'
                        >
                          <span className='text-sm'>{sub.name}</span>
                          <div className='flex items-center space-x-2'>
                            <span className='text-sm font-medium'>
                              {sub.score}/{sub.maxScore}
                            </span>
                            <div className='w-16 bg-muted rounded-full h-2'>
                              <div
                                className='bg-primary h-2 rounded-full'
                                style={{ width: `${(sub.score / sub.maxScore) * 100}%` }}
                              ></div>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )
            )}
          </div>
        </div>
      )}

      {/* 评估说明 */}
      <div className='text-xs text-muted-foreground space-y-1'>
        <p>• SF: 战略规划与治理 (Strategic Framework)</p>
        <p>• IT: 内部治理与管理 (Internal Governance)</p>
        <p>• MV: 使命价值与影响 (Mission & Value)</p>
        <p>• AD: 适应发展能力 (Adaptability & Development)</p>
      </div>
    </div>
  );
}

// ============================================================================
// 简化版雷达图
// ============================================================================

interface SimpleRadarChartProps {
  data: { name: string; value: number; maxValue?: number }[];
  title?: string;
  color?: string;
  size?: number;
  className?: string;
}

export function SimpleRadarChart({
  data,
  title,
  color = '#3b82f6',
  size = 200,
  className,
}: SimpleRadarChartProps) {
  const chartData = data.map(item => ({
    name: item.name,
    value: item.value,
    percentage: Math.round((item.value / (item.maxValue || 100)) * 100),
    fullMark: 100,
  }));

  return (
    <div className={cn('text-center', className)}>
      {title && <h4 className='font-medium mb-2'>{title}</h4>}
      <div style={{ height: size }}>
        <ResponsiveContainer width='100%' height='100%'>
          <RadarChart data={chartData}>
            <PolarGrid />
            <PolarAngleAxis dataKey='name' tick={{ fontSize: 10 }} />
            <PolarRadiusAxis domain={[0, 100]} tick={false} />
            <Radar
              dataKey='percentage'
              stroke={color}
              fill={color}
              fillOpacity={0.2}
              strokeWidth={2}
            />
          </RadarChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
}

// ============================================================================
// 对比雷达图
// ============================================================================

interface ComparisonRadarChartProps {
  currentData: OCTIDimension[];
  benchmarkData?: OCTIDimension[];
  title?: string;
  currentLabel?: string;
  benchmarkLabel?: string;
  className?: string;
}

export function ComparisonRadarChart({
  currentData,
  benchmarkData,
  title = '能力对比分析',
  currentLabel = '当前组织',
  benchmarkLabel = '行业基准',
  className,
}: ComparisonRadarChartProps) {
  const chartData = currentData.map((item, index) => {
    const benchmark = benchmarkData?.[index];
    return {
      dimension: item.dimension,
      current: Math.round((item.score / item.maxScore) * 100),
      benchmark: benchmark ? Math.round((benchmark.score / benchmark.maxScore) * 100) : 0,
      fullMark: 100,
    };
  });

  return (
    <div className={cn('space-y-4', className)}>
      {title && (
        <div className='text-center'>
          <h3 className='text-lg font-semibold'>{title}</h3>
        </div>
      )}

      <div style={{ height: 400 }}>
        <ResponsiveContainer width='100%' height='100%'>
          <RadarChart data={chartData}>
            <PolarGrid />
            <PolarAngleAxis dataKey='dimension' />
            <PolarRadiusAxis domain={[0, 100]} />
            <Radar
              name={currentLabel}
              dataKey='current'
              stroke='#3b82f6'
              fill='#3b82f6'
              fillOpacity={0.1}
              strokeWidth={2}
            />
            {benchmarkData && (
              <Radar
                name={benchmarkLabel}
                dataKey='benchmark'
                stroke='#10b981'
                fill='#10b981'
                fillOpacity={0.1}
                strokeWidth={2}
                strokeDasharray='5 5'
              />
            )}
            <Legend />
            <Tooltip />
          </RadarChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
}
