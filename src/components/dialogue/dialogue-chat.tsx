/**
 * OCTI智能评估系统 - 对话聊天组件
 *
 * 实现多轮对话收集组织画像信息
 */

'use client';

import React, { useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { cn } from '@/lib/utils';

// ============================================================================
// 类型定义
// ============================================================================

export interface DialogueMessage {
  id: string;
  type: 'SYSTEM' | 'USER' | 'ASSISTANT';
  content: string;
  timestamp: Date;
  roundNumber: number;
}

export interface DialogueSession {
  id: string;
  status: 'ACTIVE' | 'COMPLETED' | 'CANCELLED';
  currentRound: number;
  totalRounds: number;
  progress: number;
  extractedInsights?: any;
}

interface DialogueChatProps {
  session: DialogueSession;
  messages: DialogueMessage[];
  onSendMessage: (message: string) => Promise<void>;
  onComplete?: () => void;
  onCancel?: () => void;
  isLoading?: boolean;
  className?: string;
}

// ============================================================================
// 对话聊天组件
// ============================================================================

export function DialogueChat({
  session,
  messages,
  onSendMessage,
  onComplete,
  onCancel,
  isLoading = false,
  className,
}: DialogueChatProps) {
  const [inputMessage, setInputMessage] = useState('');
  const [isSending, setIsSending] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  // 自动滚动到底部
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // 自动聚焦输入框
  useEffect(() => {
    if (!isSending && !isLoading) {
      inputRef.current?.focus();
    }
  }, [isSending, isLoading]);

  const handleSendMessage = async () => {
    if (!inputMessage.trim() || isSending) return;

    const message = inputMessage.trim();
    setInputMessage('');
    setIsSending(true);

    try {
      await onSendMessage(message);
    } catch (error) {
      console.error('发送消息失败:', error);
    } finally {
      setIsSending(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('zh-CN', {
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getMessageIcon = (type: string) => {
    switch (type) {
      case 'SYSTEM':
        return '🤖';
      case 'USER':
        return '👤';
      case 'ASSISTANT':
        return '🎯';
      default:
        return '💬';
    }
  };

  const getMessageLabel = (type: string) => {
    switch (type) {
      case 'SYSTEM':
        return 'OCTI助手';
      case 'USER':
        return '您';
      case 'ASSISTANT':
        return 'AI导师';
      default:
        return '系统';
    }
  };

  return (
    <div className={cn('flex flex-col h-full', className)}>
      {/* 对话头部 */}
      <div className='flex-shrink-0 p-4 border-b bg-muted/50'>
        <div className='flex items-center justify-between'>
          <div className='space-y-1'>
            <h3 className='font-semibold'>组织画像收集对话</h3>
            <p className='text-sm text-muted-foreground'>通过智能对话深度了解您的组织情况</p>
          </div>
          <Badge variant={session.status === 'ACTIVE' ? 'default' : 'secondary'}>
            {session.status === 'ACTIVE'
              ? '进行中'
              : session.status === 'COMPLETED'
                ? '已完成'
                : '已取消'}
          </Badge>
        </div>

        {/* 进度条 */}
        <div className='mt-4 space-y-2'>
          <div className='flex justify-between text-sm'>
            <span>对话进度</span>
            <span>
              第 {session.currentRound} / {session.totalRounds} 轮
            </span>
          </div>
          <Progress value={session.progress} className='h-2' />
        </div>
      </div>

      {/* 消息列表 */}
      <div className='flex-1 overflow-y-auto p-4 space-y-4'>
        {messages.length === 0 ? (
          <div className='text-center py-8'>
            <div className='text-4xl mb-2'>💬</div>
            <p className='text-muted-foreground'>对话即将开始...</p>
          </div>
        ) : (
          messages.map(message => (
            <div
              key={message.id}
              className={cn(
                'flex items-start space-x-3',
                message.type === 'USER' ? 'flex-row-reverse space-x-reverse' : ''
              )}
            >
              {/* 头像 */}
              <div
                className={cn(
                  'flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center text-sm',
                  message.type === 'USER'
                    ? 'bg-primary text-primary-foreground'
                    : 'bg-muted text-muted-foreground'
                )}
              >
                {getMessageIcon(message.type)}
              </div>

              {/* 消息内容 */}
              <div
                className={cn('flex-1 max-w-[80%]', message.type === 'USER' ? 'text-right' : '')}
              >
                <div className='flex items-center space-x-2 mb-1'>
                  <span className='text-xs font-medium text-muted-foreground'>
                    {getMessageLabel(message.type)}
                  </span>
                  <span className='text-xs text-muted-foreground'>
                    {formatTime(message.timestamp)}
                  </span>
                  {message.roundNumber > 0 && (
                    <Badge variant='outline' className='text-xs'>
                      第{message.roundNumber}轮
                    </Badge>
                  )}
                </div>
                <div
                  className={cn(
                    'p-3 rounded-lg',
                    message.type === 'USER'
                      ? 'bg-primary text-primary-foreground ml-auto'
                      : 'bg-muted'
                  )}
                >
                  <p className='text-sm whitespace-pre-wrap'>{message.content}</p>
                </div>
              </div>
            </div>
          ))
        )}

        {/* 加载指示器 */}
        {(isLoading || isSending) && (
          <div className='flex items-start space-x-3'>
            <div className='flex-shrink-0 w-8 h-8 rounded-full bg-muted text-muted-foreground flex items-center justify-center text-sm'>
              🤖
            </div>
            <div className='flex-1'>
              <div className='flex items-center space-x-2 mb-1'>
                <span className='text-xs font-medium text-muted-foreground'>OCTI助手</span>
              </div>
              <div className='bg-muted p-3 rounded-lg'>
                <div className='flex items-center space-x-2'>
                  <div className='flex space-x-1'>
                    <div className='w-2 h-2 bg-muted-foreground rounded-full animate-bounce'></div>
                    <div
                      className='w-2 h-2 bg-muted-foreground rounded-full animate-bounce'
                      style={{ animationDelay: '0.1s' }}
                    ></div>
                    <div
                      className='w-2 h-2 bg-muted-foreground rounded-full animate-bounce'
                      style={{ animationDelay: '0.2s' }}
                    ></div>
                  </div>
                  <span className='text-xs text-muted-foreground'>正在思考...</span>
                </div>
              </div>
            </div>
          </div>
        )}

        <div ref={messagesEndRef} />
      </div>

      {/* 输入区域 */}
      <div className='flex-shrink-0 p-4 border-t'>
        {session.status === 'COMPLETED' ? (
          <Alert>
            <AlertDescription className='flex items-center justify-between'>
              <span>对话已完成！我们已收集到足够的组织信息。</span>
              {onComplete && (
                <Button size='sm' onClick={onComplete}>
                  继续下一步
                </Button>
              )}
            </AlertDescription>
          </Alert>
        ) : session.status === 'CANCELLED' ? (
          <Alert>
            <AlertDescription>对话已取消</AlertDescription>
          </Alert>
        ) : (
          <div className='space-y-3'>
            <div className='flex space-x-2'>
              <Input
                ref={inputRef}
                value={inputMessage}
                onChange={e => setInputMessage(e.target.value)}
                onKeyPress={handleKeyPress}
                placeholder='请输入您的回答...'
                disabled={isSending || isLoading}
                className='flex-1'
              />
              <Button
                onClick={handleSendMessage}
                disabled={!inputMessage.trim() || isSending || isLoading}
              >
                {isSending ? '发送中...' : '发送'}
              </Button>
            </div>

            {/* 快捷回复 */}
            <div className='flex flex-wrap gap-2'>
              <Button
                variant='outline'
                size='sm'
                onClick={() => setInputMessage('我需要更多时间考虑')}
                disabled={isSending || isLoading}
              >
                需要更多时间
              </Button>
              <Button
                variant='outline'
                size='sm'
                onClick={() => setInputMessage('可以跳过这个问题吗？')}
                disabled={isSending || isLoading}
              >
                跳过问题
              </Button>
              <Button
                variant='outline'
                size='sm'
                onClick={() => setInputMessage('我不太确定，能否提供一些选项？')}
                disabled={isSending || isLoading}
              >
                提供选项
              </Button>
            </div>

            {/* 操作按钮 */}
            <div className='flex justify-between items-center text-xs text-muted-foreground'>
              <span>按 Enter 发送，Shift + Enter 换行</span>
              {onCancel && (
                <Button
                  variant='ghost'
                  size='sm'
                  onClick={onCancel}
                  className='text-muted-foreground hover:text-foreground'
                >
                  退出对话
                </Button>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
