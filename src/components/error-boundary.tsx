/**
 * OCTI智能评估系统 - 错误边界组件
 *
 * 捕获和处理React组件错误，提供友好的错误界面
 */

'use client';

import React from 'react';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';

// ============================================================================
// 类型定义
// ============================================================================

interface ErrorBoundaryState {
  hasError: boolean;
  error?: Error | null;
  errorInfo?: React.ErrorInfo | null;
}

interface ErrorBoundaryProps {
  children: React.ReactNode;
  fallback?: React.ComponentType<{ error: Error; resetError: () => void }>;
  onError?: (error: Error, errorInfo: React.ErrorInfo) => void;
}

// ============================================================================
// 错误边界组件
// ============================================================================

export class ErrorBoundary extends React.Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return {
      hasError: true,
      error,
    };
  }

  override componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('ErrorBoundary caught an error:', error, errorInfo);

    this.setState({
      error,
      errorInfo,
    });

    // 调用错误回调
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }
  }

  resetError = () => {
    this.setState({ hasError: false, error: null, errorInfo: null });
  };

  override render() {
    if (this.state.hasError) {
      const { fallback: Fallback } = this.props;

      if (Fallback && this.state.error) {
        return <Fallback error={this.state.error} resetError={this.resetError} />;
      }

      return <DefaultErrorFallback error={this.state.error!} resetError={this.resetError} />;
    }

    return this.props.children;
  }
}

// ============================================================================
// 默认错误回退组件
// ============================================================================

interface DefaultErrorFallbackProps {
  error?: Error;
  resetError: () => void;
}

function DefaultErrorFallback({ error, resetError }: DefaultErrorFallbackProps) {
  return (
    <div className='min-h-screen flex items-center justify-center p-4'>
      <div className='max-w-md w-full space-y-4'>
        <div className='text-center'>
          <div className='text-6xl mb-4'>😵</div>
          <h1 className='text-2xl font-bold mb-2'>出现了一些问题</h1>
          <p className='text-muted-foreground mb-6'>
            很抱歉，应用遇到了意外错误。请尝试刷新页面或联系技术支持。
          </p>
        </div>

        {error && (
          <Alert>
            <AlertDescription>
              <details className='text-sm'>
                <summary className='cursor-pointer font-medium mb-2'>错误详情</summary>
                <pre className='whitespace-pre-wrap text-xs bg-muted p-2 rounded mt-2 overflow-auto'>
                  {error.message}
                  {error.stack && `\n\n${error.stack}`}
                </pre>
              </details>
            </AlertDescription>
          </Alert>
        )}

        <div className='flex space-x-2'>
          <Button onClick={resetError} className='flex-1'>
            重试
          </Button>
          <Button variant='outline' onClick={() => window.location.reload()} className='flex-1'>
            刷新页面
          </Button>
        </div>

        <div className='text-center'>
          <Button variant='ghost' size='sm' onClick={() => (window.location.href = '/')}>
            返回首页
          </Button>
        </div>
      </div>
    </div>
  );
}

// ============================================================================
// Hook版本的错误边界
// ============================================================================

export function useErrorHandler() {
  return (error: Error, errorInfo?: React.ErrorInfo) => {
    console.error('Error caught by useErrorHandler:', error, errorInfo);

    // 这里可以添加错误上报逻辑
    // 例如发送到错误监控服务
  };
}

// ============================================================================
// 异步错误处理Hook
// ============================================================================

export function useAsyncError() {
  const [, setError] = React.useState();

  return React.useCallback((error: Error) => {
    setError(() => {
      throw error;
    });
  }, []);
}

// ============================================================================
// 全局错误处理器
// ============================================================================

export function setupGlobalErrorHandlers() {
  // 处理未捕获的Promise拒绝
  window.addEventListener('unhandledrejection', event => {
    console.error('Unhandled promise rejection:', event.reason);

    // 忽略某些已知的外部错误
    if (
      event.reason?.message?.includes('MetaMask') ||
      event.reason?.message?.includes('chrome-extension') ||
      event.reason?.stack?.includes('chrome-extension')
    ) {
      event.preventDefault();
      return;
    }

    // 这里可以添加错误上报逻辑
  });

  // 处理全局JavaScript错误
  window.addEventListener('error', event => {
    console.error('Global error:', event.error);

    // 忽略某些已知的外部错误
    if (
      event.filename?.includes('chrome-extension') ||
      event.error?.stack?.includes('chrome-extension') ||
      event.message?.includes('MetaMask')
    ) {
      return;
    }

    // 这里可以添加错误上报逻辑
  });
}

// ============================================================================
// 错误边界HOC
// ============================================================================

export function withErrorBoundary<P extends object>(
  Component: React.ComponentType<P>,
  errorFallback?: React.ComponentType<{ error: Error; resetError: () => void }>
) {
  const WrappedComponent = (props: P) => (
    <ErrorBoundary fallback={errorFallback || DefaultErrorFallback}>
      <Component {...props} />
    </ErrorBoundary>
  );

  WrappedComponent.displayName = `withErrorBoundary(${Component.displayName || Component.name})`;

  return WrappedComponent;
}
