/**
 * OCTI智能评估系统 - 主布局组件
 *
 * 提供应用的主要布局结构，包括头部、侧边栏、主内容区域等
 */

'use client';

import React, { useState, useEffect } from 'react';
import { Header, MobileNav } from './header';
import { Sidebar } from './sidebar';
import { cn } from '@/lib/utils';

// ============================================================================
// 类型定义
// ============================================================================

interface MainLayoutProps {
  children: React.ReactNode;
  user?: {
    id: string;
    name: string;
    email: string;
    role: string;
    avatar?: string;
  };
  showSidebar?: boolean;
  showMobileNav?: boolean;
  className?: string;
}

// ============================================================================
// 主布局组件
// ============================================================================

export function MainLayout({
  children,
  user,
  showSidebar = true,
  showMobileNav = true,
  className,
}: MainLayoutProps) {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [_isMobile, setIsMobile] = useState(false);

  // 检测移动端
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 1024);
      if (window.innerWidth < 1024) {
        setSidebarCollapsed(true);
      }
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  const handleLogin = () => {
    // TODO: 实现登录逻辑
    console.log('Login clicked');
  };

  const handleLogout = () => {
    // TODO: 实现登出逻辑
    console.log('Logout clicked');
  };

  const toggleSidebar = () => {
    setSidebarCollapsed(!sidebarCollapsed);
  };

  return (
    <div className={cn('flex h-screen bg-background', className)}>
      {/* 侧边栏 */}
      {showSidebar && (
        <aside
          className={cn(
            'hidden lg:flex',
            sidebarCollapsed ? 'w-16' : 'w-64',
            'transition-all duration-300 ease-in-out'
          )}
        >
          <Sidebar isCollapsed={sidebarCollapsed} onToggle={toggleSidebar} className='h-full' />
        </aside>
      )}

      {/* 主内容区域 */}
      <div className='flex flex-1 flex-col overflow-hidden'>
        {/* 头部 */}
        <Header user={user} onLogin={handleLogin} onLogout={handleLogout} />

        {/* 主内容 */}
        <main className='flex-1 overflow-auto'>
          <div className='container mx-auto p-6 pb-20 lg:pb-6'>{children}</div>
        </main>
      </div>

      {/* 移动端底部导航 */}
      {showMobileNav && <MobileNav />}
    </div>
  );
}

// ============================================================================
// 页面容器组件
// ============================================================================

interface PageContainerProps {
  children: React.ReactNode;
  title?: string;
  description?: string;
  actions?: React.ReactNode;
  breadcrumb?: Array<{
    title: string;
    href?: string;
  }>;
  className?: string;
}

export function PageContainer({
  children,
  title,
  description,
  actions,
  breadcrumb,
  className,
}: PageContainerProps) {
  return (
    <div className={cn('space-y-6', className)}>
      {/* 页面头部 */}
      {(title || description || actions || breadcrumb) && (
        <div className='space-y-4'>
          {/* 面包屑导航 */}
          {breadcrumb && breadcrumb.length > 0 && (
            <nav className='flex items-center space-x-2 text-sm text-muted-foreground'>
              {breadcrumb.map((item, index) => (
                <React.Fragment key={index}>
                  {index > 0 && <span>/</span>}
                  {item.href ? (
                    <a href={item.href} className='hover:text-foreground transition-colors'>
                      {item.title}
                    </a>
                  ) : (
                    <span className='text-foreground'>{item.title}</span>
                  )}
                </React.Fragment>
              ))}
            </nav>
          )}

          {/* 页面标题和操作 */}
          <div className='flex items-center justify-between'>
            <div className='space-y-1'>
              {title && <h1 className='text-2xl font-bold tracking-tight'>{title}</h1>}
              {description && <p className='text-muted-foreground'>{description}</p>}
            </div>
            {actions && <div className='flex items-center space-x-2'>{actions}</div>}
          </div>
        </div>
      )}

      {/* 页面内容 */}
      <div>{children}</div>
    </div>
  );
}

// ============================================================================
// 卡片容器组件
// ============================================================================

interface CardContainerProps {
  children: React.ReactNode;
  title?: string;
  description?: string;
  actions?: React.ReactNode;
  className?: string;
}

export function CardContainer({
  children,
  title,
  description,
  actions,
  className,
}: CardContainerProps) {
  return (
    <div className={cn('rounded-lg border bg-card text-card-foreground shadow-sm', className)}>
      {/* 卡片头部 */}
      {(title || description || actions) && (
        <div className='flex items-center justify-between p-6 pb-4'>
          <div className='space-y-1'>
            {title && <h3 className='text-lg font-semibold'>{title}</h3>}
            {description && <p className='text-sm text-muted-foreground'>{description}</p>}
          </div>
          {actions && <div className='flex items-center space-x-2'>{actions}</div>}
        </div>
      )}

      {/* 卡片内容 */}
      <div className='p-6 pt-0'>{children}</div>
    </div>
  );
}

// ============================================================================
// 空状态组件
// ============================================================================

interface EmptyStateProps {
  icon?: string;
  title: string;
  description?: string;
  action?: React.ReactNode;
  className?: string;
}

export function EmptyState({
  icon = '📭',
  title,
  description,
  action,
  className,
}: EmptyStateProps) {
  return (
    <div className={cn('flex flex-col items-center justify-center py-12 text-center', className)}>
      <div className='text-6xl mb-4'>{icon}</div>
      <h3 className='text-lg font-semibold mb-2'>{title}</h3>
      {description && <p className='text-muted-foreground mb-6 max-w-md'>{description}</p>}
      {action && <div>{action}</div>}
    </div>
  );
}

// ============================================================================
// 加载状态组件
// ============================================================================

interface LoadingStateProps {
  title?: string;
  description?: string;
  className?: string;
}

export function LoadingState({ title = '加载中...', description, className }: LoadingStateProps) {
  return (
    <div className={cn('flex flex-col items-center justify-center py-12 text-center', className)}>
      <div className='animate-spin rounded-full h-8 w-8 border-b-2 border-primary mb-4'></div>
      <h3 className='text-lg font-semibold mb-2'>{title}</h3>
      {description && <p className='text-muted-foreground max-w-md'>{description}</p>}
    </div>
  );
}
