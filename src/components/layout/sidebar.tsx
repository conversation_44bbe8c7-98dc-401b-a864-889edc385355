/**
 * OCTI智能评估系统 - 侧边栏组件
 *
 * 提供导航菜单、快捷操作、系统信息等功能
 */

'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { cn } from '@/lib/utils';

// ============================================================================
// 类型定义
// ============================================================================

interface SidebarProps {
  className?: string;
  isCollapsed?: boolean;
  onToggle?: () => void;
}

interface NavItem {
  title: string;
  href: string;
  icon: string;
  badge?: string;
  children?: NavItem[];
}

// ============================================================================
// 导航菜单配置
// ============================================================================

const navItems: NavItem[] = [
  {
    title: '仪表板',
    href: '/dashboard',
    icon: '📊',
  },
  {
    title: '评估管理',
    href: '/assessments',
    icon: '🎯',
    badge: '3',
    children: [
      { title: '我的评估', href: '/assessments', icon: '📋' },
      { title: '创建评估', href: '/assessments/create', icon: '➕' },
      { title: '评估历史', href: '/assessments/history', icon: '📚' },
      { title: '评估模板', href: '/assessments/templates', icon: '📄' },
    ],
  },
  {
    title: '组织管理',
    href: '/organizations',
    icon: '🏢',
    children: [
      { title: '组织列表', href: '/organizations', icon: '📝' },
      { title: '添加组织', href: '/organizations/create', icon: '🆕' },
      { title: '组织画像', href: '/organizations/profiles', icon: '👥' },
      { title: '组织对比', href: '/organizations/comparison', icon: '⚖️' },
    ],
  },
  {
    title: '问卷系统',
    href: '/questionnaires',
    icon: '📝',
    children: [
      { title: '问卷库', href: '/questionnaires', icon: '📚' },
      { title: '问卷设计', href: '/questionnaires/design', icon: '🎨' },
      { title: '预设题目', href: '/questionnaires/presets', icon: '📋' },
      { title: 'AI生成', href: '/questionnaires/ai-generate', icon: '🤖' },
    ],
  },
  {
    title: '分析报告',
    href: '/reports',
    icon: '📊',
    children: [
      { title: '评估报告', href: '/reports', icon: '📄' },
      { title: '数据分析', href: '/reports/analytics', icon: '📈' },
      { title: '趋势分析', href: '/reports/trends', icon: '📉' },
      { title: '对比报告', href: '/reports/comparison', icon: '🔍' },
    ],
  },
  {
    title: '智能体配置',
    href: '/agents',
    icon: '🤖',
    children: [
      { title: '问卷设计师', href: '/agents/question-designer', icon: '🎨' },
      { title: '评估导师', href: '/agents/organization-mentor', icon: '👨‍🏫' },
      { title: '配置管理', href: '/agents/config', icon: '⚙️' },
      { title: '模型设置', href: '/agents/models', icon: '🧠' },
    ],
  },
];

const quickActions = [
  { title: '开始新评估', href: '/assessments/create', icon: '🚀', color: 'bg-blue-500' },
  { title: '添加组织', href: '/organizations/create', icon: '➕', color: 'bg-green-500' },
  { title: '查看报告', href: '/reports', icon: '📊', color: 'bg-purple-500' },
];

// ============================================================================
// 侧边栏组件
// ============================================================================

export function Sidebar({ className, isCollapsed = false, onToggle }: SidebarProps) {
  const pathname = usePathname();
  const [expandedItems, setExpandedItems] = useState<string[]>([]);

  const toggleExpanded = (href: string) => {
    setExpandedItems(prev =>
      prev.includes(href) ? prev.filter(item => item !== href) : [...prev, href]
    );
  };

  const isActive = (href: string) => {
    return pathname === href || pathname.startsWith(href + '/');
  };

  const isExpanded = (href: string) => {
    return expandedItems.includes(href) || pathname.startsWith(href + '/');
  };

  return (
    <div
      className={cn(
        'flex h-full flex-col border-r bg-background',
        isCollapsed ? 'w-16' : 'w-64',
        className
      )}
    >
      {/* 侧边栏头部 */}
      <div className='flex h-16 items-center border-b px-4'>
        {!isCollapsed && (
          <div className='flex items-center space-x-2'>
            <div className='flex h-8 w-8 items-center justify-center rounded-lg bg-gradient-to-r from-blue-600 to-green-600'>
              <span className='text-sm font-bold text-white'>O</span>
            </div>
            <div>
              <h2 className='text-lg font-semibold'>OCTI</h2>
              <p className='text-xs text-muted-foreground'>智能评估系统</p>
            </div>
          </div>
        )}
        <Button
          variant='ghost'
          size='sm'
          className={cn('ml-auto', isCollapsed && 'mx-auto')}
          onClick={onToggle}
        >
          {isCollapsed ? '→' : '←'}
        </Button>
      </div>

      {/* 快捷操作 */}
      {!isCollapsed && (
        <div className='p-4'>
          <h3 className='mb-2 text-sm font-medium text-muted-foreground'>快捷操作</h3>
          <div className='space-y-2'>
            {quickActions.map(action => (
              <Link
                key={action.href}
                href={action.href}
                className='flex items-center space-x-3 rounded-lg p-2 text-sm transition-colors hover:bg-accent'
              >
                <div
                  className={cn(
                    'flex h-6 w-6 items-center justify-center rounded text-white text-xs',
                    action.color
                  )}
                >
                  {action.icon}
                </div>
                <span>{action.title}</span>
              </Link>
            ))}
          </div>
        </div>
      )}

      {/* 导航菜单 */}
      <nav className='flex-1 space-y-1 p-4'>
        {!isCollapsed && (
          <h3 className='mb-2 text-sm font-medium text-muted-foreground'>导航菜单</h3>
        )}
        {navItems.map(item => (
          <div key={item.href}>
            <Link
              href={item.href}
              className={cn(
                'flex items-center space-x-3 rounded-lg px-3 py-2 text-sm transition-colors',
                isActive(item.href)
                  ? 'bg-primary text-primary-foreground'
                  : 'hover:bg-accent hover:text-accent-foreground'
              )}
              onClick={() => item.children && toggleExpanded(item.href)}
            >
              <span className='text-lg'>{item.icon}</span>
              {!isCollapsed && (
                <>
                  <span className='flex-1'>{item.title}</span>
                  {item.badge && (
                    <Badge variant='secondary' className='h-5 w-5 p-0 text-xs'>
                      {item.badge}
                    </Badge>
                  )}
                  {item.children && (
                    <span className='text-xs'>{isExpanded(item.href) ? '▼' : '▶'}</span>
                  )}
                </>
              )}
            </Link>

            {/* 子菜单 */}
            {item.children && !isCollapsed && isExpanded(item.href) && (
              <div className='ml-6 mt-1 space-y-1'>
                {item.children.map(child => (
                  <Link
                    key={child.href}
                    href={child.href}
                    className={cn(
                      'flex items-center space-x-3 rounded-lg px-3 py-2 text-sm transition-colors',
                      isActive(child.href)
                        ? 'bg-primary/10 text-primary'
                        : 'text-muted-foreground hover:bg-accent hover:text-accent-foreground'
                    )}
                  >
                    <span>{child.icon}</span>
                    <span>{child.title}</span>
                  </Link>
                ))}
              </div>
            )}
          </div>
        ))}
      </nav>

      {/* 系统状态 */}
      {!isCollapsed && (
        <div className='border-t p-4'>
          <h3 className='mb-2 text-sm font-medium text-muted-foreground'>系统状态</h3>
          <div className='space-y-3'>
            <div>
              <div className='flex items-center justify-between text-sm'>
                <span>API状态</span>
                <Badge variant='outline' className='text-green-600'>
                  正常
                </Badge>
              </div>
            </div>
            <div>
              <div className='flex items-center justify-between text-sm mb-1'>
                <span>存储使用</span>
                <span className='text-muted-foreground'>68%</span>
              </div>
              <Progress value={68} className='h-2' />
            </div>
            <div>
              <div className='flex items-center justify-between text-sm mb-1'>
                <span>本月评估</span>
                <span className='text-muted-foreground'>12/50</span>
              </div>
              <Progress value={24} className='h-2' />
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
