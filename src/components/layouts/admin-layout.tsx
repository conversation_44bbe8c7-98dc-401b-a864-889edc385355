/**
 * OCTI智能评估系统 - 管理端专用布局
 *
 * 为管理员提供完整的管理功能界面
 * 包含仪表板、评估管理、组织管理等功能
 */

'use client';

import React from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import {
  Brain,
  LayoutDashboard,
  FileText,
  Building,
  Users,
  BarChart3,
  Settings,
  Bot,
  LogOut,
  Menu,
  X,
  Clock,
  TrendingUp,
} from 'lucide-react';

interface AdminLayoutProps {
  children: React.ReactNode;
  user?: {
    name: string;
    email: string;
    avatar?: string;
  };
}

/**
 * 管理端导航菜单配置
 */
const ADMIN_MENU_ITEMS = [
  {
    title: '仪表板',
    href: '/admin',
    iconName: 'LayoutDashboard',
    description: '系统概览和关键指标',
  },
  {
    title: '评估管理',
    href: '/admin/assessments',
    iconName: 'FileText',
    description: '管理所有评估项目',
  },
  {
    title: '组织管理',
    href: '/admin/organizations',
    iconName: 'Building',
    description: '管理注册组织信息',
  },
  {
    title: '问卷管理',
    href: '/admin/questionnaires',
    iconName: 'Users',
    description: '管理问卷模板和配置',
  },
  {
    title: '分析报告',
    href: '/admin/reports',
    iconName: 'BarChart3',
    description: '查看分析报告和统计',
  },
  {
    title: '智能配置',
    href: '/admin/agents',
    iconName: 'Bot',
    description: '配置AI智能体参数',
  },
  {
    title: '系统设置',
    href: '/admin/settings',
    iconName: 'Settings',
    description: '系统配置和参数设置',
  },
];

/**
 * 图标映射
 */
const ICON_MAP = {
  LayoutDashboard,
  FileText,
  Building,
  Users,
  BarChart3,
  Bot,
  Settings,
  Clock,
  TrendingUp,
};

/**
 * 管理端专用布局组件
 */
export function AdminLayout({ children, user }: AdminLayoutProps) {
  const pathname = usePathname();
  const [sidebarOpen, setSidebarOpen] = React.useState(false);

  return (
    <div className='min-h-screen bg-gray-50'>
      {/* 侧边栏 */}
      <div
        className={`
        fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform transition-transform duration-300 ease-in-out
        ${sidebarOpen ? 'translate-x-0' : '-translate-x-full'}
        lg:translate-x-0 lg:static lg:inset-0
      `}
      >
        <div className='flex flex-col h-full'>
          {/* Logo区域 */}
          <div className='flex items-center justify-between h-16 px-4 border-b border-gray-200'>
            <Link href='/admin' className='flex items-center space-x-2'>
              <Brain className='h-8 w-8 text-primary' />
              <span className='text-xl font-bold text-gray-900'>OCTI管理</span>
            </Link>
            <button
              onClick={() => setSidebarOpen(false)}
              className='lg:hidden p-1 rounded-md hover:bg-gray-100'
            >
              <X className='h-5 w-5' />
            </button>
          </div>

          {/* 导航菜单 */}
          <nav className='flex-1 px-4 py-4 space-y-2'>
            {ADMIN_MENU_ITEMS.map(item => {
              const Icon = ICON_MAP[item.iconName as keyof typeof ICON_MAP];
              const isActive =
                pathname === item.href ||
                (item.href !== '/admin' && pathname.startsWith(item.href));

              return (
                <Link
                  key={item.href}
                  href={item.href}
                  className={`
                    flex items-center px-3 py-2 rounded-lg text-sm font-medium transition-colors
                    ${isActive ? 'bg-primary text-white' : 'text-gray-700 hover:bg-gray-100'}
                  `}
                  onClick={() => setSidebarOpen(false)}
                >
                  <Icon className='h-5 w-5 mr-3' />
                  <div>
                    <div>{item.title}</div>
                    <div className={`text-xs ${isActive ? 'text-white/80' : 'text-gray-500'}`}>
                      {item.description}
                    </div>
                  </div>
                </Link>
              );
            })}
          </nav>

          {/* 用户信息区域 */}
          {user && (
            <div className='p-4 border-t border-gray-200'>
              <div className='flex items-center space-x-3 mb-3'>
                <div className='w-8 h-8 bg-primary rounded-full flex items-center justify-center'>
                  <span className='text-white text-sm font-medium'>
                    {user.name.charAt(0).toUpperCase()}
                  </span>
                </div>
                <div className='flex-1 min-w-0'>
                  <div className='text-sm font-medium text-gray-900 truncate'>{user.name}</div>
                  <div className='text-xs text-gray-500 truncate'>{user.email}</div>
                </div>
              </div>
              <button className='flex items-center w-full px-3 py-2 text-sm text-gray-700 rounded-lg hover:bg-gray-100'>
                <LogOut className='h-4 w-4 mr-2' />
                退出登录
              </button>
            </div>
          )}
        </div>
      </div>

      {/* 主要内容区域 */}
      <div className='lg:pl-64'>
        {/* 顶部导航栏 */}
        <header className='bg-white shadow-sm border-b border-gray-200'>
          <div className='flex items-center justify-between h-16 px-4'>
            <button
              onClick={() => setSidebarOpen(true)}
              className='lg:hidden p-2 rounded-md hover:bg-gray-100'
            >
              <Menu className='h-5 w-5' />
            </button>

            <div className='flex items-center space-x-4'>
              {/* 面包屑导航 */}
              <nav className='flex items-center space-x-2 text-sm text-gray-600'>
                <Link href='/admin' className='hover:text-primary'>
                  管理后台
                </Link>
                {pathname !== '/admin' && (
                  <>
                    <span>/</span>
                    <span className='text-gray-900'>
                      {ADMIN_MENU_ITEMS.find(
                        item =>
                          pathname === item.href ||
                          (item.href !== '/admin' && pathname.startsWith(item.href))
                      )?.title || '页面'}
                    </span>
                  </>
                )}
              </nav>
            </div>

            <div className='flex items-center space-x-4'>
              {/* 快速操作按钮 */}
              <Link href='/' className='text-sm text-gray-600 hover:text-primary'>
                返回用户端
              </Link>
            </div>
          </div>
        </header>

        {/* 页面内容 */}
        <main className='flex-1 p-6'>{children}</main>
      </div>

      {/* 移动端遮罩层 */}
      {sidebarOpen && (
        <div
          className='fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden'
          onClick={() => setSidebarOpen(false)}
        />
      )}
    </div>
  );
}

/**
 * 管理端页面容器组件
 */
export function AdminPageContainer({
  children,
  title,
  description,
  actions,
  className = '',
}: {
  children: React.ReactNode;
  title?: string;
  description?: string;
  actions?: React.ReactNode;
  className?: string;
}) {
  return (
    <div className={`max-w-7xl mx-auto ${className}`}>
      {(title || description || actions) && (
        <div className='mb-6'>
          <div className='flex items-center justify-between'>
            <div>
              {title && <h1 className='text-2xl font-bold text-gray-900 mb-2'>{title}</h1>}
              {description && <p className='text-gray-600'>{description}</p>}
            </div>
            {actions && <div className='flex items-center space-x-3'>{actions}</div>}
          </div>
        </div>
      )}
      {children}
    </div>
  );
}

/**
 * 管理端统计卡片组件
 */
export function AdminStatsCard({
  title,
  value,
  change,
  changeType = 'neutral',
  iconName,
}: {
  title: string;
  value: string | number;
  change?: string;
  changeType?: 'positive' | 'negative' | 'neutral';
  iconName?: keyof typeof ICON_MAP;
}) {
  const Icon = iconName ? ICON_MAP[iconName] : null;

  return (
    <div className='bg-white rounded-lg shadow-sm border border-gray-200 p-6'>
      <div className='flex items-center justify-between'>
        <div>
          <p className='text-sm font-medium text-gray-600'>{title}</p>
          <p className='text-2xl font-bold text-gray-900 mt-1'>{value}</p>
          {change && (
            <p
              className={`text-sm mt-1 ${
                changeType === 'positive'
                  ? 'text-green-600'
                  : changeType === 'negative'
                    ? 'text-red-600'
                    : 'text-gray-600'
              }`}
            >
              {change}
            </p>
          )}
        </div>
        {Icon && (
          <div className='p-3 bg-primary/10 rounded-lg'>
            <Icon className='h-6 w-6 text-primary' />
          </div>
        )}
      </div>
    </div>
  );
}
