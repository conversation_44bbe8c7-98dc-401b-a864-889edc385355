/**
 * OCTI智能评估系统 - 进度指示器组件
 *
 * 显示问卷填写进度、时间估算、完成状态等信息
 */

'use client';

import React from 'react';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';

// ============================================================================
// 类型定义
// ============================================================================

interface ProgressIndicatorProps {
  currentQuestion: number;
  totalQuestions: number;
  answeredQuestions: number;
  requiredQuestions: number;
  answeredRequired: number;
  timeSpent?: number;
  estimatedTime?: number;
  showTimeInfo?: boolean;
  showDetailedStats?: boolean;
  className?: string;
}

// ============================================================================
// 进度指示器组件
// ============================================================================

export function ProgressIndicator({
  currentQuestion,
  totalQuestions,
  answeredQuestions,
  requiredQuestions,
  answeredRequired,
  timeSpent = 0,
  estimatedTime = 0,
  showTimeInfo = true,
  showDetailedStats = false,
  className,
}: ProgressIndicatorProps) {
  // 计算各种进度百分比
  const overallProgress = (answeredQuestions / totalQuestions) * 100;
  const requiredProgress = (answeredRequired / requiredQuestions) * 100;
  const currentProgress = ((currentQuestion + 1) / totalQuestions) * 100;

  // 时间相关计算 - 保留变量以备将来使用于详细时间显示
  /*
  const timeSpentMinutes = Math.floor(timeSpent / 60);
  const timeSpentSeconds = timeSpent % 60;
  const estimatedMinutes = Math.floor(estimatedTime / 60);
  const remainingMinutes = Math.floor(remainingTime / 60);
  */
  const remainingTime = Math.max(0, estimatedTime - timeSpent);

  // 进度状态
  const isOnTrack = timeSpent <= estimatedTime * 1.2; // 允许20%的时间缓冲
  const isNearCompletion = overallProgress >= 80;
  const allRequiredCompleted = answeredRequired >= requiredQuestions;

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    if (mins > 0) {
      return `${mins}分${secs > 0 ? `${secs}秒` : ''}`;
    }
    return `${secs}秒`;
  };

  return (
    <div className={cn('space-y-4', className)}>
      {/* 主要进度信息 */}
      <div className='space-y-3'>
        {/* 当前位置 */}
        <div className='flex items-center justify-between'>
          <div className='flex items-center space-x-2'>
            <h3 className='text-lg font-semibold'>
              问题 {currentQuestion + 1} / {totalQuestions}
            </h3>
            <Badge variant='outline'>{Math.round(currentProgress)}% 进度</Badge>
          </div>

          {showTimeInfo && estimatedTime > 0 && (
            <div className='flex items-center space-x-2 text-sm text-muted-foreground'>
              <span>⏱️</span>
              <span>
                已用时 {formatTime(timeSpent)}
                {remainingTime > 0 && ` / 预计剩余 ${formatTime(remainingTime)}`}
              </span>
            </div>
          )}
        </div>

        {/* 当前进度条 */}
        <div className='space-y-1'>
          <Progress value={currentProgress} className='h-2' />
          <div className='flex justify-between text-xs text-muted-foreground'>
            <span>当前进度</span>
            <span>
              {currentQuestion + 1}/{totalQuestions}
            </span>
          </div>
        </div>
      </div>

      {/* 详细统计信息 */}
      {showDetailedStats && (
        <div className='grid grid-cols-2 gap-4 p-4 bg-muted/50 rounded-lg'>
          {/* 整体完成情况 */}
          <div className='space-y-2'>
            <div className='flex items-center justify-between'>
              <span className='text-sm font-medium'>整体完成</span>
              <Badge variant={isNearCompletion ? 'default' : 'secondary'}>
                {answeredQuestions}/{totalQuestions}
              </Badge>
            </div>
            <Progress value={overallProgress} className='h-1.5' />
            <div className='text-xs text-muted-foreground'>
              {Math.round(overallProgress)}% 已完成
            </div>
          </div>

          {/* 必答题完成情况 */}
          <div className='space-y-2'>
            <div className='flex items-center justify-between'>
              <span className='text-sm font-medium'>必答题</span>
              <Badge variant={allRequiredCompleted ? 'default' : 'destructive'}>
                {answeredRequired}/{requiredQuestions}
              </Badge>
            </div>
            <Progress
              value={requiredProgress}
              className={cn('h-1.5', allRequiredCompleted ? 'bg-green-100' : 'bg-red-100')}
            />
            <div className='text-xs text-muted-foreground'>
              {Math.round(requiredProgress)}% 必答题已完成
            </div>
          </div>
        </div>
      )}

      {/* 时间和状态信息 */}
      {showTimeInfo && (
        <div className='flex items-center justify-between p-3 bg-muted/30 rounded-lg'>
          <div className='flex items-center space-x-4'>
            {/* 时间状态 */}
            <div className='flex items-center space-x-2'>
              <div
                className={cn('w-2 h-2 rounded-full', isOnTrack ? 'bg-green-500' : 'bg-yellow-500')}
              ></div>
              <span className='text-sm'>{isOnTrack ? '进度正常' : '稍微超时'}</span>
            </div>

            {/* 完成状态 */}
            <div className='flex items-center space-x-2'>
              <div
                className={cn(
                  'w-2 h-2 rounded-full',
                  allRequiredCompleted ? 'bg-green-500' : 'bg-red-500'
                )}
              ></div>
              <span className='text-sm'>
                {allRequiredCompleted ? '必答题已完成' : '还有必答题未完成'}
              </span>
            </div>
          </div>

          {/* 预计剩余时间 */}
          {remainingTime > 0 && (
            <div className='text-sm text-muted-foreground'>
              预计还需 {formatTime(remainingTime)}
            </div>
          )}
        </div>
      )}

      {/* 提示信息 */}
      <div className='space-y-2'>
        {!allRequiredCompleted && (
          <div className='flex items-center space-x-2 text-sm text-amber-600'>
            <span>⚠️</span>
            <span>还有 {requiredQuestions - answeredRequired} 道必答题未完成</span>
          </div>
        )}

        {isNearCompletion && allRequiredCompleted && (
          <div className='flex items-center space-x-2 text-sm text-green-600'>
            <span>🎉</span>
            <span>太棒了！您已经完成了大部分问题</span>
          </div>
        )}

        {overallProgress === 100 && (
          <div className='flex items-center space-x-2 text-sm text-green-600'>
            <span>✅</span>
            <span>所有问题都已完成，可以提交问卷了</span>
          </div>
        )}
      </div>
    </div>
  );
}

// ============================================================================
// 简化版进度指示器
// ============================================================================

interface SimpleProgressIndicatorProps {
  current: number;
  total: number;
  label?: string;
  showPercentage?: boolean;
  className?: string;
}

export function SimpleProgressIndicator({
  current,
  total,
  label = '进度',
  showPercentage = true,
  className,
}: SimpleProgressIndicatorProps) {
  const percentage = Math.round((current / total) * 100);

  return (
    <div className={cn('space-y-2', className)}>
      <div className='flex items-center justify-between text-sm'>
        <span className='font-medium'>{label}</span>
        <span className='text-muted-foreground'>
          {current}/{total}
          {showPercentage && ` (${percentage}%)`}
        </span>
      </div>
      <Progress value={percentage} className='h-2' />
    </div>
  );
}

// ============================================================================
// 圆形进度指示器
// ============================================================================

interface CircularProgressProps {
  percentage: number;
  size?: number;
  strokeWidth?: number;
  label?: string;
  className?: string;
}

export function CircularProgress({
  percentage,
  size = 60,
  strokeWidth = 4,
  label,
  className,
}: CircularProgressProps) {
  const radius = (size - strokeWidth) / 2;
  const circumference = radius * 2 * Math.PI;
  const strokeDasharray = `${circumference} ${circumference}`;
  const strokeDashoffset = circumference - (percentage / 100) * circumference;

  return (
    <div className={cn('flex flex-col items-center space-y-2', className)}>
      <div className='relative'>
        <svg width={size} height={size} className='transform -rotate-90'>
          {/* 背景圆 */}
          <circle
            cx={size / 2}
            cy={size / 2}
            r={radius}
            stroke='currentColor'
            strokeWidth={strokeWidth}
            fill='transparent'
            className='text-muted'
          />
          {/* 进度圆 */}
          <circle
            cx={size / 2}
            cy={size / 2}
            r={radius}
            stroke='currentColor'
            strokeWidth={strokeWidth}
            fill='transparent'
            strokeDasharray={strokeDasharray}
            strokeDashoffset={strokeDashoffset}
            strokeLinecap='round'
            className='text-primary transition-all duration-300 ease-in-out'
          />
        </svg>
        {/* 中心文字 */}
        <div className='absolute inset-0 flex items-center justify-center'>
          <span className='text-sm font-semibold'>{Math.round(percentage)}%</span>
        </div>
      </div>
      {label && <span className='text-xs text-muted-foreground text-center'>{label}</span>}
    </div>
  );
}
