'use client';

import React from 'react';
import { parseBlocks } from '@/lib/analysis/block-parser';
import { RenderBlock } from '@/components/ui/analysis-cards';

interface AdvancedAnalysisRendererProps {
  content: string;
  className?: string;
  type?: 'basic' | 'enhanced';
}

/**
 * 高级分析结果渲染器
 * 使用react-markdown + 混合处理技术，完美处理LLM返回的各种格式
 */
export function AdvancedAnalysisRenderer({
  content,
  className = '',
}: AdvancedAnalysisRendererProps) {
  /**
   * 卡片式2.0：按结构Block渲染
   */
  const renderCleanContent = () => {
    const blocks = parseBlocks(content);
    return blocks.map((b, i) => <RenderBlock key={i} block={b} />);
  };

  return (
    <div className={`advanced-analysis-content space-y-4 ${className}`}>{renderCleanContent()}</div>
  );
}
