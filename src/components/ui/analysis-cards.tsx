'use client';
/**
 * 卡片式2.0通用卡片组件
 * 路径：src/components/ui/analysis-cards.tsx
 */
import React from 'react';
import { CheckCircle, AlertTriangle, Lightbulb, Target, TrendingUp, Clock } from 'lucide-react';
import type {
  Block,
  TableBlock,
  ListBlock,
  FlowBlock,
  ParagraphBlock,
  SectionBlock,
  MermaidBlock,
} from '@/lib/analysis/block-parser';
import { SimpleMermaidRenderer } from '@/components/ui/mermaid-diagram';

/**
 * 轻量图标选择
 */
function IconFor(text: string) {
  const lower = text.toLowerCase();
  if (lower.includes('优势')) return <CheckCircle className='h-4 w-4 text-green-600' />;
  if (lower.includes('挑战') || lower.includes('问题'))
    return <AlertTriangle className='h-4 w-4 text-orange-600' />;
  if (lower.includes('建议')) return <Lightbulb className='h-4 w-4 text-blue-600' />;
  if (lower.includes('目标')) return <Target className='h-4 w-4 text-purple-600' />;
  if (lower.includes('发展') || lower.includes('提升'))
    return <TrendingUp className='h-4 w-4 text-indigo-600' />;
  return <Clock className='h-4 w-4 text-slate-600' />;
}

export function SectionCard({ block }: { block: SectionBlock }) {
  const cleanedTitle = cleanMarkdownSymbols(block.title);

  return (
    <div className='mt-8 mb-4 first:mt-0'>
      <div className='flex items-center gap-3 p-4 bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg shadow-sm'>
        <div className='w-8 h-8 rounded-full bg-blue-600 text-white flex items-center justify-center'>
          {IconFor(cleanedTitle)}
        </div>
        <h2 className='text-lg font-semibold text-gray-900 m-0'>{cleanedTitle}</h2>
      </div>
    </div>
  );
}

/**
 * 清理Markdown符号的函数
 */
function cleanMarkdownSymbols(text: string): string {
  return text
    // 清理代码块符号
    .replace(/```[\s\S]*?```/g, '') // 移除代码块
    .replace(/`([^`]+)`/g, '$1') // 移除行内代码符号

    // 清理粗体和斜体符号
    .replace(/\*\*([^*]+)\*\*/g, '$1') // 移除粗体符号 **text**
    .replace(/\*([^*]+)\*/g, '$1') // 移除斜体符号 *text*
    .replace(/__([^_]+)__/g, '$1') // 移除粗体符号 __text__
    .replace(/_([^_]+)_/g, '$1') // 移除斜体符号 _text_

    // 清理标题符号
    .replace(/^#{1,6}\s+/gm, '') // 移除标题符号 # ## ###

    // 清理列表符号（保留内容）
    .replace(/^\s*[-*+]\s+/gm, '• ') // 转换列表符号为统一的圆点
    .replace(/^\s*\d+\.\s+/gm, '') // 移除数字列表符号

    // 清理链接符号
    .replace(/\[([^\]]+)\]\([^)]+\)/g, '$1') // 移除链接符号，保留文本

    // 清理其他符号
    .replace(/~~([^~]+)~~/g, '$1') // 移除删除线符号
    .replace(/>\s+/gm, '') // 移除引用符号

    // 清理多余的空白
    .replace(/\n{3,}/g, '\n\n') // 多个换行合并为两个
    .replace(/^\s+|\s+$/gm, '') // 移除行首行尾空格
    .trim();
}

export function ParagraphCard({ block }: { block: ParagraphBlock }) {
  const cleanedText = cleanMarkdownSymbols(block.text);

  return (
    <div className='my-4 p-5 bg-white rounded-lg border border-gray-100 shadow-sm'>
      <p className='text-gray-700 leading-relaxed whitespace-pre-wrap break-words'>{cleanedText}</p>
    </div>
  );
}

export function ListCard({ block }: { block: ListBlock }) {
  return (
    <div className='my-4 p-5 bg-white rounded-lg border border-gray-100 shadow-sm'>
      <ul className='space-y-2 list-disc pl-5'>
        {block.items.map((it, idx) => (
          <li key={idx} className='text-gray-700 leading-relaxed'>
            {cleanMarkdownSymbols(it)}
          </li>
        ))}
      </ul>
    </div>
  );
}

export function FlowCard({ block }: { block: FlowBlock }) {
  return (
    <div className='my-4 p-5 bg-white rounded-lg border border-gray-100 shadow-sm'>
      <div className='flex flex-wrap items-center gap-2'>
        {block.steps.map((s, i) => (
          <React.Fragment key={i}>
            <span className='px-2 py-1 bg-indigo-50 text-indigo-700 rounded text-sm border border-indigo-200'>
              {cleanMarkdownSymbols(s)}
            </span>
            {i < block.steps.length - 1 && <span className='text-gray-400'>→</span>}
          </React.Fragment>
        ))}
      </div>
    </div>
  );
}

export function TableCard({ block }: { block: TableBlock }) {
  const headers = block.headers.length
    ? block.headers
    : Array.from(
        { length: Math.max(...block.rows.map(r => r.length), 0) },
        (_, i) => `字段${i + 1}`
      );
  return (
    <div className='my-6'>
      <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4'>
        {block.rows.map((row, idx) => (
          <div
            key={idx}
            className='p-5 bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 rounded-xl border border-blue-200 shadow-sm'
          >
            {row.map((cell, ci) => (
              <div key={ci} className='mb-3 last:mb-0'>
                <div className='text-xs font-medium text-slate-600 uppercase tracking-wide mb-1'>
                  {headers[ci] ?? `字段${ci + 1}`}
                </div>
                <div className='text-gray-800 leading-relaxed text-sm whitespace-pre-wrap break-words'>
                  {cell}
                </div>
              </div>
            ))}
          </div>
        ))}
      </div>
    </div>
  );
}

export function MermaidCard({ block }: { block: MermaidBlock }) {
  return (
    <div className='my-4'>
      <SimpleMermaidRenderer
        code={block.code}
        className='bg-white border border-gray-100 rounded-lg'
      />
    </div>
  );
}

export function RenderBlock({ block }: { block: Block }) {
  if (block.type === 'section') return <SectionCard block={block} />;
  if (block.type === 'paragraph') return <ParagraphCard block={block} />;
  if (block.type === 'list') return <ListCard block={block} />;
  if (block.type === 'flow') return <FlowCard block={block} />;
  if (block.type === 'table') return <TableCard block={block} />;
  if (block.type === 'mermaid') return <MermaidCard block={block} />;
  return null;
}
