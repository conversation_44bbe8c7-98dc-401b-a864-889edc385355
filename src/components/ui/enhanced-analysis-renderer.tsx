'use client';

import React from 'react';
import { SimpleMermaid<PERSON>enderer } from './mermaid-diagram';

interface EnhancedAnalysisRendererProps {
  content: string;
  className?: string;
}

/**
 * 增强版分析结果渲染器
 * 专门处理DeepSeek返回的复杂格式，包括HTML标记、特殊符号等
 */
export function EnhancedAnalysisRenderer({ content, className = '' }: EnhancedAnalysisRendererProps) {
  
  /**
   * 深度清理和格式化文本
   */
  const processContent = (text: string): string => {
    return text
      // 处理HTML标记
      .replace(/<br\s*\/?>/gi, '\n')
      .replace(/<\/?(p|div|span|strong|b|em|i)[^>]*>/gi, '')
      .replace(/<[^>]+>/g, '')

      // 保护Mermaid代码块（在其他处理之前）
      .replace(/(```mermaid[\s\S]*?```)/g, (match) => {
        return '\n' + match + '\n';
      })

      // 清理Markdown符号
      .replace(/```[\s\S]*?```/g, '') // 移除非mermaid代码块
      .replace(/`([^`]+)`/g, '$1') // 移除行内代码符号
      .replace(/\*\*([^*]+)\*\*/g, '$1') // 移除粗体符号 **text**
      .replace(/\*([^*]+)\*/g, '$1') // 移除斜体符号 *text*
      .replace(/__([^_]+)__/g, '$1') // 移除粗体符号 __text__
      .replace(/_([^_]+)_/g, '$1') // 移除斜体符号 _text_
      .replace(/^#{1,6}\s+/gm, '') // 移除标题符号
      .replace(/^\s*[-*+]\s+/gm, '• ') // 转换列表符号
      .replace(/^\s*\d+\.\s+/gm, '') // 移除数字列表符号
      .replace(/\[([^\]]+)\]\([^)]+\)/g, '$1') // 移除链接符号，保留文本
      .replace(/~~([^~]+)~~/g, '$1') // 移除删除线符号
      .replace(/>\s+/gm, '') // 移除引用符号

      // 处理特殊符号
      .replace(/→/g, ' → ')
      .replace(/：/g, '：')
      .replace(/\s*---\s*/g, '\n---\n')

      // 处理换行和空格（但保护代码块内容）
      .replace(/\n{3,}/g, '\n\n')
      .replace(/^\s+|\s+$/gm, '')
      .replace(/\s{2,}/g, ' ')
      .trim();
  };

  /**
   * 解析内容为结构化段落
   */
  const parseContent = (text: string) => {
    const cleanText = processContent(text);
    const paragraphs = cleanText.split('\n\n').filter(p => p.trim());
    
    return paragraphs.map((paragraph, index) => {
      const trimmed = paragraph.trim();
      if (!trimmed) return null;

      // 分隔线
      if (trimmed === '---') {
        return <hr key={index} className='my-6 border-gray-300' />;
      }

      // 中文数字标题（一、二、三、）
      if (trimmed.match(/^[一二三四五六七八九十]+、/)) {
        return (
          <div key={index} className='mt-8 mb-4'>
            <h3 className='text-xl font-bold text-gray-800 border-l-4 border-blue-500 pl-4 py-2 bg-blue-50 rounded-r'>
              {trimmed}
            </h3>
          </div>
        );
      }

      // 数字标题（1. 2. 3.）
      if (trimmed.match(/^\d+\.\s+/)) {
        return (
          <div key={index} className='mt-6 mb-3'>
            <h4 className='text-lg font-semibold text-gray-700 border-l-3 border-green-400 pl-3 py-1 bg-green-50 rounded-r'>
              {trimmed}
            </h4>
          </div>
        );
      }

      // 字母序列（A → B → C）
      if (trimmed.match(/[A-Z]\s*→\s*[A-Z]/)) {
        return (
          <div key={index} className='my-4 p-4 bg-purple-50 border border-purple-200 rounded-lg'>
            <div className='font-mono text-purple-700 text-center font-bold text-lg'>
              {trimmed}
            </div>
          </div>
        );
      }

      // Mermaid图表检测和渲染
      if (trimmed.includes('```mermaid') || isMermaidDiagram(trimmed)) {
        return renderMermaidDiagram(trimmed, index);
      }

      // 表格检测和渲染
      if (trimmed.includes('|') && trimmed.includes('---')) {
        return renderTable(trimmed, index);
      }

      // 列表项处理
      if (trimmed.includes('\n• ') || trimmed.startsWith('• ')) {
        const items = trimmed
          .split('\n')
          .filter(line => line.trim())
          .map(line => line.replace(/^[•\-\*]\s*/, '').trim())
          .filter(item => item);

        return (
          <ul key={index} className='my-4 space-y-2'>
            {items.map((item, itemIndex) => (
              <li key={itemIndex} className='flex items-start'>
                <span className='text-blue-500 mr-3 mt-1 flex-shrink-0 font-bold'>•</span>
                <span className='text-gray-700 leading-relaxed'>{item}</span>
              </li>
            ))}
          </ul>
        );
      }

      // 冒号分隔的描述项
      if (trimmed.includes('：') && !trimmed.match(/^[一二三四五六七八九十]+、/)) {
        const [label, ...contentParts] = trimmed.split('：');
        const content = contentParts.join('：').trim();
        
        if (!content) {
          return (
            <h5 key={index} className='font-semibold text-gray-800 mt-4 mb-2 text-sm uppercase tracking-wide bg-gray-100 px-2 py-1 rounded'>
              {label}
            </h5>
          );
        }
        
        return (
          <div key={index} className='my-4 p-4 bg-gray-50 rounded-lg border-l-4 border-gray-400'>
            <div className='font-semibold text-gray-800 mb-2'>{label}：</div>
            <div className='text-gray-700 leading-relaxed'>{content}</div>
          </div>
        );
      }

      // 普通段落
      return (
        <p key={index} className='text-gray-700 leading-relaxed my-4 text-justify'>
          {trimmed}
        </p>
      );
    }).filter(Boolean);
  };

  /**
   * 渲染表格
   */
  const renderTable = (tableText: string, index: number) => {
    const lines = tableText.trim().split('\n');
    if (lines.length < 3) return null;

    const headers = (lines[0] || '')
      .split('|')
      .map(h => h.trim())
      .filter(h => h);

    const rows = lines
      .slice(2)
      .map(line =>
        line
          .split('|')
          .map(cell => cell.trim())
          .filter(cell => cell)
      )
      .filter(row => row.length > 0);

    if (headers.length === 0 || rows.length === 0) return null;

    return (
      <div key={index} className='my-6 overflow-x-auto'>
        <table className='min-w-full bg-white border border-gray-200 rounded-lg shadow-sm'>
          <thead className='bg-gray-50'>
            <tr>
              {headers.map((header, headerIndex) => (
                <th
                  key={headerIndex}
                  className='px-4 py-3 text-left text-sm font-semibold text-gray-700 border-b border-gray-200'
                >
                  {header}
                </th>
              ))}
            </tr>
          </thead>
          <tbody>
            {rows.map((row, rowIndex) => (
              <tr key={rowIndex} className={rowIndex % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
                {row.map((cell, cellIndex) => (
                  <td
                    key={cellIndex}
                    className='px-4 py-3 text-sm text-gray-700 border-b border-gray-200'
                  >
                    {cell}
                  </td>
                ))}
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    );
  };

  /**
   * 检测是否为Mermaid图表格式
   */
  const isMermaidDiagram = (text: string): boolean => {
    // 检测常见的Mermaid关键词和语法
    const mermaidKeywords = [
      'graph', 'flowchart', 'sequenceDiagram', 'classDiagram',
      'stateDiagram', 'journey', 'gantt', 'pie', 'gitgraph',
      '-->', '--->', '-.->',  // 箭头语法
      'A[', 'B[', 'C[', 'D[', 'E[', 'F[', 'G[', 'H[', // 节点语法
      'LR', 'TD', 'TB', 'RL', 'BT' // 方向语法
    ];

    return mermaidKeywords.some(keyword => text.includes(keyword));
  };

  /**
   * 渲染Mermaid图表
   */
  const renderMermaidDiagram = (text: string, index: number) => {
    let diagramCode = text;

    // 如果包含```mermaid标记，提取代码
    const codeMatch = text.match(/```mermaid\s*([\s\S]*?)\s*```/);
    if (codeMatch && codeMatch[1]) {
      diagramCode = codeMatch[1].trim();
    } else {
      // 如果没有代码块标记，直接使用文本内容
      diagramCode = text.trim();
    }

    return (
      <div key={index} className='my-6'>
        <SimpleMermaidRenderer
          code={diagramCode}
          className='bg-white border border-gray-200 rounded-lg p-4 shadow-sm'
        />
      </div>
    );
  };

  return (
    <div className={`enhanced-analysis-content ${className}`}>
      {parseContent(content)}
    </div>
  );
}
