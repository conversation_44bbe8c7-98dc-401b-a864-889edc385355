'use client';

import React, { useEffect, useRef } from 'react';

interface MermaidDiagramProps {
  code: string;
  className?: string;
}

/**
 * Mermaid图表渲染组件
 * 用于渲染DeepSeek分析结果中的Mermaid图表
 */
export function MermaidDiagram({ code, className = '' }: MermaidDiagramProps) {
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    // 动态加载Mermaid库
    const loadMermaid = async () => {
      try {
        // 检查是否已经加载了Mermaid
        if (typeof window !== 'undefined' && (window as any).mermaid) {
          renderDiagram();
          return;
        }

        // 动态导入Mermaid
        const mermaidModule = await import('mermaid');
        const mermaid = mermaidModule.default;

        // 初始化Mermaid
        mermaid.initialize({
          startOnLoad: false,
          theme: 'default',
          securityLevel: 'loose',
          fontFamily: 'Arial, sans-serif',
          fontSize: 14,
          flowchart: {
            useMaxWidth: true,
            htmlLabels: true,
            curve: 'basis',
          },
        });

        renderDiagram();
      } catch (error) {
        console.warn('Mermaid加载失败，使用文本显示:', error);
        renderFallback();
      }
    };

    const renderDiagram = async () => {
      if (!containerRef.current) return;

      try {
        const mermaidModule = (window as any).mermaid || (await import('mermaid'));
        const mermaid = mermaidModule.default || mermaidModule;

        // 清空容器
        containerRef.current.innerHTML = '';

        // 生成唯一ID
        const id = `mermaid-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

        // 渲染图表
        const { svg } = await mermaid.render(id, code);

        if (containerRef.current) {
          containerRef.current.innerHTML = svg;
        }
      } catch (error) {
        console.warn('Mermaid渲染失败:', error);
        renderFallback();
      }
    };

    const renderFallback = () => {
      if (!containerRef.current) return;

      containerRef.current.innerHTML = `
        <div class="bg-gray-50 border border-gray-200 rounded-lg p-4">
          <div class="text-sm text-gray-600 mb-2">📊 流程图（文本格式）</div>
          <pre class="text-xs text-gray-700 whitespace-pre-wrap font-mono bg-white p-3 rounded border overflow-x-auto">${code}</pre>
          <div class="text-xs text-gray-400 mt-2">注：图表渲染需要Mermaid库支持</div>
        </div>
      `;
    };

    loadMermaid();
  }, [code]);

  return (
    <div className={`mermaid-container my-6 ${className}`}>
      <div ref={containerRef} className='text-center'>
        <div className='bg-gray-100 border border-gray-200 rounded-lg p-4 animate-pulse'>
          <div className='text-sm text-gray-500'>正在加载图表...</div>
        </div>
      </div>
    </div>
  );
}

/**
 * 简化版Mermaid渲染器（不依赖外部库）
 */
export function SimpleMermaidRenderer({ code, className = '' }: MermaidDiagramProps) {
  // 解析简单的流程图结构
  const parseSimpleFlowchart = (mermaidCode: string) => {
    const lines = mermaidCode.split('\n').filter(line => line.trim());
    const nodes: { [key: string]: string } = {};
    const edges: Array<{ from: string; to: string; label?: string }> = [];

    lines.forEach(line => {
      const trimmed = line.trim();

      // 解析节点定义 A[文本]
      const nodeMatch = trimmed.match(/^([A-Z]+)\[([^\]]+)\]$/);
      if (nodeMatch && nodeMatch[1] && nodeMatch[2]) {
        nodes[nodeMatch[1]] = nodeMatch[2];
        return;
      }

      // 解析连接 A --> B
      const edgeMatch = trimmed.match(/^([A-Z]+)\s*-->\s*([A-Z]+)$/);
      if (edgeMatch && edgeMatch[1] && edgeMatch[2]) {
        edges.push({ from: edgeMatch[1], to: edgeMatch[2] });
        return;
      }

      // 解析带标签的连接 A -->|标签| B
      const labeledEdgeMatch = trimmed.match(/^([A-Z]+)\s*-->\|([^|]+)\|\s*([A-Z]+)$/);
      if (labeledEdgeMatch && labeledEdgeMatch[1] && labeledEdgeMatch[3]) {
        const edge: { from: string; to: string; label?: string } = {
          from: labeledEdgeMatch[1],
          to: labeledEdgeMatch[3],
        };
        if (labeledEdgeMatch[2]) {
          edge.label = labeledEdgeMatch[2];
        }
        edges.push(edge);
      }
    });

    return { nodes, edges };
  };

  const { nodes, edges } = parseSimpleFlowchart(code);

  return (
    <div className={`simple-mermaid my-6 ${className}`}>
      <div className='bg-gradient-to-br from-blue-50 to-purple-50 border border-blue-200 rounded-lg p-6'>
        <div className='text-sm text-blue-600 mb-4 font-medium'>📊 流程图</div>

        {Object.keys(nodes).length > 0 ? (
          <div className='space-y-4'>
            {/* 渲染节点 */}
            <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3'>
              {Object.entries(nodes).map(([key, value]) => (
                <div
                  key={key}
                  className='bg-white border border-blue-300 rounded-lg p-3 text-center shadow-sm'
                >
                  <div className='text-xs text-blue-500 font-mono'>{key}</div>
                  <div className='text-sm text-gray-700 mt-1'>{value}</div>
                </div>
              ))}
            </div>

            {/* 渲染连接关系 */}
            {edges.length > 0 && (
              <div className='mt-4'>
                <div className='text-xs text-gray-600 mb-2'>流程关系：</div>
                <div className='space-y-1'>
                  {edges.map((edge, index) => (
                    <div key={index} className='text-sm text-gray-600 flex items-center'>
                      <span className='bg-blue-100 px-2 py-1 rounded text-xs'>
                        {nodes[edge.from] || edge.from}
                      </span>
                      <span className='mx-2 text-blue-500'>→</span>
                      {edge.label && (
                        <span className='text-xs text-blue-600 mx-2'>({edge.label})</span>
                      )}
                      <span className='bg-purple-100 px-2 py-1 rounded text-xs'>
                        {nodes[edge.to] || edge.to}
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        ) : (
          <pre className='text-xs text-gray-700 whitespace-pre-wrap font-mono bg-white p-3 rounded border overflow-x-auto'>
            {code}
          </pre>
        )}
      </div>
    </div>
  );
}
