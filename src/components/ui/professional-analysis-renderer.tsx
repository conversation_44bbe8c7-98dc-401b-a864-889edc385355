'use client';

import React from 'react';
import {
  CheckCircle,
  AlertTriangle,
  Target,
  TrendingUp,
  Lightbulb,
  ArrowRight,
  Star,
  Users,
  BarChart3,
} from 'lucide-react';
import { SimpleMermaidRenderer } from './mermaid-diagram';

interface ProfessionalAnalysisRendererProps {
  content: string;
  className?: string;
  type?: 'basic' | 'enhanced';
}

/**
 * 专业化分析结果渲染器
 * 智能处理LLM返回的内容，转换为专业的商务展示格式
 */
export function ProfessionalAnalysisRenderer({
  content,
  className = '',
}: ProfessionalAnalysisRendererProps) {
  /**
   * 智能内容预处理
   * 统一处理Markdown和HTML格式
   */
  const preprocessContent = (text: string): string => {
    return (
      text
        // 清理HTML标签但保留换行信息
        .replace(/<br\s*\/?>/gi, '\n')
        .replace(/<\/p>/gi, '\n\n')
        .replace(/<p[^>]*>/gi, '')
        .replace(/<\/?(div|span|strong|b|em|i)[^>]*>/gi, '')
        .replace(/<[^>]+>/g, '')

        // 标准化换行
        .replace(/\r\n/g, '\n')
        .replace(/\r/g, '\n')

        // 处理特殊符号
        .replace(/→/g, ' → ')
        .replace(/：/g, '：')

        // 清理多余空白
        .replace(/\n{3,}/g, '\n\n')
        .replace(/^\s+|\s+$/gm, '')
        .trim()
    );
  };

  /**
   * 智能段落分割
   * 基于语义和标点符号进行智能分割
   */
  const intelligentParagraphSplit = (text: string): string[] => {
    // 首先按双换行分割
    let paragraphs = text.split('\n\n');

    // 如果段落太少，尝试其他分割方式
    if (paragraphs.length < 3) {
      // 按句号+空格分割，但保持逻辑连贯性
      const sentences = text.split(/。\s+(?=[一二三四五六七八九十]|[A-Z]|[0-9]+\.|\*\*|###|##)/);
      if (sentences.length > paragraphs.length) {
        paragraphs = sentences.map(s => s.trim() + (s.endsWith('。') ? '' : '。')).filter(Boolean);
      }
    }

    return paragraphs.filter(p => p.trim().length > 0);
  };

  /**
   * 检测内容类型
   */
  const detectContentType = (text: string): string => {
    if (text.match(/^#{1,6}\s+/)) return 'heading';
    if (text.match(/^\*\*.*\*\*$/)) return 'highlight';
    if (text.match(/^[一二三四五六七八九十]+、/)) return 'chinese-number';
    if (text.match(/^\d+\.\s+/)) return 'numbered-list';
    if (text.match(/^[-*+]\s+/)) return 'bullet-list';
    if (text.includes('|') && text.includes('---')) return 'table';
    if (text.includes('```mermaid')) return 'mermaid';
    if (text.match(/^[A-Z]\s*→\s*[A-Z]/)) return 'flow';
    if (text.includes('：') && (text.split('：')[0] ?? '').length < 20) return 'definition';
    if (text.match(/^(优势|劣势|机会|威胁|建议|总结|结论)[:：]/)) return 'section';
    return 'paragraph';
  };

  /**
   * 获取内容对应的图标
   */
  const getContentIcon = (_type: string, text: string) => {
    if (text.includes('优势') || text.includes('强项'))
      return <CheckCircle className='h-4 w-4 text-green-600' />;
    if (text.includes('劣势') || text.includes('不足') || text.includes('挑战'))
      return <AlertTriangle className='h-4 w-4 text-orange-600' />;
    if (text.includes('建议') || text.includes('推荐'))
      return <Lightbulb className='h-4 w-4 text-blue-600' />;
    if (text.includes('目标') || text.includes('方向'))
      return <Target className='h-4 w-4 text-purple-600' />;
    if (text.includes('发展') || text.includes('提升'))
      return <TrendingUp className='h-4 w-4 text-indigo-600' />;
    if (text.includes('团队') || text.includes('人员'))
      return <Users className='h-4 w-4 text-cyan-600' />;
    if (text.includes('分析') || text.includes('评估'))
      return <BarChart3 className='h-4 w-4 text-gray-600' />;
    return <Star className='h-4 w-4 text-yellow-600' />;
  };

  /**
   * 渲染标题
   */
  const renderHeading = (text: string, index: number) => {
    // 移除Markdown标记
    const cleanText = text.replace(/^#{1,6}\s+/, '').replace(/\*\*/g, '');
    const level = (text.match(/^#{1,6}/) || [''])[0].length;

    const baseClasses = 'font-bold border-l-4 pl-4 py-2 rounded-r-lg mb-4';
    const levelClasses = {
      1: 'text-2xl text-gray-900 border-blue-600 bg-blue-50',
      2: 'text-xl text-gray-800 border-green-500 bg-green-50',
      3: 'text-lg text-gray-700 border-purple-500 bg-purple-50',
      4: 'text-base text-gray-700 border-orange-500 bg-orange-50',
      5: 'text-sm text-gray-600 border-gray-500 bg-gray-50',
      6: 'text-sm text-gray-600 border-gray-400 bg-gray-50',
    };

    const classes = `${baseClasses} ${levelClasses[level as keyof typeof levelClasses] || levelClasses[3]}`;

    return (
      <div key={index} className={classes}>
        <div className='flex items-center gap-2'>
          {getContentIcon('heading', cleanText)}
          <span>{cleanText}</span>
        </div>
      </div>
    );
  };

  /**
   * 渲染中文数字标题
   */
  const renderChineseNumberHeading = (text: string, index: number) => {
    return (
      <div key={index} className='mt-6 mb-4'>
        <div className='flex items-center gap-3 p-4 bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg'>
          <div className='flex-shrink-0 w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold'>
            {text.match(/^[一二三四五六七八九十]+/)?.[0]}
          </div>
          <h3 className='text-lg font-semibold text-gray-800'>
            {text.replace(/^[一二三四五六七八九十]+、\s*/, '')}
          </h3>
        </div>
      </div>
    );
  };

  /**
   * 渲染定义项
   */
  const renderDefinition = (text: string, index: number) => {
    const [label, ...contentParts] = text.split('：');
    const content = contentParts.join('：').trim();

    if (!content) {
      return (
        <div
          key={index}
          className='font-semibold text-gray-700 mt-4 mb-2 text-sm uppercase tracking-wide'
        >
          {label}
        </div>
      );
    }

    return (
      <div key={index} className='my-4 p-4 bg-white border border-gray-200 rounded-lg shadow-sm'>
        <div className='flex items-start gap-3'>
          {getContentIcon('definition', label ?? '')}
          <div className='flex-1'>
            <div className='font-semibold text-gray-800 mb-2'>{label}</div>
            <div className='text-gray-600 leading-relaxed'>{content}</div>
          </div>
        </div>
      </div>
    );
  };

  /**
   * 渲染列表项
   */
  const renderListItem = (text: string, index: number) => {
    const cleanText = text.replace(/^[-*+]\s+/, '').replace(/\*\*/g, '');

    return (
      <div key={index} className='flex items-start gap-3 my-2 p-3 bg-gray-50 rounded-lg'>
        <ArrowRight className='h-4 w-4 text-blue-600 mt-0.5 flex-shrink-0' />
        <span className='text-gray-700 leading-relaxed'>{cleanText}</span>
      </div>
    );
  };

  /**
   * 渲染高亮文本
   */
  const renderHighlight = (text: string, index: number) => {
    const cleanText = text.replace(/\*\*/g, '');

    return (
      <div key={index} className='my-3 p-4 bg-yellow-50 border border-yellow-200 rounded-lg'>
        <div className='flex items-center gap-2'>
          <Star className='h-4 w-4 text-yellow-600' />
          <span className='font-semibold text-yellow-800'>{cleanText}</span>
        </div>
      </div>
    );
  };

  /**
   * 渲染流程图
   */
  const renderFlow = (text: string, index: number) => {
    return (
      <div key={index} className='my-4 p-4 bg-purple-50 border border-purple-200 rounded-lg'>
        <div className='font-mono text-purple-700 text-center font-bold text-lg'>{text}</div>
      </div>
    );
  };

  /**
   * 渲染普通段落
   */
  const renderParagraph = (text: string, index: number) => {
    // 处理段落中的粗体文本
    const processedText = text.replace(
      /\*\*(.*?)\*\*/g,
      '<strong class="font-semibold text-gray-800">$1</strong>'
    );

    return (
      <div
        key={index}
        className='text-gray-600 leading-relaxed my-4 p-4 bg-white rounded-lg border border-gray-100'
        dangerouslySetInnerHTML={{ __html: processedText }}
      />
    );
  };

  /**
   * 主渲染函数
   */
  const renderContent = () => {
    const processedContent = preprocessContent(content);
    const paragraphs = intelligentParagraphSplit(processedContent);

    return paragraphs
      .map((paragraph, index) => {
        const trimmed = paragraph.trim();
        if (!trimmed) return null;

        const contentType = detectContentType(trimmed);

        switch (contentType) {
          case 'heading':
            return renderHeading(trimmed, index);
          case 'chinese-number':
            return renderChineseNumberHeading(trimmed, index);
          case 'definition':
            return renderDefinition(trimmed, index);
          case 'bullet-list':
            return renderListItem(trimmed, index);
          case 'highlight':
            return renderHighlight(trimmed, index);
          case 'flow':
            return renderFlow(trimmed, index);
          case 'mermaid':
            // Mermaid图表处理保持原有逻辑
            const codeMatch = trimmed.match(/```mermaid\s*([\s\S]*?)\s*```/);
            if (codeMatch && codeMatch[1]) {
              return (
                <div key={index} className='my-6'>
                  <SimpleMermaidRenderer
                    code={codeMatch[1].trim()}
                    className='bg-white border border-gray-200 rounded-lg p-4 shadow-sm'
                  />
                </div>
              );
            }
            return null;
          default:
            return renderParagraph(trimmed, index);
        }
      })
      .filter(Boolean);
  };

  return (
    <div className={`professional-analysis-content space-y-4 ${className}`}>{renderContent()}</div>
  );
}
