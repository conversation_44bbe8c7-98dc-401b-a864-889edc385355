/**
 * OCTI智能评估系统 - 常量定义
 *
 * 定义系统中使用的所有常量
 */

// ============================================================================
// 系统常量
// ============================================================================

/**
 * 应用基本信息
 */
export const APP_INFO = {
  NAME: 'OCTI智能评估系统',
  VERSION: '4.0.0',
  DESCRIPTION: '基于配置驱动和智能体模块化的组织能力评估平台',
  AUTHOR: 'OCTI Team',
  WEBSITE: 'https://octi.example.com',
} as const;

/**
 * API相关常量
 */
export const API = {
  BASE_URL: '/api/v1',
  TIMEOUT: 30000, // 30秒
  RETRY_ATTEMPTS: 3,
  RATE_LIMIT: {
    WINDOW_MS: 15 * 60 * 1000, // 15分钟
    MAX_REQUESTS: 100,
  },
} as const;

/**
 * 数据库相关常量
 */
export const DATABASE = {
  MAX_CONNECTIONS: 20,
  CONNECTION_TIMEOUT: 10000, // 10秒
  QUERY_TIMEOUT: 30000, // 30秒
} as const;

/**
 * 缓存相关常量
 */
export const CACHE = {
  DEFAULT_TTL: 3600, // 1小时
  SESSION_TTL: 86400, // 24小时
  CONFIG_TTL: 300, // 5分钟
  ANALYSIS_TTL: 7200, // 2小时
} as const;

// ============================================================================
// 业务常量
// ============================================================================

/**
 * 组织类型标签
 */
export const ORGANIZATION_TYPE_LABELS = {
  ngo: '非政府组织',
  foundation: '基金会',
  social_enterprise: '社会企业',
  charity: '慈善组织',
  community_org: '社区组织',
  other: '其他',
} as const;

/**
 * 组织规模标签
 */
export const ORGANIZATION_SCALE_LABELS = {
  small: '小型（1-10人）',
  medium: '中型（11-50人）',
  large: '大型（51-200人）',
  extra_large: '超大型（200人以上）',
} as const;

/**
 * 发展阶段标签
 */
export const DEVELOPMENT_STAGE_LABELS = {
  startup: '初创期',
  growth: '成长期',
  mature: '成熟期',
  transformation: '转型期',
} as const;

/**
 * 评估状态标签
 */
export const ASSESSMENT_STATUS_LABELS = {
  draft: '草稿',
  profile_collection: '画像收集中',
  questionnaire_generation: '问卷生成中',
  in_progress: '进行中',
  analysis: '分析中',
  completed: '已完成',
  archived: '已归档',
} as const;

/**
 * 评估类型标签
 */
export const ASSESSMENT_TYPE_LABELS = {
  standard: '标准版',
  professional: '专业版',
} as const;

/**
 * 能力维度标签
 */
export const CAPABILITY_DIMENSION_LABELS = {
  governance: '治理能力',
  strategy: '战略能力',
  operations: '运营能力',
  finance: '财务能力',
  human_resources: '人力资源能力',
  technology: '技术能力',
  partnerships: '合作能力',
  impact: '影响力',
} as const;

/**
 * 能力等级标签
 */
export const CAPABILITY_LEVEL_LABELS = {
  beginner: '初级',
  developing: '发展中',
  proficient: '熟练',
  advanced: '高级',
  expert: '专家',
} as const;

/**
 * 能力等级分数范围
 */
export const CAPABILITY_LEVEL_RANGES = {
  beginner: [0, 20],
  developing: [21, 40],
  proficient: [41, 60],
  advanced: [61, 80],
  expert: [81, 100],
} as const;

/**
 * 问题类型标签
 */
export const QUESTION_TYPE_LABELS = {
  single_choice: '单选题',
  multiple_choice: '多选题',
  scale: '量表题',
  text: '文本题',
  boolean: '是非题',
} as const;

/**
 * 问题来源标签
 */
export const QUESTION_SOURCE_LABELS = {
  preset: '预设题目',
  ai_generated: 'AI生成题目',
} as const;

// ============================================================================
// 问卷配置常量
// ============================================================================

/**
 * 问卷配置
 */
export const QUESTIONNAIRE_CONFIG = {
  TOTAL_QUESTIONS: 60,
  PRESET_QUESTIONS: 32,
  AI_GENERATED_QUESTIONS: 28,
  PROFILE_QUESTIONS: 10,
  MAX_OPTIONS_PER_QUESTION: 8,
  MIN_OPTIONS_PER_QUESTION: 2,
  ESTIMATED_TIME_PER_QUESTION: 1.5, // 分钟
} as const;

/**
 * 量表配置
 */
export const SCALE_CONFIG = {
  MIN_VALUE: 1,
  MAX_VALUE: 5,
  LABELS: {
    1: '完全不同意',
    2: '不同意',
    3: '中立',
    4: '同意',
    5: '完全同意',
  },
} as const;

// ============================================================================
// AI模型配置常量
// ============================================================================

/**
 * LLM模型配置
 */
export const LLM_CONFIG = {
  MINIMAX: {
    MODEL: 'abab6.5s-chat',
    MAX_TOKENS: 4000,
    TEMPERATURE: 0.7,
    TOP_P: 0.9,
  },
  DEEPSEEK: {
    MODEL: 'deepseek-chat',
    MAX_TOKENS: 4000,
    TEMPERATURE: 0.7,
    TOP_P: 0.9,
  },
  TIMEOUT: 300000, // 5分钟（与LLM客户端保持一致）
  RETRY_ATTEMPTS: 3,
  RETRY_DELAY: 1000, // 1秒
} as const;

/**
 * 智能体配置
 */
export const AGENT_CONFIG = {
  QUESTION_DESIGNER: {
    NAME: '问卷设计师',
    VERSION: '1.0.0',
    MAX_QUESTIONS_PER_BATCH: 10,
    QUALITY_THRESHOLD: 0.8,
  },
  ORGANIZATION_MENTOR: {
    NAME: '组织评估导师',
    VERSION: '1.0.0',
    ANALYSIS_DEPTH: 'comprehensive',
    CONFIDENCE_THRESHOLD: 0.7,
  },
} as const;

// ============================================================================
// 文件和存储常量
// ============================================================================

/**
 * 文件配置
 */
export const FILE_CONFIG = {
  MAX_FILE_SIZE: 10 * 1024 * 1024, // 10MB
  ALLOWED_TYPES: ['pdf', 'doc', 'docx', 'txt', 'csv', 'xlsx'],
  UPLOAD_PATH: '/uploads',
  REPORT_PATH: '/reports',
} as const;

/**
 * 报告配置
 */
export const REPORT_CONFIG = {
  FORMATS: ['pdf', 'html', 'json'],
  DEFAULT_FORMAT: 'pdf',
  EXPIRY_DAYS: 30,
  MAX_SIZE: 50 * 1024 * 1024, // 50MB
} as const;

// ============================================================================
// 安全相关常量
// ============================================================================

/**
 * 安全配置
 */
export const SECURITY_CONFIG = {
  JWT: {
    EXPIRES_IN: '24h',
    REFRESH_EXPIRES_IN: '7d',
    ALGORITHM: 'HS256',
  },
  PASSWORD: {
    MIN_LENGTH: 8,
    REQUIRE_UPPERCASE: true,
    REQUIRE_LOWERCASE: true,
    REQUIRE_NUMBERS: true,
    REQUIRE_SYMBOLS: true,
  },
  RATE_LIMIT: {
    LOGIN_ATTEMPTS: 5,
    LOCKOUT_DURATION: 15 * 60 * 1000, // 15分钟
  },
  ENCRYPTION: {
    ALGORITHM: 'aes-256-gcm',
    KEY_LENGTH: 32,
    IV_LENGTH: 16,
  },
} as const;

// ============================================================================
// 验证规则常量
// ============================================================================

/**
 * 验证规则
 */
export const VALIDATION_RULES = {
  ORGANIZATION_NAME: {
    MIN_LENGTH: 2,
    MAX_LENGTH: 100,
  },
  DESCRIPTION: {
    MIN_LENGTH: 10,
    MAX_LENGTH: 1000,
  },
  EMAIL: {
    PATTERN: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  },
  PHONE: {
    PATTERN: /^1[3-9]\d{9}$/,
  },
  URL: {
    PATTERN: /^https?:\/\/.+/,
  },
} as const;

// ============================================================================
// 颜色和主题常量
// ============================================================================

/**
 * 主题颜色
 */
export const THEME_COLORS = {
  PRIMARY: '#0ea5e9',
  SECONDARY: '#64748b',
  SUCCESS: '#22c55e',
  WARNING: '#f59e0b',
  ERROR: '#ef4444',
  INFO: '#3b82f6',
} as const;

/**
 * 能力维度颜色映射
 */
export const CAPABILITY_COLORS = {
  governance: '#8b5cf6',
  strategy: '#06b6d4',
  operations: '#10b981',
  finance: '#f59e0b',
  human_resources: '#ef4444',
  technology: '#6366f1',
  partnerships: '#ec4899',
  impact: '#84cc16',
} as const;

// ============================================================================
// 错误代码常量
// ============================================================================

/**
 * 错误代码
 */
export const ERROR_CODES = {
  // 通用错误
  UNKNOWN_ERROR: 'UNKNOWN_ERROR',
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  PERMISSION_DENIED: 'PERMISSION_DENIED',
  RESOURCE_NOT_FOUND: 'RESOURCE_NOT_FOUND',

  // 认证错误
  UNAUTHORIZED: 'UNAUTHORIZED',
  TOKEN_EXPIRED: 'TOKEN_EXPIRED',
  INVALID_CREDENTIALS: 'INVALID_CREDENTIALS',

  // 业务错误
  ORGANIZATION_NOT_FOUND: 'ORGANIZATION_NOT_FOUND',
  ASSESSMENT_NOT_FOUND: 'ASSESSMENT_NOT_FOUND',
  QUESTIONNAIRE_NOT_FOUND: 'QUESTIONNAIRE_NOT_FOUND',
  INVALID_ASSESSMENT_STATUS: 'INVALID_ASSESSMENT_STATUS',

  // AI服务错误
  LLM_SERVICE_ERROR: 'LLM_SERVICE_ERROR',
  AGENT_EXECUTION_ERROR: 'AGENT_EXECUTION_ERROR',
  ANALYSIS_FAILED: 'ANALYSIS_FAILED',

  // 系统错误
  DATABASE_ERROR: 'DATABASE_ERROR',
  CACHE_ERROR: 'CACHE_ERROR',
  FILE_UPLOAD_ERROR: 'FILE_UPLOAD_ERROR',
} as const;

// ============================================================================
// 导出所有常量
// ============================================================================

export * from './routes';
export * from './messages';
