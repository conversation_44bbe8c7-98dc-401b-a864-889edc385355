/**
 * OCTI智能评估系统 - 路由常量
 *
 * 定义系统中所有的路由路径
 */

// ============================================================================
// 前端路由常量
// ============================================================================

/**
 * 公共页面路由
 */
export const PUBLIC_ROUTES = {
  HOME: '/',
  ABOUT: '/about',
  CONTACT: '/contact',
  DEMO: '/demo',
  LOGIN: '/auth/login',
  REGISTER: '/auth/register',
  FORGOT_PASSWORD: '/auth/forgot-password',
  RESET_PASSWORD: '/auth/reset-password',
} as const;

/**
 * 用户端路由（公开访问）
 */
export const USER_ROUTES = {
  HOME: '/',
  ASSESSMENT_START: '/assessment/start',
  ASSESSMENT_QUESTIONNAIRE: '/assessment/questionnaire',
  ASSESSMENT_RESULTS: '/assessment/results',
  HELP: '/help',
  PRIVACY: '/privacy',
  CONTACT: '/contact',
} as const;

/**
 * 管理端路由（需要管理员权限）
 */
export const ADMIN_ROUTES = {
  DASHBOARD: '/admin',
  ASSESSMENTS: '/admin/assessments',
  ORGANIZATIONS: '/admin/organizations',
  QUESTIONNAIRES: '/admin/questionnaires',
  REPORTS: '/admin/reports',
  AGENTS: '/admin/agents',
  SETTINGS: '/admin/settings',
} as const;

/**
 * 认证后页面路由
 */
export const PROTECTED_ROUTES = {
  DASHBOARD: '/dashboard',
  PROFILE: '/profile',
  SETTINGS: '/settings',
} as const;

/**
 * 组织管理路由
 */
export const ORGANIZATION_ROUTES = {
  LIST: '/organizations',
  CREATE: '/organizations/create',
  DETAIL: '/organizations/[id]',
  EDIT: '/organizations/[id]/edit',
  DELETE: '/organizations/[id]/delete',
} as const;

/**
 * 评估相关路由
 */
export const ASSESSMENT_ROUTES = {
  LIST: '/assessments',
  CREATE: '/assessments/create',
  START: '/assessments/create',
  PROFILE: '/assessments/[id]/profile',
  QUESTIONNAIRE: '/assessments/[id]/questionnaire',
  PROGRESS: '/assessments/[id]/progress',
  RESULTS: '/assessments/[id]/results',
  DETAIL: '/assessments/[id]',
  EDIT: '/assessments/[id]/edit',
  DELETE: '/assessments/[id]/delete',
  REPORT: '/assessments/[id]/report',
} as const;

/**
 * 问卷相关路由
 */
export const QUESTIONNAIRE_ROUTES = {
  LIST: '/questionnaires',
  CREATE: '/questionnaires/create',
  DETAIL: '/questionnaires/[id]',
  EDIT: '/questionnaires/[id]/edit',
  PREVIEW: '/questionnaires/[id]/preview',
  FILL: '/questionnaires/[id]/fill',
} as const;

/**
 * 报告相关路由
 */
export const REPORT_ROUTES = {
  LIST: '/reports',
  DETAIL: '/reports/[id]',
  DOWNLOAD: '/reports/[id]/download',
  SHARE: '/reports/[id]/share',
} as const;

// ============================================================================
// API路由常量
// ============================================================================

/**
 * API基础路径
 */
export const API_BASE = '/api/v1' as const;

/**
 * 认证API路由
 */
export const AUTH_API_ROUTES = {
  LOGIN: `${API_BASE}/auth/login`,
  REGISTER: `${API_BASE}/auth/register`,
  LOGOUT: `${API_BASE}/auth/logout`,
  REFRESH: `${API_BASE}/auth/refresh`,
  FORGOT_PASSWORD: `${API_BASE}/auth/forgot-password`,
  RESET_PASSWORD: `${API_BASE}/auth/reset-password`,
  VERIFY_EMAIL: `${API_BASE}/auth/verify-email`,
  PROFILE: `${API_BASE}/auth/profile`,
} as const;

/**
 * 组织API路由
 */
export const ORGANIZATION_API_ROUTES = {
  LIST: `${API_BASE}/organizations`,
  CREATE: `${API_BASE}/organizations`,
  DETAIL: `${API_BASE}/organizations/[id]`,
  UPDATE: `${API_BASE}/organizations/[id]`,
  DELETE: `${API_BASE}/organizations/[id]`,
  PROFILE: `${API_BASE}/organizations/[id]/profile`,
  ASSESSMENTS: `${API_BASE}/organizations/[id]/assessments`,
} as const;

/**
 * 评估API路由
 */
export const ASSESSMENT_API_ROUTES = {
  LIST: `${API_BASE}/assessments`,
  CREATE: `${API_BASE}/assessments`,
  DETAIL: `${API_BASE}/assessments/[id]`,
  UPDATE: `${API_BASE}/assessments/[id]`,
  DELETE: `${API_BASE}/assessments/[id]`,
  START: `${API_BASE}/assessments/[id]/start`,
  SUBMIT: `${API_BASE}/assessments/[id]/submit`,
  ANALYZE: `${API_BASE}/assessments/[id]/analyze`,
  RESULTS: `${API_BASE}/assessments/[id]/results`,
  REPORT: `${API_BASE}/assessments/[id]/report`,
} as const;

/**
 * 问卷API路由
 */
export const QUESTIONNAIRE_API_ROUTES = {
  LIST: `${API_BASE}/questionnaires`,
  CREATE: `${API_BASE}/questionnaires`,
  DETAIL: `${API_BASE}/questionnaires/[id]`,
  UPDATE: `${API_BASE}/questionnaires/[id]`,
  DELETE: `${API_BASE}/questionnaires/[id]`,
  GENERATE: `${API_BASE}/questionnaires/generate`,
  QUESTIONS: `${API_BASE}/questionnaires/[id]/questions`,
  RESPONSES: `${API_BASE}/questionnaires/[id]/responses`,
} as const;

/**
 * 问题API路由
 */
export const QUESTION_API_ROUTES = {
  LIST: `${API_BASE}/questions`,
  CREATE: `${API_BASE}/questions`,
  DETAIL: `${API_BASE}/questions/[id]`,
  UPDATE: `${API_BASE}/questions/[id]`,
  DELETE: `${API_BASE}/questions/[id]`,
  PRESET: `${API_BASE}/questions/preset`,
  GENERATE: `${API_BASE}/questions/generate`,
} as const;

/**
 * 回答API路由
 */
export const RESPONSE_API_ROUTES = {
  LIST: `${API_BASE}/responses`,
  CREATE: `${API_BASE}/responses`,
  DETAIL: `${API_BASE}/responses/[id]`,
  UPDATE: `${API_BASE}/responses/[id]`,
  DELETE: `${API_BASE}/responses/[id]`,
  BATCH_CREATE: `${API_BASE}/responses/batch`,
  BATCH_UPDATE: `${API_BASE}/responses/batch`,
} as const;

/**
 * 分析API路由
 */
export const ANALYSIS_API_ROUTES = {
  ANALYZE: `${API_BASE}/analysis/analyze`,
  RESULTS: `${API_BASE}/analysis/[id]/results`,
  COMPARE: `${API_BASE}/analysis/compare`,
  TRENDS: `${API_BASE}/analysis/trends`,
  INSIGHTS: `${API_BASE}/analysis/insights`,
} as const;

/**
 * 报告API路由
 */
export const REPORT_API_ROUTES = {
  LIST: `${API_BASE}/reports`,
  CREATE: `${API_BASE}/reports`,
  DETAIL: `${API_BASE}/reports/[id]`,
  UPDATE: `${API_BASE}/reports/[id]`,
  DELETE: `${API_BASE}/reports/[id]`,
  DOWNLOAD: `${API_BASE}/reports/[id]/download`,
  SHARE: `${API_BASE}/reports/[id]/share`,
  GENERATE: `${API_BASE}/reports/generate`,
} as const;

/**
 * 智能体API路由
 */
export const AGENT_API_ROUTES = {
  LIST: `${API_BASE}/agents`,
  DETAIL: `${API_BASE}/agents/[id]`,
  EXECUTE: `${API_BASE}/agents/[id]/execute`,
  CONFIG: `${API_BASE}/agents/[id]/config`,
  LOGS: `${API_BASE}/agents/[id]/logs`,
  QUESTION_DESIGNER: `${API_BASE}/agents/question-designer`,
  ORGANIZATION_MENTOR: `${API_BASE}/agents/organization-mentor`,
} as const;

/**
 * 配置API路由
 */
export const CONFIG_API_ROUTES = {
  LIST: `${API_BASE}/configs`,
  DETAIL: `${API_BASE}/configs/[key]`,
  UPDATE: `${API_BASE}/configs/[key]`,
  SYSTEM: `${API_BASE}/configs/system`,
  LLM: `${API_BASE}/configs/llm`,
  AGENTS: `${API_BASE}/configs/agents`,
} as const;

/**
 * 文件API路由
 */
export const FILE_API_ROUTES = {
  UPLOAD: `${API_BASE}/files/upload`,
  DOWNLOAD: `${API_BASE}/files/[id]/download`,
  DELETE: `${API_BASE}/files/[id]`,
  LIST: `${API_BASE}/files`,
} as const;

/**
 * 统计API路由
 */
export const ANALYTICS_API_ROUTES = {
  DASHBOARD: `${API_BASE}/analytics/dashboard`,
  ORGANIZATIONS: `${API_BASE}/analytics/organizations`,
  ASSESSMENTS: `${API_BASE}/analytics/assessments`,
  USAGE: `${API_BASE}/analytics/usage`,
  PERFORMANCE: `${API_BASE}/analytics/performance`,
} as const;

/**
 * 管理员API路由
 */
export const ADMIN_API_ROUTES = {
  USERS: `${API_BASE}/admin/users`,
  ORGANIZATIONS: `${API_BASE}/admin/organizations`,
  ASSESSMENTS: `${API_BASE}/admin/assessments`,
  SYSTEM: `${API_BASE}/admin/system`,
  LOGS: `${API_BASE}/admin/logs`,
  HEALTH: `${API_BASE}/admin/health`,
} as const;

// ============================================================================
// 路由工具函数
// ============================================================================

/**
 * 构建带参数的路由
 *
 * @param route - 路由模板
 * @param params - 路由参数
 * @returns 构建后的路由
 */
export function buildRoute(route: string, params: Record<string, string | number>): string {
  let result = route;

  Object.entries(params).forEach(([key, value]) => {
    result = result.replace(`[${key}]`, String(value));
  });

  return result;
}

/**
 * 检查是否为公共路由
 *
 * @param pathname - 路径名
 * @returns 是否为公共路由
 */
export function isPublicRoute(pathname: string): boolean {
  return Object.values(PUBLIC_ROUTES).includes(pathname as any);
}

/**
 * 检查是否为受保护路由
 *
 * @param pathname - 路径名
 * @returns 是否为受保护路由
 */
export function isProtectedRoute(pathname: string): boolean {
  const protectedPaths = [
    ...Object.values(PROTECTED_ROUTES),
    ...Object.values(ORGANIZATION_ROUTES),
    ...Object.values(ASSESSMENT_ROUTES),
    ...Object.values(QUESTIONNAIRE_ROUTES),
    ...Object.values(REPORT_ROUTES),
  ];

  return protectedPaths.some(path => {
    // 处理动态路由
    const pattern = path.replace(/\[.*?\]/g, '[^/]+');
    const regex = new RegExp(`^${pattern}$`);
    return regex.test(pathname);
  });
}

/**
 * 检查是否为管理员路由
 *
 * @param pathname - 路径名
 * @returns 是否为管理员路由
 */
export function isAdminRoute(pathname: string): boolean {
  return pathname.startsWith('/admin');
}

/**
 * 获取面包屑导航
 *
 * @param pathname - 路径名
 * @returns 面包屑数组
 */
export function getBreadcrumbs(pathname: string): Array<{ label: string; href: string }> {
  const segments = pathname.split('/').filter(Boolean);
  const breadcrumbs: Array<{ label: string; href: string }> = [{ label: '首页', href: '/' }];

  let currentPath = '';

  segments.forEach((segment, _index) => {
    currentPath += `/${segment}`;

    // 根据路径段生成标签
    let label = segment;
    switch (segment) {
      case 'dashboard':
        label = '仪表板';
        break;
      case 'organizations':
        label = '组织管理';
        break;
      case 'assessments':
        label = '评估管理';
        break;
      case 'questionnaires':
        label = '问卷管理';
        break;
      case 'reports':
        label = '报告管理';
        break;
      case 'admin':
        label = '系统管理';
        break;
      case 'create':
        label = '创建';
        break;
      case 'edit':
        label = '编辑';
        break;
      default:
        // 如果是ID，尝试获取更友好的名称
        if (/^[a-f0-9-]{36}$/.test(segment)) {
          label = '详情';
        }
    }

    breadcrumbs.push({
      label,
      href: currentPath,
    });
  });

  return breadcrumbs;
}
