/**
 * 文本块解析器（卡片式2.0）
 * 路径：src/lib/analysis/block-parser.ts
 * 说明：将原始LLM文本解析为结构化Block，前端按类型渲染为不同卡片
 */

export type BlockType = 'section' | 'paragraph' | 'list' | 'flow' | 'table' | 'mermaid';

export interface SectionBlock {
  type: 'section';
  title: string;
}

export interface ParagraphBlock {
  type: 'paragraph';
  text: string;
}

export interface ListBlock {
  type: 'list';
  items: string[];
}

export interface FlowBlock {
  type: 'flow';
  steps: string[];
}

export interface TableBlock {
  type: 'table';
  headers: string[];
  rows: string[][];
}

export interface MermaidBlock {
  type: 'mermaid';
  code: string;
}

export type Block =
  | SectionBlock
  | ParagraphBlock
  | ListBlock
  | FlowBlock
  | TableBlock
  | MermaidBlock;

/**
 * 清理行：移除首尾空白、全分隔线及Markdown符号
 */
function cleanLine(line: string): string {
  return line
    .replace(/[\t]+/g, ' ')
    .replace(/^\s+|\s+$/g, '')
    .replace(/^([\|│\s\-\_]+)$/g, '')

    // 清理Markdown符号（但保留结构性符号）
    .replace(/\*\*([^*]+)\*\*/g, '$1') // 移除粗体符号 **text**
    .replace(/\*([^*]+)\*/g, '$1') // 移除斜体符号 *text*
    .replace(/__([^_]+)__/g, '$1') // 移除粗体符号 __text__
    .replace(/_([^_]+)_/g, '$1') // 移除斜体符号 _text_
    .replace(/`([^`]+)`/g, '$1') // 移除行内代码符号
    .replace(/\[([^\]]+)\]\([^)]+\)/g, '$1') // 移除链接符号，保留文本
    .replace(/~~([^~]+)~~/g, '$1') // 移除删除线符号

    .trim();
}

/**
 * 检测是否为表格行（含 | 或 │）
 */
function isTableLine(line: string): boolean {
  const l = line.trim();
  if (!l) return false;
  if (/^\s*[\|│].*[\|│]\s*$/.test(l)) return true;
  // 兼容没有首尾分隔，但中间有 | 的情况
  return l.includes('|') || l.includes('│');
}

/**
 * 解析表格块
 */
function parseTable(lines: string[], start: number): { block: TableBlock; end: number } {
  const buf: string[] = [];
  let i = start;
  while (i < lines.length && isTableLine(lines[i] ?? '')) {
    const safe = lines[i] ?? '';
    const line = safe.replace(/[\s]+/g, ' ');
    // 跳过仅由 ─ 或 — 或 - 组成的横线
    if (/^[\s\|│\-—─]+$/.test(line)) {
      i++;
      continue;
    }
    buf.push(line);
    i++;
  }
  const segments = buf.map(l =>
    l
      .split(/[\|│]/)
      .map(s => s.trim())
      .filter(Boolean)
  );
  // 取第一行作为表头（若符合“非全横线”且单元格>1）
  const headerArr: string[] = segments.length > 0 ? (segments[0] ?? []) : [];
  const rowArr: string[][] = segments.length > 1 ? segments.slice(1) : [];
  return { block: { type: 'table', headers: headerArr, rows: rowArr }, end: i - 1 };
}

/**
 * 解析列表块
 */
function parseList(lines: string[], start: number): { block: ListBlock; end: number } {
  const items: string[] = [];
  let i = start;
  while (i < lines.length) {
    const current = lines[i] ?? '';
    const m = current.match(/^\s*(?:[-*•·]|\d+[).、]|[A-Za-z][).、])\s+(.*)$/);
    if (!m) break;
    items.push((m[1] ?? '').trim());
    i++;
  }
  return { block: { type: 'list', items }, end: i - 1 };
}

/**
 * 解析流程块（A → B → C / A => B / A -> B）
 */
function parseFlow(line: string): FlowBlock {
  const steps = line
    .split(/\s*(?:→|=>|->|—>)\s*/)
    .map(s => s.trim())
    .filter(Boolean);
  return { type: 'flow', steps };
}

/**
 * 主解析入口
 */
export function parseBlocks(content: string): Block[] {
  const rawLines = content.split(/\r?\n/);
  const lines = rawLines.map(l => cleanLine(l ?? '')).filter(l => l !== '');
  const blocks: Block[] = [];

  let i = 0;
  while (i < lines.length) {
    const line = lines[i] ?? '';

    // Section：中文数字或 ### / ## / 【】
    if (
      /^[一二三四五六七八九十]+、/.test(line ?? '') ||
      /^#{2,}\s+/.test(line ?? '') ||
      /^【.+】$/.test(line ?? '')
    ) {
      const title = (line ?? '').replace(/^#{2,}\s+/, '').replace(/^【|】$/g, '');
      blocks.push({ type: 'section', title });
      i++;
      continue;
    }

    // Mermaid 代码块 ```mermaid ... ```
    if (/^```\s*mermaid/i.test(line ?? '')) {
      let code = '';
      i++;
      while (i < lines.length && !/^```/.test(lines[i] ?? '')) {
        code += (code ? '\n' : '') + lines[i];
        i++;
      }
      // 跳过结尾 ```
      if (i < lines.length && /^```/.test(lines[i] ?? '')) i++;
      blocks.push({ type: 'mermaid', code });
      continue;
    }

    // 表格块
    if (isTableLine(line)) {
      const { block, end } = parseTable(lines, i);
      blocks.push(block);
      i = end + 1;
      continue;
    }

    // 列表块
    if (/^\s*(?:[-*•·]|\d+[).、]|[A-Za-z][).、])\s+/.test(line)) {
      const { block, end } = parseList(lines, i);
      blocks.push(block);
      i = end + 1;
      continue;
    }

    // 流程块（整行含箭头）
    if (/(?:→|=>|->|—>)/.test(line)) {
      blocks.push(parseFlow(line));
      i++;
      continue;
    }

    // 段落：聚合直到遇到空行/下一结构
    let buf = line;
    i++;
    while (i < lines.length) {
      const nextLine = lines[i] ?? '';
      const shouldStop =
        isTableLine(nextLine) ||
        /^\s*(?:[-*•·]|\d+[).、]|[A-Za-z][).、])\s+/.test(nextLine) ||
        /(?:→|=>|->|—>)/.test(nextLine) ||
        /^[一二三四五六七八九十]+、/.test(nextLine) ||
        /^#{2,}\s+/.test(nextLine) ||
        /^【.+】$/.test(nextLine);
      if (shouldStop) break;
      buf += (buf.endsWith('。') ? '' : '') + (buf ? ' ' : '') + nextLine;
      i++;
    }
    blocks.push({ type: 'paragraph', text: buf });
  }

  return blocks;
}
