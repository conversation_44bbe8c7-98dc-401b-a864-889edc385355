/**
 * OCTI智能评估系统 - API工具函数
 *
 * 提供API开发中常用的工具函数
 * 包括请求验证、响应格式化、错误处理等
 */

import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

// ============================================================================
// 类型定义
// ============================================================================

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
    details?: any;
  };
  meta?: {
    timestamp: string;
    version: string;
    requestId: string;
  };
}

export interface PaginationInfo {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}

export interface ApiError {
  code: string;
  message: string;
  statusCode: number;
  details?: any;
}

// ============================================================================
// 错误代码定义
// ============================================================================

export const ERROR_CODES = {
  // 认证相关
  AUTH_REQUIRED: 'AUTH_001',
  AUTH_INVALID: 'AUTH_002',
  AUTH_EXPIRED: 'AUTH_003',
  PERMISSION_DENIED: 'AUTH_004',

  // 验证相关
  VALIDATION_ERROR: 'VALIDATION_001',
  INVALID_FORMAT: 'VALIDATION_002',
  MISSING_REQUIRED: 'VALIDATION_003',

  // 资源相关
  RESOURCE_NOT_FOUND: 'RESOURCE_001',
  RESOURCE_CONFLICT: 'RESOURCE_002',
  RESOURCE_LOCKED: 'RESOURCE_003',

  // 业务逻辑相关
  BUSINESS_LOGIC_ERROR: 'BUSINESS_001',
  OPERATION_NOT_ALLOWED: 'BUSINESS_002',
  QUOTA_EXCEEDED: 'BUSINESS_003',

  // 外部服务相关
  LLM_SERVICE_ERROR: 'EXTERNAL_001',
  DATABASE_ERROR: 'EXTERNAL_002',
  NETWORK_ERROR: 'EXTERNAL_003',

  // 系统相关
  INTERNAL_ERROR: 'SYSTEM_001',
  SERVICE_UNAVAILABLE: 'SYSTEM_002',
  RATE_LIMIT_EXCEEDED: 'SYSTEM_003',
} as const;

// ============================================================================
// 响应创建函数
// ============================================================================

/**
 * 创建成功响应
 *
 * @param data - 响应数据
 * @param statusCode - HTTP状态码
 * @param meta - 元数据
 * @returns NextResponse对象
 */
export function createSuccessResponse<T>(
  data: T,
  statusCode: number = 200,
  meta?: Partial<ApiResponse['meta']>
): NextResponse<ApiResponse<T>> {
  const response: ApiResponse<T> = {
    success: true,
    data,
    meta: {
      timestamp: new Date().toISOString(),
      version: '4.0',
      requestId: generateRequestId(),
      ...meta,
    },
  };

  return NextResponse.json(response, { status: statusCode });
}

/**
 * 创建错误响应
 *
 * @param code - 错误代码
 * @param message - 错误消息
 * @param statusCode - HTTP状态码
 * @param details - 错误详情
 * @returns NextResponse对象
 */
export function createErrorResponse(
  code: string,
  message: string,
  statusCode: number = 400,
  details?: any
): NextResponse<ApiResponse> {
  const response: ApiResponse = {
    success: false,
    error: {
      code,
      message,
      details,
    },
    meta: {
      timestamp: new Date().toISOString(),
      version: '4.0',
      requestId: generateRequestId(),
    },
  };

  return NextResponse.json(response, { status: statusCode });
}

/**
 * 创建分页响应
 *
 * @param data - 数据数组
 * @param pagination - 分页信息
 * @param meta - 额外元数据
 * @returns NextResponse对象
 */
export function createPaginatedResponse<T>(
  data: T[],
  pagination: PaginationInfo,
  meta?: Record<string, any>
): NextResponse<ApiResponse<{ items: T[]; pagination: PaginationInfo }>> {
  return createSuccessResponse({
    items: data,
    pagination,
    ...meta,
  });
}

// ============================================================================
// 请求验证函数
// ============================================================================

/**
 * 验证请求体
 *
 * @param request - NextRequest对象
 * @param schema - Zod验证schema
 * @returns 验证后的数据
 */
export async function validateRequestBody<T>(
  request: NextRequest,
  schema: z.ZodSchema<T>
): Promise<T> {
  try {
    const body = await request.json();
    return schema.parse(body);
  } catch (error) {
    if (error instanceof z.ZodError) {
      throw new ValidationError('请求体验证失败', error.errors);
    }
    throw new ValidationError('请求体格式无效');
  }
}

/**
 * 验证查询参数
 *
 * @param request - NextRequest对象
 * @param schema - Zod验证schema
 * @returns 验证后的数据
 */
export function validateQueryParams<T>(request: NextRequest, schema: z.ZodSchema<T>): T {
  try {
    const { searchParams } = new URL(request.url);
    const params = Object.fromEntries(searchParams.entries());
    return schema.parse(params);
  } catch (error) {
    if (error instanceof z.ZodError) {
      throw new ValidationError('查询参数验证失败', error.errors);
    }
    throw new ValidationError('查询参数格式无效');
  }
}

/**
 * 验证路径参数
 *
 * @param params - 路径参数对象
 * @param schema - Zod验证schema
 * @returns 验证后的数据
 */
export function validatePathParams<T>(
  params: Record<string, string | string[]>,
  schema: z.ZodSchema<T>
): T {
  try {
    return schema.parse(params);
  } catch (error) {
    if (error instanceof z.ZodError) {
      throw new ValidationError('路径参数验证失败', error.errors);
    }
    throw new ValidationError('路径参数格式无效');
  }
}

// ============================================================================
// 错误处理函数
// ============================================================================

/**
 * 处理API错误
 *
 * @param error - 错误对象
 * @returns NextResponse对象
 */
export function handleApiError(error: unknown): NextResponse<ApiResponse> {
  console.error('API Error:', error);

  if (error instanceof ValidationError) {
    return createErrorResponse(ERROR_CODES.VALIDATION_ERROR, error.message, 400, error.details);
  }

  if (error instanceof BusinessLogicError) {
    return createErrorResponse(ERROR_CODES.BUSINESS_LOGIC_ERROR, error.message, 400, error.details);
  }

  if (error instanceof ResourceNotFoundError) {
    return createErrorResponse(ERROR_CODES.RESOURCE_NOT_FOUND, error.message, 404);
  }

  if (error instanceof AuthenticationError) {
    return createErrorResponse(ERROR_CODES.AUTH_REQUIRED, error.message, 401);
  }

  if (error instanceof AuthorizationError) {
    return createErrorResponse(ERROR_CODES.PERMISSION_DENIED, error.message, 403);
  }

  // 默认内部服务器错误
  return createErrorResponse(ERROR_CODES.INTERNAL_ERROR, '内部服务器错误', 500);
}

// ============================================================================
// 自定义错误类
// ============================================================================

export class ValidationError extends Error {
  constructor(
    message: string,
    public details?: any
  ) {
    super(message);
    this.name = 'ValidationError';
  }
}

export class BusinessLogicError extends Error {
  constructor(
    message: string,
    public details?: any
  ) {
    super(message);
    this.name = 'BusinessLogicError';
  }
}

export class ResourceNotFoundError extends Error {
  constructor(message: string = '资源不存在') {
    super(message);
    this.name = 'ResourceNotFoundError';
  }
}

export class AuthenticationError extends Error {
  constructor(message: string = '身份验证失败') {
    super(message);
    this.name = 'AuthenticationError';
  }
}

export class AuthorizationError extends Error {
  constructor(message: string = '权限不足') {
    super(message);
    this.name = 'AuthorizationError';
  }
}

// ============================================================================
// 工具函数
// ============================================================================

/**
 * 生成请求ID
 *
 * @returns 唯一请求ID
 */
function generateRequestId(): string {
  return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * 解析分页参数
 *
 * @param searchParams - URL搜索参数
 * @param defaultLimit - 默认每页数量
 * @returns 分页参数
 */
export function parsePaginationParams(
  searchParams: URLSearchParams,
  defaultLimit: number = 20
): { page: number; limit: number; skip: number } {
  const page = Math.max(1, parseInt(searchParams.get('page') || '1', 10));
  const limit = Math.min(
    100,
    Math.max(1, parseInt(searchParams.get('limit') || String(defaultLimit), 10))
  );
  const skip = (page - 1) * limit;

  return { page, limit, skip };
}

/**
 * 计算分页信息
 *
 * @param page - 当前页码
 * @param limit - 每页数量
 * @param total - 总记录数
 * @returns 分页信息
 */
export function calculatePagination(page: number, limit: number, total: number): PaginationInfo {
  const totalPages = Math.ceil(total / limit);
  const hasNextPage = page < totalPages;
  const hasPreviousPage = page > 1;

  return {
    page,
    limit,
    total,
    totalPages,
    hasNextPage,
    hasPreviousPage,
  };
}

/**
 * 格式化API响应数据
 *
 * @param data - 原始数据
 * @param formatter - 格式化函数
 * @returns 格式化后的数据
 */
export function formatResponseData<T, R>(data: T, formatter: (item: T) => R): R {
  return formatter(data);
}

/**
 * 批量格式化API响应数据
 *
 * @param data - 原始数据数组
 * @param formatter - 格式化函数
 * @returns 格式化后的数据数组
 */
export function formatResponseDataArray<T, R>(data: T[], formatter: (item: T) => R): R[] {
  return data.map(formatter);
}

/**
 * 验证请求
 *
 * @param request - NextRequest对象
 * @param options - 验证选项
 * @returns 验证结果
 */
export async function validateRequest(
  request: NextRequest,
  options: {
    bodySchema?: z.ZodSchema;
    querySchema?: z.ZodSchema;
    pathSchema?: z.ZodSchema;
    requireAuth?: boolean;
  } = {}
): Promise<{
  body?: any;
  query?: any;
  path?: any;
  user?: any;
}> {
  const result: any = {};

  // 验证请求体
  if (options.bodySchema) {
    result.body = await validateRequestBody(request, options.bodySchema);
  }

  // 验证查询参数
  if (options.querySchema) {
    result.query = validateQueryParams(request, options.querySchema);
  }

  // 验证路径参数
  if (options.pathSchema) {
    // 这里需要从某个地方获取路径参数，具体实现取决于路由结构
    // result.path = validatePathParams(pathParams, options.pathSchema);
  }

  // 验证身份认证
  if (options.requireAuth) {
    // 这里需要实现身份认证逻辑
    // result.user = await authenticateRequest(request);
  }

  return result;
}
