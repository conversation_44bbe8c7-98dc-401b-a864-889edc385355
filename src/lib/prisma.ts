/**
 * OCTI智能评估系统 - Prisma数据库客户端
 *
 * 提供全局的Prisma客户端实例，支持连接池和错误处理
 */

import { PrismaClient } from '@prisma/client';
import { getEnvVar } from './utils';

// 扩展Prisma客户端类型，添加全局属性
declare global {
  // eslint-disable-next-line no-var
  var __prisma: PrismaClient | undefined;
}

/**
 * 创建Prisma客户端实例
 *
 * @returns Prisma客户端实例
 */
function createPrismaClient(): PrismaClient {
  return new PrismaClient({
    log: process.env.NODE_ENV === 'development' ? ['query', 'error', 'warn'] : ['error'],
    errorFormat: 'pretty',
    datasources: {
      db: {
        url: getEnvVar('DATABASE_URL'),
      },
    },
  });
}

/**
 * 全局Prisma客户端实例
 *
 * 在开发环境中使用全局变量避免热重载时创建多个连接
 * 在生产环境中直接创建新实例
 */
export const prisma = globalThis.__prisma ?? createPrismaClient();

// 在开发环境中将客户端实例保存到全局变量
if (process.env.NODE_ENV === 'development') {
  globalThis.__prisma = prisma;
}

/**
 * 数据库连接健康检查
 *
 * @returns 连接是否正常
 */
export async function checkDatabaseConnection(): Promise<boolean> {
  try {
    await prisma.$queryRaw`SELECT 1`;
    return true;
  } catch (error) {
    console.error('Database connection failed:', error);
    return false;
  }
}

/**
 * 优雅关闭数据库连接
 */
export async function disconnectDatabase(): Promise<void> {
  try {
    await prisma.$disconnect();
    console.log('Database disconnected successfully');
  } catch (error) {
    console.error('Error disconnecting from database:', error);
  }
}

/**
 * 数据库事务执行器
 *
 * @param callback - 事务回调函数
 * @returns 事务执行结果
 */
export async function executeTransaction<T>(
  callback: (
    tx: Omit<
      PrismaClient,
      '$connect' | '$disconnect' | '$on' | '$transaction' | '$use' | '$extends'
    >
  ) => Promise<T>
): Promise<T> {
  return await prisma.$transaction(callback);
}

/**
 * 批量操作执行器
 *
 * @param operations - 批量操作数组
 * @returns 批量操作结果
 */
export async function executeBatch(operations: any[]): Promise<any[]> {
  return await prisma.$transaction(operations);
}

/**
 * 数据库查询性能监控
 */
export function enableQueryLogging(): void {
  // 在开发环境启用查询日志
  if (process.env.NODE_ENV === 'development') {
    console.log('Query logging enabled in development mode');
  }
}

/**
 * 数据库错误处理
 */
export function handlePrismaError(error: any): never {
  console.error('Prisma error:', error);

  // 根据错误类型提供更友好的错误信息
  if (error.code === 'P2002') {
    throw new Error('数据已存在，请检查唯一性约束');
  } else if (error.code === 'P2025') {
    throw new Error('记录不存在');
  } else if (error.code === 'P2003') {
    throw new Error('外键约束违反');
  } else if (error.code === 'P2014') {
    throw new Error('数据关系冲突');
  } else {
    throw new Error('数据库操作失败');
  }
}

// 进程退出时自动断开数据库连接
process.on('beforeExit', async () => {
  await disconnectDatabase();
});

process.on('SIGINT', async () => {
  await disconnectDatabase();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  await disconnectDatabase();
  process.exit(0);
});
