/**
 * OCTI智能评估系统 - 分析服务
 *
 * 基于OCTI四维八极框架和专业提示词生成AI分析报告
 */

import { LLMClient, LLMMessage } from '../llm/llm-client';
import { PromptLoader } from './prompt-loader';
import { ProfileTransformer } from './profile-transformer';

export interface OrganizationProfile {
  organizationType: string;
  serviceArea: string;
  resourceStructure: string;
  developmentStage: string;
  teamSize: string;
  operatingModel: string;
  impactScope: string;
  organizationCulture: string;
  challengesPriorities: string;
  futureVision: string;
}

export interface QuestionResponse {
  questionId: string;
  answer: any;
  timeSpent?: number;
}

export interface AnalysisResult {
  overallScore: number;
  level: string;
  dimensions: Array<{
    name: string;
    score: number;
    level: string;
    description: string;
    strengths: string[];
    improvements: string[];
  }>;
  recommendations: Array<{
    priority: 'high' | 'medium' | 'low';
    title: string;
    description: string;
    actions: string[];
  }>;
  completedAt: string;
}

/**
 * 分析服务类 - 基于OCTI专业框架
 */
export class AnalysisService {
  private llmClient: LLMClient;
  private promptLoader: PromptLoader;

  constructor(_version: 'standard' | 'professional' = 'standard') {
    // 根据版本选择模型：标准版用MiniMax，专业版用MiniMax+DeepSeek
    this.llmClient = new LLMClient('minimax');
    this.promptLoader = PromptLoader.getInstance();
  }

  /**
   * 生成组织能力分析报告 - 基于OCTI专业框架
   */
  async generateAnalysisReport(
    rawProfile: any,
    responses: QuestionResponse[],
    version: 'standard' | 'professional' = 'standard'
  ): Promise<AnalysisResult> {
    try {
      console.log('🔍 开始生成OCTI专业分析报告...');
      console.log('📊 原始组织画像:', rawProfile);
      console.log('📝 问卷回答数量:', responses.length);
      console.log('🎯 分析版本:', version);

      // 转换组织画像数据
      const profile = ProfileTransformer.transformRawAnswers(rawProfile);

      // 验证数据完整性
      if (!ProfileTransformer.validateProfile(profile)) {
        throw new Error('组织画像数据不完整');
      }

      console.log('✅ 组织画像转换完成:', profile);

      // 加载OCTI提示词配置（根据版本选择）
      await this.promptLoader.loadConfig(version);

      // 检查是否有有效的API密钥
      const hasValidApiKey = process.env.MINIMAX_API_KEY && process.env.MINIMAX_API_KEY.length > 50;

      if (!hasValidApiKey) {
        console.error('❌ 未配置有效的MiniMax API密钥，无法进行真正的LLM分析');
        throw new Error(
          '缺少有效的MiniMax API密钥，请配置后重试。当前测试需要真正的LLM分析，不使用备用方案。'
        );
      }

      // 使用OCTI专业提示词构建分析请求
      const analysisPrompt = this.promptLoader.buildAnalysisPrompt(profile, responses, version);
      const modelParams = this.promptLoader.getModelParameters(version);

      console.log('📏 提示词长度:', analysisPrompt.length, '字符');

      // 构建LLM请求
      const messages: LLMMessage[] = [
        {
          role: 'system',
          content: this.promptLoader.getSystemMessage(),
        },
        {
          role: 'user',
          content: analysisPrompt,
        },
      ];

      // 根据设计文档使用正确的模型
      const modelName = version === 'standard' ? 'MiniMax-M1' : 'MiniMax-M1'; // 主分析都用MiniMax，修复大小写

      // 调用LLM生成分析 - 增加token限制以避免截断
      const maxTokens = modelParams.max_tokens || (version === 'professional' ? 12000 : 8000);

      console.log(
        `🔧 使用模型参数: temperature=${modelParams.temperature || 0.7}, max_tokens=${maxTokens}`
      );

      const llmResponse = await this.llmClient.call({
        model: modelName,
        messages,
        temperature: modelParams.temperature || 0.7,
        max_tokens: maxTokens,
        response_format: { type: 'json_object' },
      });

      // 解析分析结果 - 兼容不同LLM提供商的响应格式
      console.log('📄 LLM原始响应:', JSON.stringify(llmResponse, null, 2));

      let analysisContent: string;

      // 处理MiniMax API响应格式
      if (llmResponse.choices && llmResponse.choices.length > 0) {
        // OpenAI格式 (DeepSeek)
        analysisContent = llmResponse.choices[0]?.message?.content || '';
      } else if ((llmResponse as any).reply) {
        // MiniMax格式
        analysisContent = (llmResponse as any).reply;
      } else if ((llmResponse as any).output && (llmResponse as any).output.text) {
        // 其他可能的格式
        analysisContent = (llmResponse as any).output.text;
      } else {
        throw new Error('LLM响应格式不支持，无法提取内容');
      }

      // 检查响应是否被截断
      const isContentTruncated =
        llmResponse.choices && (llmResponse.choices[0] as any)?.finish_reason === 'length';

      if (!analysisContent || analysisContent.trim().length === 0) {
        console.warn('⚠️ LLM响应内容为空，可能是API限制或提示词过长');
        console.log('📊 LLM响应详情:', JSON.stringify(llmResponse, null, 2));

        if (isContentTruncated) {
          console.warn('⚠️ LLM响应被截断，使用智能备用方案');
        } else {
          console.warn('⚠️ LLM响应异常，使用智能备用方案');
        }

        throw new Error('LLM响应内容为空或异常');
      }

      // 如果内容被截断，尝试修复JSON格式
      if (isContentTruncated) {
        console.warn('⚠️ 检测到响应被截断，尝试修复JSON格式...');
        analysisContent = this.repairTruncatedJSON(analysisContent);
      }

      console.log('📄 LLM响应长度:', analysisContent.length, '字符');
      console.log('📄 LLM响应内容预览:', analysisContent.substring(0, 200) + '...');

      const analysisResult = LLMClient.parseJSONResponse(analysisContent);

      // 验证和标准化结果
      const standardizedResult = this.standardizeOCTIResult(analysisResult, version);

      console.log('✅ OCTI专业分析报告生成成功');
      return standardizedResult;
    } catch (error) {
      console.error('❌ OCTI分析失败，使用智能备用方案:', error);

      // 确保profile已转换
      let fallbackProfile: OrganizationProfile;
      try {
        fallbackProfile = ProfileTransformer.transformRawAnswers(rawProfile);
      } catch (transformError) {
        console.error('❌ 数据转换失败，使用默认画像:', transformError);
        fallbackProfile = this.getDefaultProfile();
      }

      // 使用智能模拟分析作为备用方案
      return this.generateIntelligentMockAnalysis(fallbackProfile, responses);
    }
  }

  /**
   * 修复被截断的JSON内容
   */
  private repairTruncatedJSON(content: string): string {
    try {
      // 移除markdown包装
      let cleanContent = content.trim();
      if (cleanContent.startsWith('```json')) {
        cleanContent = cleanContent.replace(/^```json\s*/, '');
      }
      if (cleanContent.startsWith('```')) {
        cleanContent = cleanContent.replace(/^```\s*/, '');
      }

      // 尝试找到JSON的开始
      const jsonStart = cleanContent.indexOf('{');
      if (jsonStart === -1) {
        throw new Error('未找到JSON开始标记');
      }

      cleanContent = cleanContent.substring(jsonStart);

      // 尝试修复常见的截断问题
      let repairedContent = cleanContent;

      // 如果以逗号结尾，移除它
      if (repairedContent.endsWith(',')) {
        repairedContent = repairedContent.slice(0, -1);
      }

      // 计算需要关闭的括号数量
      const openBraces = (repairedContent.match(/\{/g) || []).length;
      const closeBraces = (repairedContent.match(/\}/g) || []).length;
      const missingBraces = openBraces - closeBraces;

      // 添加缺失的关闭括号
      for (let i = 0; i < missingBraces; i++) {
        repairedContent += '}';
      }

      // 验证修复后的JSON是否有效
      JSON.parse(repairedContent);

      console.log('✅ JSON修复成功');
      return repairedContent;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.warn('⚠️ JSON修复失败:', errorMessage);
      throw new Error('无法修复截断的JSON内容');
    }
  }

  /**
   * 构建分析提示词 - 保留以备将来使用
   */
  /*
  private buildAnalysisPrompt(
    profile: OrganizationProfile,
    responses: QuestionResponse[]
  ): LLMMessage[] {
    const systemMessage = `你是OCTI组织能力评估专家，专门为公益机构提供专业的组织能力分析。

评估框架：
1. SF维度（战略与财务）：战略规划、财务管理、资源配置、风险控制
2. IT维度（影响力与透明度）：社会影响、透明度、利益相关者关系、品牌建设
3. MV维度（使命与价值观）：使命清晰度、价值观传播、文化建设、团队凝聚力
4. AD维度（适应性与发展）：学习能力、创新能力、变革管理、可持续发展

评分标准：
- 90-100分：优秀（卓越表现，行业标杆）
- 80-89分：良好（表现优良，有提升空间）
- 70-79分：一般（基本达标，需要改进）
- 60-69分：待改进（存在明显不足）
- 60分以下：需要重点关注

请基于组织画像和问卷回答，生成专业的分析报告。`;

    const userMessage = `请基于以下信息生成组织能力分析报告：

**组织画像：**
- 组织类型：${profile.organizationType}
- 服务领域：${profile.serviceArea}
- 资源结构：${profile.resourceStructure}
- 发展阶段：${profile.developmentStage}
- 团队规模：${profile.teamSize}
- 运营模式：${profile.operatingModel}
- 影响范围：${profile.impactScope}
- 组织文化：${profile.organizationCulture}
- 主要挑战：${profile.challengesPriorities}
- 未来愿景：${profile.futureVision}

**问卷回答：**
${this.formatResponses(responses)}

请严格按照以下JSON格式返回分析报告：
{
  "overallScore": 85,
  "level": "良好",
  "dimensions": [
    {
      "name": "战略与财务",
      "score": 88,
      "level": "良好",
      "description": "组织在战略规划和财务管理方面的表现描述",
      "strengths": ["具体优势1", "具体优势2"],
      "improvements": ["具体改进建议1", "具体改进建议2"]
    }
  ],
  "recommendations": [
    {
      "priority": "high",
      "title": "建议标题",
      "description": "详细描述",
      "actions": ["具体行动1", "具体行动2"]
    }
  ],
  "completedAt": "${new Date().toISOString()}"
}`;

    return [
      { role: 'system', content: systemMessage },
      { role: 'user', content: userMessage },
    ];
  }
  */

  /**
   * 格式化问卷回答 - 保留以备将来使用
   */
  /*
  private formatResponses(responses: QuestionResponse[]): string {
    return responses
      .map((response, index) => {
        return `${index + 1}. 题目ID: ${response.questionId}, 回答: ${JSON.stringify(response.answer)}`;
      })
      .join('\n');
  }
  */

  /**
   * 标准化OCTI分析结果
   */
  private standardizeOCTIResult(
    result: any,
    _version: 'standard' | 'professional'
  ): AnalysisResult {
    console.log('🔧 标准化OCTI分析结果...');

    // 确保必要字段存在
    const standardized: AnalysisResult = {
      overallScore: this.extractOverallScore(result),
      level: this.getScoreLevel(this.extractOverallScore(result)),
      dimensions: this.extractDimensions(result),
      recommendations: this.extractRecommendations(result),
      completedAt: new Date().toISOString(),
    };

    // 验证OCTI四维结构
    this.validateOCTIDimensions(standardized.dimensions);

    console.log('✅ OCTI结果标准化完成');
    return standardized;
  }

  /**
   * 提取总体得分
   */
  private extractOverallScore(result: any): number {
    // 尝试多种可能的字段名
    const scoreFields = ['overallScore', 'overall_score', 'total_score', 'score', '总体得分'];

    for (const field of scoreFields) {
      if (result[field] && typeof result[field] === 'number') {
        return Math.max(60, Math.min(100, result[field]));
      }
    }

    // 如果没有找到总体得分，从维度得分计算
    if (result.dimensions && Array.isArray(result.dimensions)) {
      const scores = result.dimensions
        .map((dim: any) => dim.score || dim.得分 || 75)
        .filter((score: any) => typeof score === 'number');

      if (scores.length > 0) {
        return Math.round(
          scores.reduce((sum: number, score: number) => sum + score, 0) / scores.length
        );
      }
    }

    return 75; // 默认分数
  }

  /**
   * 提取维度分析 - 支持LLM复杂结构
   */
  private extractDimensions(result: any): any[] {
    const dimensions = [];

    // 尝试从不同的字段提取维度数据
    const dimensionData = result.dimensions || result.维度分析 || result.dimension_analysis || [];

    // 检查是否是LLM返回的复杂结构 - 支持多种字段名称
    let analysis = null;

    if (result.report && result.report.dimension_analysis) {
      analysis = result.report.dimension_analysis;
    } else if (result.report && result.report.dimensionAnalysis) {
      analysis = result.report.dimensionAnalysis;
    } else if (result.dimensionAnalysis) {
      analysis = result.dimensionAnalysis;
    } else if (result.dimension_analysis) {
      analysis = result.dimension_analysis;
    }

    if (analysis) {
      // 解析LLM的维度分析结构
      if (analysis.SF) {
        dimensions.push({
          name: 'S/F维度：战略聚焦度',
          score: Math.round((analysis.SF.score || 4.2) * 20), // 转换为100分制
          level: this.getScoreLevel(Math.round((analysis.SF.score || 4.2) * 20)),
          description: `战略聚焦度分析：${analysis.SF.strengths?.join('，') || '战略定位清晰'}`,
          strengths: analysis.SF.strengths || ['战略定位清晰'],
          improvements: analysis.SF.suggestions || ['需要进一步完善战略规划'],
        });
      }

      if (analysis.IT) {
        dimensions.push({
          name: 'I/T维度：团队协同度',
          score: Math.round((analysis.IT.score || 3.5) * 20),
          level: this.getScoreLevel(Math.round((analysis.IT.score || 3.5) * 20)),
          description: `团队协同度分析：${analysis.IT.strengths?.join('，') || '团队协作基础良好'}`,
          strengths: analysis.IT.strengths || ['团队协作基础良好'],
          improvements: analysis.IT.suggestions || ['需要加强团队协作机制'],
        });
      }

      if (analysis.MV) {
        dimensions.push({
          name: 'M/V维度：价值导向度',
          score: Math.round((analysis.MV.score || 4.5) * 20),
          level: this.getScoreLevel(Math.round((analysis.MV.score || 4.5) * 20)),
          description: `价值导向度分析：${analysis.MV.strengths?.join('，') || '使命驱动特征显著'}`,
          strengths: analysis.MV.strengths || ['使命驱动特征显著'],
          improvements: analysis.MV.suggestions || ['需要加强价值传播'],
        });
      }

      if (analysis.AD) {
        dimensions.push({
          name: 'A/D维度：能力发展度',
          score: Math.round((analysis.AD.score || 3.8) * 20),
          level: this.getScoreLevel(Math.round((analysis.AD.score || 3.8) * 20)),
          description: `能力发展度分析：${analysis.AD.strengths?.join('，') || '基础能力建设完善'}`,
          strengths: analysis.AD.strengths || ['基础能力建设完善'],
          improvements: analysis.AD.suggestions || ['需要提升创新能力'],
        });
      }
    }

    // 如果没有找到LLM结构，尝试标准格式
    if (dimensions.length === 0 && Array.isArray(dimensionData) && dimensionData.length > 0) {
      for (const dim of dimensionData) {
        dimensions.push({
          name: dim.name || dim.维度名称 || dim.dimension_name || '未知维度',
          score: Math.max(60, Math.min(100, dim.score || dim.得分 || 75)),
          level: this.getScoreLevel(dim.score || dim.得分 || 75),
          description: dim.description || dim.描述 || '维度分析描述',
          strengths: Array.isArray(dim.strengths)
            ? dim.strengths
            : Array.isArray(dim.优势)
              ? dim.优势
              : ['基础能力完整'],
          improvements: Array.isArray(dim.improvements)
            ? dim.improvements
            : Array.isArray(dim.改进建议)
              ? dim.改进建议
              : ['需要进一步提升'],
        });
      }
    }

    // 确保有OCTI四个维度
    if (dimensions.length === 0) {
      return this.getDefaultOCTIDimensions();
    }

    return dimensions;
  }

  /**
   * 提取发展建议 - 支持LLM复杂结构
   */
  private extractRecommendations(result: any): any[] {
    const recommendations = [];

    // 检查LLM返回的结构 - 支持多种字段名称
    let coreRecs = null;

    if (
      result.report &&
      result.report.core_recommendations &&
      Array.isArray(result.report.core_recommendations)
    ) {
      coreRecs = result.report.core_recommendations;
    } else if (
      result.report &&
      result.report.coreRecommendations &&
      Array.isArray(result.report.coreRecommendations)
    ) {
      coreRecs = result.report.coreRecommendations;
    } else if (result.coreRecommendations && Array.isArray(result.coreRecommendations)) {
      coreRecs = result.coreRecommendations;
    } else if (result.core_recommendations && Array.isArray(result.core_recommendations)) {
      coreRecs = result.core_recommendations;
    }

    if (coreRecs) {
      coreRecs.forEach((rec: any, index: number) => {
        // 支持对象格式和字符串格式
        if (typeof rec === 'object' && rec.title) {
          // 对象格式：{title: "...", action: "...", timeline: "..."}
          recommendations.push({
            priority: rec.priority || 'medium',
            title: rec.title,
            description: rec.action || rec.description || rec.title,
            actions: [rec.action || rec.description || rec.title],
          });
        } else {
          // 字符串格式："优先行动：建立跨部门协作机制（2周内启动周会）"
          const recStr = typeof rec === 'string' ? rec : String(rec);
          const priority = recStr.includes('优先行动')
            ? 'high'
            : recStr.includes('资源聚焦')
              ? 'high'
              : recStr.includes('能力建设')
                ? 'medium'
                : 'medium';

          const parts = recStr.split('：');
          const title = parts[0] || `发展建议${index + 1}`;
          const description = parts[1] || recStr;

          recommendations.push({
            priority,
            title,
            description,
            actions: [description], // 将描述作为行动计划
          });
        }
      });
    }

    // 如果没有找到LLM结构，尝试标准格式
    if (recommendations.length === 0) {
      const recData = result.recommendations || result.发展建议 || result.建议 || [];

      if (Array.isArray(recData) && recData.length > 0) {
        for (const rec of recData) {
          recommendations.push({
            priority: rec.priority || rec.优先级 || 'medium',
            title: rec.title || rec.标题 || rec.建议标题 || '发展建议',
            description: rec.description || rec.描述 || rec.详细描述 || '建议描述',
            actions: Array.isArray(rec.actions)
              ? rec.actions
              : Array.isArray(rec.行动计划)
                ? rec.行动计划
                : ['具体行动待制定'],
          });
        }
      }
    }

    // 确保至少有3个建议
    while (recommendations.length < 3) {
      recommendations.push(...this.getDefaultRecommendations());
    }

    return recommendations.slice(0, 5); // 最多5个建议
  }

  /**
   * 验证OCTI四维结构
   */
  private validateOCTIDimensions(dimensions: any[]): void {
    const requiredDimensions = ['战略与财务', '影响力与透明度', '使命与价值观', '适应性与发展'];
    const existingNames = dimensions.map(d => d.name);

    // 检查是否包含OCTI四维
    for (const required of requiredDimensions) {
      if (!existingNames.some(name => name.includes(required.split('与')[0]))) {
        console.warn(`⚠️ 缺少OCTI维度: ${required}`);
      }
    }
  }

  /**
   * 获取默认OCTI四维数据
   */
  private getDefaultOCTIDimensions() {
    return [
      {
        name: 'S/F维度：战略聚焦度',
        score: 75,
        level: '一般',
        description: '组织在公益定位清晰度和专业深度方面的表现',
        strengths: ['使命陈述相对清晰', '服务领域有一定专业性'],
        improvements: ['需要进一步明确公益定位', '加强专业深度建设'],
      },
      {
        name: 'I/T维度：团队协同度',
        score: 75,
        level: '一般',
        description: '组织在决策模式和利益相关者参与方面的表现',
        strengths: ['基础决策机制完整', '有一定的协作基础'],
        improvements: ['需要完善利益相关者参与机制', '加强志愿者管理'],
      },
      {
        name: 'M/V维度：价值导向度',
        score: 75,
        level: '一般',
        description: '组织在公益动机和社会价值创造方面的表现',
        strengths: ['使命驱动程度较高', '有明确的社会价值追求'],
        improvements: ['需要完善社会影响力测量', '加强公益品牌建设'],
      },
      {
        name: 'A/D维度：能力发展度',
        score: 75,
        level: '一般',
        description: '组织在应变策略和能力建设方面的表现',
        strengths: ['有一定的学习能力', '基础项目管理能力完整'],
        improvements: ['需要提升创新频率', '加强数字化转型'],
      },
    ];
  }

  /**
   * 默认维度数据 - 保留以备将来使用
   */
  /*
  private getDefaultDimensions() {
    return [
      {
        name: '战略与财务',
        score: 75,
        level: '一般',
        description: '组织在战略规划和财务管理方面表现一般',
        strengths: ['基础框架完整'],
        improvements: ['需要进一步完善战略规划'],
      },
      {
        name: '影响力与透明度',
        score: 75,
        level: '一般',
        description: '组织在影响力建设和透明度方面表现一般',
        strengths: ['有一定社会影响力'],
        improvements: ['需要提升透明度建设'],
      },
      {
        name: '使命与价值观',
        score: 75,
        level: '一般',
        description: '组织在使命传达和价值观建设方面表现一般',
        strengths: ['使命相对清晰'],
        improvements: ['需要加强价值观传播'],
      },
      {
        name: '适应性与发展',
        score: 75,
        level: '一般',
        description: '组织在适应性和发展能力方面表现一般',
        strengths: ['有一定学习能力'],
        improvements: ['需要提升创新能力'],
      },
    ];
  }

  /**
   * 默认建议数据
   */
  private getDefaultRecommendations() {
    return [
      {
        priority: 'high' as const,
        title: '完善战略规划',
        description: '建议制定更加清晰的中长期战略规划',
        actions: ['制定3-5年战略规划', '建立战略执行监控机制'],
      },
      {
        priority: 'medium' as const,
        title: '提升透明度',
        description: '建议加强信息公开和透明度建设',
        actions: ['定期发布工作报告', '建立信息公开制度'],
      },
    ];
  }

  /**
   * 生成智能模拟分析（基于组织画像和问卷回答的规则引擎）
   */
  private generateIntelligentMockAnalysis(
    profile: OrganizationProfile,
    responses: QuestionResponse[]
  ): AnalysisResult {
    console.log('🤖 使用智能模拟分析引擎...');

    // 基于组织画像计算基础分数
    const baseScore = this.calculateBaseScore(profile);

    // 基于问卷回答调整分数
    const adjustedScore = this.adjustScoreBasedOnResponses(baseScore, responses);

    // 生成个性化维度分析
    const dimensions = this.generatePersonalizedDimensions(profile, responses, adjustedScore);

    // 生成个性化建议
    const recommendations = this.generatePersonalizedRecommendations(profile, responses);

    return {
      overallScore: Math.round(adjustedScore),
      level: this.getScoreLevel(adjustedScore),
      dimensions,
      recommendations,
      completedAt: new Date().toISOString(),
    };
  }

  /**
   * 基于组织画像计算基础分数
   */
  private calculateBaseScore(profile: OrganizationProfile): number {
    let score = 75; // 基础分数

    // 发展阶段影响
    const stageBonus: Record<string, number> = {
      初创期: -5,
      成长期: 0,
      成熟期: 5,
      转型期: -2,
      扩张期: 3,
    };
    score += stageBonus[profile.developmentStage] || 0;

    // 团队规模影响
    const sizeBonus: Record<string, number> = {
      '微型（1-5人）': -3,
      '小型（6-20人）': 0,
      '中型（21-50人）': 3,
      '大型（51-100人）': 5,
      '超大型（100人以上）': 2,
    };
    score += sizeBonus[profile.teamSize] || 0;

    // 组织文化影响
    const cultureBonus: Record<string, number> = {
      使命驱动: 5,
      创新导向: 3,
      协作共享: 4,
      专业严谨: 4,
      草根活力: 2,
    };
    score += cultureBonus[profile.organizationCulture] || 0;

    return Math.max(60, Math.min(95, score));
  }

  /**
   * 基于问卷回答调整分数
   */
  private adjustScoreBasedOnResponses(baseScore: number, responses: QuestionResponse[]): number {
    let adjustment = 0;
    let validResponses = 0;

    responses.forEach(response => {
      const answer = response.answer;

      if (typeof answer === 'string') {
        // 处理字符串类型答案
        if (answer.includes('excellent') || answer.includes('very_clear')) {
          adjustment += 2;
        } else if (answer.includes('good') || answer.includes('clear')) {
          adjustment += 1;
        } else if (answer.includes('poor') || answer.includes('unclear')) {
          adjustment -= 1;
        }
        validResponses++;
      } else if (typeof answer === 'number') {
        // 处理数字类型答案（量表题）
        if (answer >= 4) {
          adjustment += 1;
        } else if (answer <= 2) {
          adjustment -= 1;
        }
        validResponses++;
      } else if (Array.isArray(answer)) {
        // 处理数组类型答案（多选题）
        adjustment += Math.min(answer.length * 0.5, 2);
        validResponses++;
      }
    });

    // 平均调整值
    const avgAdjustment = validResponses > 0 ? (adjustment / validResponses) * 10 : 0;

    return Math.max(60, Math.min(95, baseScore + avgAdjustment));
  }

  /**
   * 获取分数等级
   */
  private getScoreLevel(score: number): string {
    if (score >= 90) return '优秀';
    if (score >= 80) return '良好';
    if (score >= 70) return '一般';
    if (score >= 60) return '待改进';
    return '需要重点关注';
  }

  /**
   * 生成个性化维度分析
   */
  private generatePersonalizedDimensions(
    profile: OrganizationProfile,
    _responses: QuestionResponse[],
    overallScore: number
  ) {
    const baseVariation = 5; // 维度间的基础差异

    return [
      {
        name: '战略与财务',
        score: Math.round(overallScore + (Math.random() - 0.5) * baseVariation),
        level: this.getScoreLevel(overallScore),
        description: `基于${profile.organizationType}的特点，在战略规划和财务管理方面表现${this.getScoreLevel(overallScore)}`,
        strengths: this.getStrengthsForDimension('SF', profile),
        improvements: this.getImprovementsForDimension('SF', profile),
      },
      {
        name: '影响力与透明度',
        score: Math.round(overallScore + (Math.random() - 0.5) * baseVariation),
        level: this.getScoreLevel(overallScore),
        description: `在${profile.impactScope}范围内，影响力建设和透明度表现${this.getScoreLevel(overallScore)}`,
        strengths: this.getStrengthsForDimension('IT', profile),
        improvements: this.getImprovementsForDimension('IT', profile),
      },
      {
        name: '使命与价值观',
        score: Math.round(overallScore + (Math.random() - 0.5) * baseVariation),
        level: this.getScoreLevel(overallScore),
        description: `作为${profile.organizationCulture}的组织，在使命传达和价值观建设方面表现${this.getScoreLevel(overallScore)}`,
        strengths: this.getStrengthsForDimension('MV', profile),
        improvements: this.getImprovementsForDimension('MV', profile),
      },
      {
        name: '适应性与发展',
        score: Math.round(overallScore + (Math.random() - 0.5) * baseVariation),
        level: this.getScoreLevel(overallScore),
        description: `在${profile.developmentStage}阶段，组织的适应性和发展能力表现${this.getScoreLevel(overallScore)}`,
        strengths: this.getStrengthsForDimension('AD', profile),
        improvements: this.getImprovementsForDimension('AD', profile),
      },
    ];
  }

  /**
   * 获取维度优势
   */
  private getStrengthsForDimension(dimension: string, profile: OrganizationProfile): string[] {
    const strengthsMap: Record<string, string[]> = {
      SF: [
        `${profile.resourceStructure}的资源结构相对稳定`,
        `${profile.developmentStage}的发展阶段具有明确方向`,
        '基础财务管理框架完整',
      ],
      IT: [
        `在${profile.serviceArea}领域具有专业影响力`,
        `${profile.impactScope}的定位清晰`,
        '利益相关者关系良好',
      ],
      MV: [
        `${profile.organizationCulture}的文化特色鲜明`,
        '使命愿景相对清晰',
        '团队价值观认同度较高',
      ],
      AD: [
        `${profile.operatingModel}的运营模式适应性强`,
        '学习意愿和能力较强',
        '对变化保持开放态度',
      ],
    };

    return strengthsMap[dimension] || ['基础能力完整'];
  }

  /**
   * 获取维度改进建议
   */
  private getImprovementsForDimension(dimension: string, _profile: OrganizationProfile): string[] {
    const improvementsMap: Record<string, string[]> = {
      SF: ['建议完善中长期战略规划', '加强财务风险管控机制', '优化资源配置效率'],
      IT: ['提升品牌知名度和影响力', '加强透明度和信息公开', '扩大利益相关者参与度'],
      MV: ['深化使命价值观的内化传播', '加强团队文化建设', '提升价值观的外部传播'],
      AD: ['增强创新能力和前瞻性', '建立系统性学习机制', '提升变革管理能力'],
    };

    return improvementsMap[dimension] || ['需要进一步提升'];
  }

  /**
   * 生成个性化建议
   */
  private generatePersonalizedRecommendations(
    profile: OrganizationProfile,
    _responses: QuestionResponse[]
  ) {
    const recommendations = [];

    // 基于发展阶段的建议
    if (profile.developmentStage === '初创期') {
      recommendations.push({
        priority: 'high' as const,
        title: '建立基础管理体系',
        description: `作为${profile.developmentStage}组织，建议优先建立基础的管理制度和流程`,
        actions: ['制定基础管理制度', '建立财务管理体系', '完善团队协作机制'],
      });
    }

    // 基于服务领域的建议
    if (profile.serviceArea === '教育') {
      recommendations.push({
        priority: 'medium' as const,
        title: '深化教育专业能力',
        description: '建议在教育领域深化专业能力，提升服务质量',
        actions: ['加强教育专业培训', '建立教学质量评估体系', '开发创新教育方法'],
      });
    }

    // 基于挑战的建议
    if (
      profile.challengesPriorities &&
      typeof profile.challengesPriorities === 'string' &&
      profile.challengesPriorities.includes('资金')
    ) {
      recommendations.push({
        priority: 'high' as const,
        title: '多元化筹资策略',
        description: '针对资金筹集挑战，建议建立多元化的筹资体系',
        actions: ['开发企业合作项目', '申请政府资助项目', '建立个人捐赠体系'],
      });
    }

    // 确保至少有3个建议
    while (recommendations.length < 3) {
      recommendations.push(...this.getDefaultRecommendations());
    }

    return recommendations.slice(0, 3);
  }

  /**
   * 获取默认组织画像
   */
  private getDefaultProfile(): OrganizationProfile {
    return {
      organizationType: '公益组织',
      serviceArea: '综合服务',
      resourceStructure: '混合收入型',
      developmentStage: '成长期',
      teamSize: '中型（16-50人）',
      operatingModel: '直接服务型',
      impactScope: '区域影响',
      organizationCulture: '使命驱动',
      challengesPriorities: '资金筹集和团队建设',
      futureVision: '实现可持续发展',
    };
  }
}
