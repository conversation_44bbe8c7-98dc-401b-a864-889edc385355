/**
 * OCTI智能评估系统 - 组织画像数据转换器
 *
 * 将原始问卷答案转换为结构化的组织画像数据
 */

export interface OrganizationProfile {
  organizationType: string;
  serviceArea: string;
  resourceStructure: string;
  developmentStage: string;
  teamSize: string;
  operatingModel: string;
  impactScope: string;
  organizationCulture: string;
  challengesPriorities: string;
  futureVision: string;
}

/**
 * 组织画像数据转换器
 */
export class ProfileTransformer {
  /**
   * 将原始答案转换为结构化组织画像
   */
  static transformRawAnswers(rawAnswers: any): OrganizationProfile {
    console.log('🔄 转换组织画像数据:', rawAnswers);

    // 如果已经是结构化数据，直接返回
    if (rawAnswers.organizationType) {
      return rawAnswers as OrganizationProfile;
    }

    // 转换原始答案（数字键值对）为结构化数据
    const profile: OrganizationProfile = {
      organizationType: this.getOrganizationType(rawAnswers['1']),
      serviceArea: this.getServiceArea(rawAnswers['2']),
      resourceStructure: this.getResourceStructure(rawAnswers['3']),
      developmentStage: this.getDevelopmentStage(rawAnswers['4']),
      teamSize: this.getTeamSize(rawAnswers['5']),
      operatingModel: this.getOperatingModel(rawAnswers['6']),
      impactScope: this.getImpactScope(rawAnswers['7']),
      organizationCulture: this.getOrganizationCulture(rawAnswers['8']),
      challengesPriorities: this.getChallengesPriorities(rawAnswers['9']),
      futureVision: this.getFutureVision(rawAnswers['10']),
    };

    console.log('✅ 组织画像转换完成:', profile);
    return profile;
  }

  private static getOrganizationType(value: string): string {
    const mapping: Record<string, string> = {
      A: '初创期公益组织',
      B: '探索期公益组织',
      C: '成长期公益组织',
      D: '成熟期公益组织',
      E: '转型期公益组织',
      F: '分化期公益组织',
    };
    return mapping[value] || '公益组织';
  }

  private static getServiceArea(value: string): string {
    const mapping: Record<string, string> = {
      A: '教育',
      B: '环保',
      C: '扶贫',
      D: '医疗健康',
      E: '文化艺术',
      F: '综合服务',
    };
    return mapping[value] || '综合服务';
  }

  private static getResourceStructure(value: string): string {
    const mapping: Record<string, string> = {
      A: '政府主导型',
      B: '基金会支持型',
      C: '公众募捐型',
      D: '企业合作型',
      E: '混合收入型',
      F: '自主经营型',
    };
    return mapping[value] || '混合收入型';
  }

  private static getDevelopmentStage(value: string): string {
    const mapping: Record<string, string> = {
      A: '初创期',
      B: '探索期',
      C: '成长期',
      D: '成熟期',
      E: '转型期',
      F: '分化期',
    };
    return mapping[value] || '成长期';
  }

  private static getTeamSize(value: string): string {
    const mapping: Record<string, string> = {
      A: '微型（1-5人）',
      B: '小型（6-15人）',
      C: '中型（16-50人）',
      D: '大型（51-100人）',
      E: '超大型（100人以上）',
      F: '分布式团队',
    };
    return mapping[value] || '中型（16-50人）';
  }

  private static getOperatingModel(value: string): string {
    const mapping: Record<string, string> = {
      A: '直接服务型',
      B: '倡导推动型',
      C: '平台连接型',
      D: '能力建设型',
      E: '资源配置型',
      F: '创新孵化型',
    };
    return mapping[value] || '直接服务型';
  }

  private static getImpactScope(value: string): string {
    const mapping: Record<string, string> = {
      A: '社区影响',
      B: '城市影响',
      C: '区域影响',
      D: '全国影响',
      E: '国际影响',
      F: '行业影响',
    };
    return mapping[value] || '区域影响';
  }

  private static getOrganizationCulture(value: string): string {
    const mapping: Record<string, string> = {
      A: '使命驱动',
      B: '创新导向',
      C: '协作共享',
      D: '专业严谨',
      E: '草根活力',
      F: '学习成长',
    };
    return mapping[value] || '使命驱动';
  }

  private static getChallengesPriorities(value: string): string {
    const mapping: Record<string, string> = {
      A: '资金筹集',
      B: '团队建设',
      C: '项目管理',
      D: '影响力扩大',
      E: '制度建设',
      F: '创新发展',
    };
    return mapping[value] || '资金筹集和团队建设';
  }

  private static getFutureVision(value: string): string {
    const mapping: Record<string, string> = {
      A: '成为行业领导者',
      B: '扩大服务规模',
      C: '提升专业能力',
      D: '建立品牌影响力',
      E: '实现可持续发展',
      F: '推动行业变革',
    };
    return mapping[value] || '实现可持续发展';
  }

  /**
   * 验证组织画像数据完整性
   */
  static validateProfile(profile: OrganizationProfile): boolean {
    const requiredFields = [
      'organizationType',
      'serviceArea',
      'resourceStructure',
      'developmentStage',
      'teamSize',
      'operatingModel',
      'impactScope',
      'organizationCulture',
      'challengesPriorities',
      'futureVision',
    ];

    for (const field of requiredFields) {
      const fieldValue = (profile as any)[field];
      if (!fieldValue || (typeof fieldValue === 'string' && fieldValue.trim() === '')) {
        console.warn(`⚠️ 组织画像缺少字段: ${field}`);
        return false;
      }
    }

    return true;
  }
}
