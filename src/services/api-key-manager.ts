/**
 * API Key管理器 - 支持多密钥轮询和负载均衡
 *
 * 功能：
 * - 自动检测可用API密钥
 * - 轮询分配API密钥
 * - 故障转移和重试机制
 * - 密钥使用统计
 */

export interface ApiKeyStats {
  keyId: string;
  usageCount: number;
  successCount: number;
  failureCount: number;
  lastUsed: Date;
  isActive: boolean;
}

export class ApiKeyManager {
  private static instance: ApiKeyManager;
  private keys: string[] = [];
  private currentIndex: number = 0;
  private stats: Map<string, ApiKeyStats> = new Map();

  private constructor() {
    this.loadApiKeys();
  }

  /**
   * 获取单例实例
   */
  static getInstance(): ApiKeyManager {
    if (!ApiKeyManager.instance) {
      ApiKeyManager.instance = new ApiKeyManager();
    }
    return ApiKeyManager.instance;
  }

  /**
   * 加载可用的API密钥
   */
  private loadApiKeys(): void {
    const rawKeys = [
      process.env.MINIMAX_API_KEY_1,
      process.env.MINIMAX_API_KEY_2,
      process.env.MINIMAX_API_KEY_3,
      process.env.MINIMAX_API_KEY_4,
      process.env.MINIMAX_API_KEY, // 备用主密钥
    ];

    // 过滤有效密钥（长度大于50的字符串）
    this.keys = rawKeys.filter(
      (key): key is string => key !== undefined && typeof key === 'string' && key.length > 50
    );

    // 初始化统计信息
    this.keys.forEach((_, index) => {
      const keyId = `key_${index + 1}`;
      this.stats.set(keyId, {
        keyId,
        usageCount: 0,
        successCount: 0,
        failureCount: 0,
        lastUsed: new Date(),
        isActive: true,
      });
    });

    console.log(`🔑 API Key管理器：发现${this.keys.length}个有效密钥`);
  }

  /**
   * 获取下一个可用的API密钥
   */
  getNextApiKey(): { key: string; keyId: string } | null {
    if (this.keys.length === 0) {
      console.warn('⚠️ 没有可用的API密钥');
      return null;
    }

    // 简单轮询策略
    const key = this.keys[this.currentIndex];
    if (!key) {
      console.warn('⚠️ 获取API密钥失败');
      return null;
    }

    const keyId = `key_${this.currentIndex + 1}`;

    // 更新使用统计
    const stats = this.stats.get(keyId);
    if (stats) {
      stats.usageCount++;
      stats.lastUsed = new Date();
    }

    // 移到下一个密钥
    this.currentIndex = (this.currentIndex + 1) % this.keys.length;

    console.log(`🔄 分配API密钥: ${keyId} (${key.substring(0, 20)}...)`);
    return { key, keyId };
  }

  /**
   * 记录API调用成功
   */
  recordSuccess(keyId: string): void {
    const stats = this.stats.get(keyId);
    if (stats) {
      stats.successCount++;
      console.log(`✅ ${keyId}调用成功 (成功${stats.successCount}次)`);
    }
  }

  /**
   * 记录API调用失败
   */
  recordFailure(keyId: string, error: string): void {
    const stats = this.stats.get(keyId);
    if (stats) {
      stats.failureCount++;
      console.warn(`❌ ${keyId}调用失败: ${error} (失败${stats.failureCount}次)`);

      // 如果失败次数过多，临时禁用该密钥
      if (stats.failureCount >= 5) {
        stats.isActive = false;
        console.warn(`⚠️ ${keyId}失败次数过多，临时禁用`);
      }
    }
  }

  /**
   * 获取统计信息
   */
  getStats(): ApiKeyStats[] {
    return Array.from(this.stats.values());
  }

  /**
   * 获取可用密钥数量
   */
  getAvailableCount(): number {
    return this.keys.filter((_, index) => {
      const keyId = `key_${index + 1}`;
      const stats = this.stats.get(keyId);
      return stats?.isActive !== false;
    }).length;
  }

  /**
   * 重置所有密钥状态（用于故障恢复）
   */
  resetAllKeys(): void {
    this.stats.forEach(stats => {
      stats.isActive = true;
      stats.failureCount = 0;
    });
    console.log('🔄 重置所有API密钥状态');
  }

  /**
   * 打印使用统计
   */
  printStats(): void {
    console.log('\n📊 API Key使用统计:');
    this.stats.forEach(stats => {
      const successRate =
        stats.usageCount > 0 ? ((stats.successCount / stats.usageCount) * 100).toFixed(1) : '0.0';

      console.log(
        `  ${stats.keyId}: 使用${stats.usageCount}次, 成功${stats.successCount}次, 成功率${successRate}%`
      );
    });
  }
}

/**
 * 获取API密钥管理器实例
 */
export const apiKeyManager = ApiKeyManager.getInstance();
