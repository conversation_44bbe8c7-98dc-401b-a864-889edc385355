/**
 * OCTI智能评估系统 - 配置引擎
 *
 * 负责JSON配置文件的管理、版本控制和热更新
 * 支持智能体配置的动态加载和验证
 */

import fs from 'fs/promises';
import path from 'path';
import { z } from 'zod';
import { safeJsonParse, deepClone } from '@/lib/utils';

// ============================================================================
// 配置类型定义
// ============================================================================

/**
 * 智能体配置接口
 */
export interface AgentConfig {
  id: string;
  type: 'QUESTION_DESIGNER' | 'ORGANIZATION_MENTOR';
  name: string;
  version: string;
  description: string;
  config: {
    model: string;
    temperature: number;
    maxTokens: number;
    systemPrompt: string;
    userPromptTemplate: string;
    parameters: Record<string, any>;
  };
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

/**
 * 问卷配置接口
 */
export interface QuestionnaireConfig {
  totalQuestions: number;
  presetQuestions: number;
  aiGeneratedQuestions: number;
  dimensions: {
    [key: string]: {
      name: string;
      description: string;
      presetCount: number;
      aiGeneratedCount: number;
      weight: number;
    };
  };
  qualityThresholds: {
    relevance: number;
    clarity: number;
    uniqueness: number;
  };
}

/**
 * 系统配置接口
 */
export interface SystemConfig {
  llm: {
    minimax: {
      baseUrl: string;
      model: string;
      maxTokens: number;
      temperature: number;
    };
    deepseek: {
      baseUrl: string;
      model: string;
      maxTokens: number;
      temperature: number;
    };
  };
  questionnaire: QuestionnaireConfig;
  analysis: {
    confidenceThreshold: number;
    enableDualModel: boolean;
    fusionStrategy: 'weighted' | 'consensus' | 'adaptive';
  };
}

// ============================================================================
// 配置验证Schema
// ============================================================================

const AgentConfigSchema = z.object({
  id: z.string(),
  type: z.enum(['QUESTION_DESIGNER', 'ORGANIZATION_MENTOR']),
  name: z.string(),
  version: z.string(),
  description: z.string(),
  config: z.object({
    model: z.string(),
    temperature: z.number().min(0).max(2),
    maxTokens: z.number().positive(),
    systemPrompt: z.string(),
    userPromptTemplate: z.string(),
    parameters: z.record(z.any()),
  }),
  isActive: z.boolean(),
  createdAt: z.string(),
  updatedAt: z.string(),
});

const QuestionnaireConfigSchema = z.object({
  totalQuestions: z.number().positive(),
  presetQuestions: z.number().positive(),
  aiGeneratedQuestions: z.number().positive(),
  dimensions: z.record(
    z.object({
      name: z.string(),
      description: z.string(),
      presetCount: z.number().nonnegative(),
      aiGeneratedCount: z.number().nonnegative(),
      weight: z.number().positive(),
    })
  ),
  qualityThresholds: z.object({
    relevance: z.number().min(0).max(1),
    clarity: z.number().min(0).max(1),
    uniqueness: z.number().min(0).max(1),
  }),
});

// ============================================================================
// 配置引擎类
// ============================================================================

export class ConfigEngine {
  private configCache = new Map<string, any>();
  private configPath: string;
  private watchers = new Map<string, any>();

  constructor(configPath: string = 'configs') {
    this.configPath = path.resolve(process.cwd(), configPath);
  }

  /**
   * 加载配置文件
   *
   * @param configType - 配置类型
   * @param useCache - 是否使用缓存
   * @returns 配置对象
   */
  async loadConfig<T = any>(configType: string, useCache: boolean = true): Promise<T> {
    // 检查缓存
    if (useCache && this.configCache.has(configType)) {
      return deepClone(this.configCache.get(configType));
    }

    try {
      const configFile = path.join(this.configPath, `${configType}.json`);
      const configContent = await fs.readFile(configFile, 'utf-8');
      const config = safeJsonParse(configContent, null);

      if (!config) {
        throw new Error(`Invalid JSON in config file: ${configFile}`);
      }

      // 验证配置
      this.validateConfig(configType, config);

      // 缓存配置
      this.configCache.set(configType, deepClone(config));

      return config;
    } catch (error) {
      console.error(`Failed to load config ${configType}:`, error);
      throw new Error(`Configuration loading failed: ${configType}`);
    }
  }

  /**
   * 保存配置文件
   *
   * @param configType - 配置类型
   * @param config - 配置对象
   */
  async saveConfig(configType: string, config: any): Promise<void> {
    try {
      // 验证配置
      this.validateConfig(configType, config);

      const configFile = path.join(this.configPath, `${configType}.json`);
      const configContent = JSON.stringify(config, null, 2);

      await fs.writeFile(configFile, configContent, 'utf-8');

      // 更新缓存
      this.configCache.set(configType, deepClone(config));

      console.log(`Configuration saved: ${configType}`);
    } catch (error) {
      console.error(`Failed to save config ${configType}:`, error);
      throw new Error(`Configuration saving failed: ${configType}`);
    }
  }

  /**
   * 验证配置
   *
   * @param configType - 配置类型
   * @param config - 配置对象
   */
  private validateConfig(configType: string, config: any): void {
    try {
      switch (configType) {
        case 'question-designer':
        case 'organization-mentor':
          AgentConfigSchema.parse(config);
          break;
        case 'questionnaire':
          QuestionnaireConfigSchema.parse(config);
          break;
        default:
          // 对于未知类型，进行基本验证
          if (typeof config !== 'object' || config === null) {
            throw new Error('Configuration must be an object');
          }
      }
    } catch (error) {
      throw new Error(`Configuration validation failed: ${error}`);
    }
  }

  /**
   * 获取智能体配置
   *
   * @param agentType - 智能体类型
   * @returns 智能体配置
   */
  async getAgentConfig(
    agentType: 'QUESTION_DESIGNER' | 'ORGANIZATION_MENTOR'
  ): Promise<AgentConfig> {
    const configType = agentType.toLowerCase().replace('_', '-');
    return await this.loadConfig<AgentConfig>(configType);
  }

  /**
   * 获取问卷配置
   *
   * @returns 问卷配置
   */
  async getQuestionnaireConfig(): Promise<QuestionnaireConfig> {
    return await this.loadConfig<QuestionnaireConfig>('questionnaire');
  }

  /**
   * 获取系统配置
   *
   * @returns 系统配置
   */
  async getSystemConfig(): Promise<SystemConfig> {
    return await this.loadConfig<SystemConfig>('system');
  }

  /**
   * 启用配置文件监听
   *
   * @param configType - 配置类型
   * @param callback - 变更回调函数
   */
  async watchConfig(configType: string, callback?: (config: any) => void): Promise<void> {
    const configFile = path.join(this.configPath, `${configType}.json`);

    try {
      const watcher = await fs.watch(configFile);

      for await (const event of watcher) {
        if (event.eventType === 'change') {
          console.log(`Configuration changed: ${configType}`);

          try {
            // 重新加载配置
            const newConfig = await this.loadConfig(configType, false);

            // 执行回调
            if (callback) {
              callback(newConfig);
            }
          } catch (error) {
            console.error(`Failed to reload config ${configType}:`, error);
          }
        }
      }
    } catch (error) {
      console.error(`Failed to watch config ${configType}:`, error);
    }
  }

  /**
   * 停止配置文件监听
   *
   * @param configType - 配置类型
   */
  stopWatching(configType: string): void {
    const watcher = this.watchers.get(configType);
    if (watcher) {
      watcher.close();
      this.watchers.delete(configType);
      console.log(`Stopped watching config: ${configType}`);
    }
  }

  /**
   * 清空配置缓存
   *
   * @param configType - 配置类型（可选，不传则清空所有缓存）
   */
  clearCache(configType?: string): void {
    if (configType) {
      this.configCache.delete(configType);
    } else {
      this.configCache.clear();
    }
  }

  /**
   * 获取配置列表
   *
   * @returns 配置文件列表
   */
  async getConfigList(): Promise<string[]> {
    try {
      const files = await fs.readdir(this.configPath);
      return files.filter(file => file.endsWith('.json')).map(file => file.replace('.json', ''));
    } catch (error) {
      console.error('Failed to get config list:', error);
      return [];
    }
  }

  /**
   * 检查配置文件是否存在
   *
   * @param configType - 配置类型
   * @returns 是否存在
   */
  async configExists(configType: string): Promise<boolean> {
    try {
      const configFile = path.join(this.configPath, `${configType}.json`);
      await fs.access(configFile);
      return true;
    } catch {
      return false;
    }
  }
}

// ============================================================================
// 全局配置引擎实例
// ============================================================================

export const configEngine = new ConfigEngine();
