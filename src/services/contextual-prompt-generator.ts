/**
 * OCTI智能评估系统 - 上下文感知提示词生成器
 * 
 * 基于增强组织画像生成差异化的提示词
 */

import {
  EnhancedOrganizationProfile,
  GenerationContext,
  EvaluationAngle,
  PromptTemplate
} from '@/types/enhanced-organization-profile';
import { EvaluationAngleMatrix } from './evaluation-angle-matrix';

type OCTIDimension = 'SF' | 'IT' | 'MV' | 'AD';

/**
 * 上下文感知的提示词生成器
 */
export class ContextualPromptGenerator {
  private promptTemplates: Map<string, PromptTemplate> = new Map();
  private angleMatrix: EvaluationAngleMatrix;

  constructor() {
    this.angleMatrix = new EvaluationAngleMatrix();
    this.initializePromptTemplates();
  }

  /**
   * 基于增强画像生成差异化提示词
   */
  generateContextualPrompt(
    dimension: OCTIDimension,
    profile: EnhancedOrganizationProfile,
    context: GenerationContext
  ): string {
    console.log(`🎯 为${dimension}维度生成上下文感知提示词...`);

    // 1. 选择最适合的提示词模板
    const template = this.selectOptimalTemplate(dimension, profile);

    // 2. 选择评估角度
    const angles = this.angleMatrix.getOptimalAngles(dimension, profile, context);

    // 3. 生成个性化上下文
    const personalizedContext = this.generatePersonalizedContext(profile, angles);

    // 4. 构建最终提示词
    const finalPrompt = this.buildFinalPrompt(template, personalizedContext, angles, context);

    console.log(`✅ ${dimension}维度提示词生成完成，长度: ${finalPrompt.length}字符`);

    return finalPrompt;
  }

  /**
   * 选择最优提示词模板
   */
  private selectOptimalTemplate(dimension: OCTIDimension, profile: EnhancedOrganizationProfile): PromptTemplate {
    const templateKey = this.calculateTemplateKey(dimension, profile);
    return this.promptTemplates.get(templateKey) || this.getDefaultTemplate(dimension);
  }

  /**
   * 计算模板键值
   */
  private calculateTemplateKey(dimension: OCTIDimension, profile: EnhancedOrganizationProfile): string {
    // 基于组织特征计算最适合的模板
    const characteristics = profile.detailedCharacteristics;
    
    let templateType = 'standard';
    
    // 基于复杂度选择模板类型
    if (profile.organizationScale === '大型' || profile.organizationScale === '超大型') {
      templateType = 'advanced';
    } else if (profile.organizationScale === '微型' || profile.developmentStage === '初创期') {
      templateType = 'basic';
    }

    // 基于特殊特征调整
    if (characteristics.operationalProfile.innovationOrientation === 'pioneer') {
      templateType += '_innovative';
    }
    
    if (profile.operatingModel === '倡导型') {
      templateType += '_advocacy';
    }

    return `${dimension}_${templateType}`;
  }

  /**
   * 获取默认模板
   */
  private getDefaultTemplate(dimension: OCTIDimension): PromptTemplate {
    return this.promptTemplates.get(`${dimension}_standard`) || {
      id: `${dimension}_default`,
      name: `${dimension}维度默认模板`,
      dimension,
      template: this.getBasicTemplate(dimension),
      applicableProfiles: ['all'],
      variables: ['organizationType', 'serviceArea', 'developmentStage'],
      complexity: 'basic'
    };
  }

  /**
   * 生成个性化上下文
   */
  private generatePersonalizedContext(
    profile: EnhancedOrganizationProfile,
    angles: EvaluationAngle[]
  ): string {
    const context = [];

    // 组织基本信息（强化服务领域约束）
    context.push(`组织类型：${profile.organizationType}`);
    const serviceAreas = Array.isArray(profile.serviceArea) ? profile.serviceArea : [profile.serviceArea];
    context.push(`核心服务领域：${serviceAreas.join('、')}`);
    context.push(`组织规模：${profile.organizationScale}`);
    context.push(`发展阶段：${profile.developmentStage}`);
    context.push(`运营模式：${profile.operatingModel}`);
    context.push(`影响定位：${profile.impactPositioning}`);
    context.push(`组织文化：${profile.organizationalCulture}`);

    // 详细特征
    const chars = profile.detailedCharacteristics;
    context.push(`\n组织特征分析：`);
    context.push(`- 资金稳定性：${chars.resourceProfile.fundingStability}`);
    context.push(`- 决策风格：${chars.operationalProfile.decisionMakingStyle}`);
    context.push(`- 创新导向：${chars.operationalProfile.innovationOrientation}`);
    context.push(`- 风险承受：${chars.operationalProfile.riskTolerance}`);
    context.push(`- 影响力测量成熟度：${chars.impactProfile.measurementMaturity}`);
    context.push(`- 利益相关者参与：${chars.impactProfile.stakeholderEngagement}`);

    // 发展特征
    if (chars.developmentProfile.strategicPriorities.length > 0) {
      context.push(`\n战略重点：${chars.developmentProfile.strategicPriorities.join('、')}`);
    }

    if (chars.developmentProfile.challengeAreas.length > 0) {
      context.push(`挑战领域：${chars.developmentProfile.challengeAreas.join('、')}`);
    }

    // 强化服务领域约束
    context.push(`\n🎯 重要约束条件：`);
    context.push(`1. 所有问题必须紧密围绕【${serviceAreas.join('、')}】领域设计`);
    context.push(`2. 问题场景、案例、工具都必须与该服务领域高度相关`);
    context.push(`3. 避免涉及其他不相关的服务领域（如：${this.getIrrelevantAreas(serviceAreas).join('、')}等）`);
    context.push(`4. 所有提及的工具、平台、方法都应该是中国本土化的或在中国广泛使用的`);

    // 评估角度
    context.push(`\n本次评估角度：`);
    angles.forEach(angle => {
      context.push(`- ${angle.name}：${angle.description}`);
    });

    return context.join('\n');
  }

  /**
   * 获取不相关的服务领域（用于约束）
   */
  private getIrrelevantAreas(currentAreas: string[]): string[] {
    const allAreas = [
      '医疗健康', '教育', '环境保护', '扶贫济困', '养老服务',
      '儿童保护', '妇女权益', '残障服务', '社区发展', '文化艺术',
      '体育运动', '科技创新', '法律援助', '心理健康', '动物保护'
    ];

    return allAreas.filter(area => !currentAreas.some(current =>
      current.includes(area) || area.includes(current)
    )).slice(0, 3); // 只返回前3个作为示例
  }

  /**
   * 构建最终提示词
   */
  private buildFinalPrompt(
    template: PromptTemplate,
    personalizedContext: string,
    angles: EvaluationAngle[],
    context: GenerationContext
  ): string {
    // 替换模板变量
    let prompt = template.template;
    
    // 基本变量替换
    prompt = prompt.replace(/\{personalizedContext\}/g, personalizedContext);
    prompt = prompt.replace(/\{questionCount\}/g, context.questionCount.toString());
    prompt = prompt.replace(/\{dimension\}/g, context.dimension);

    // 添加角度特定的指导
    const angleGuidance = this.generateAngleGuidance(angles);
    prompt = prompt.replace(/\{angleGuidance\}/g, angleGuidance);

    // 添加多样性要求
    const diversityRequirements = this.generateDiversityRequirements(context);
    prompt = prompt.replace(/\{diversityRequirements\}/g, diversityRequirements);

    // 添加避免模式
    if (context.avoidancePatterns.length > 0) {
      const avoidanceGuidance = `\n⚠️ 避免生成包含以下内容的问题：\n${context.avoidancePatterns.map(p => `- ${p}`).join('\n')}`;
      prompt += avoidanceGuidance;
    }

    return prompt;
  }

  /**
   * 生成角度指导
   */
  private generateAngleGuidance(angles: EvaluationAngle[]): string {
    const guidance = [];
    
    guidance.push('请从以下评估角度生成问题：');
    angles.forEach((angle, index) => {
      guidance.push(`${index + 1}. ${angle.name}：${angle.description}`);
      guidance.push(`   推荐问题风格：${angle.questionStyles.join('、')}`);
      guidance.push(`   复杂度要求：${angle.complexityLevel}`);
    });

    return guidance.join('\n');
  }

  /**
   * 生成多样性要求
   */
  private generateDiversityRequirements(context: GenerationContext): string {
    const requirements = [];

    requirements.push('🎯 多样性要求：');
    requirements.push('1. 每个问题都应该从不同角度评估同一维度');
    requirements.push('2. 问题类型要多样化：直接询问、情境判断、案例分析、比较选择等');
    requirements.push('3. 表达方式要有变化：疑问句、陈述句、假设句等');
    requirements.push('4. 评估层次要不同：操作层面、管理层面、战略层面');
    requirements.push('5. 时间维度要考虑：过去经验、现状评估、未来规划');

    // 添加中国本土化要求
    requirements.push('\n🇨🇳 中国本土化要求：');
    requirements.push('1. 所有提及的工具、平台、软件都应该是中国本土的或在中国广泛使用的');
    requirements.push('2. 推荐使用的工具示例：微信、钉钉、腾讯会议、石墨文档、金山文档、飞书等');
    requirements.push('3. 避免提及：Slack、Zoom、Google Workspace、Microsoft Teams等国外工具');
    requirements.push('4. 法规政策应参考中国相关法律法规和政策环境');
    requirements.push('5. 案例和情境应符合中国公益机构的实际运营环境');

    // 添加服务领域严格约束
    requirements.push('\n⚠️ 服务领域严格约束：');
    requirements.push('1. 问题内容必须100%围绕组织的核心服务领域设计');
    requirements.push('2. 所有案例、场景、工具都必须与该服务领域直接相关');
    requirements.push('3. 绝对不能涉及其他不相关的服务领域');
    requirements.push('4. 如果是环保机构，就只能涉及环保相关内容，不能涉及医疗、教育等');
    requirements.push('5. 如果是教育机构，就只能涉及教育相关内容，不能涉及医疗、环保等');
    requirements.push('6. 如果是公益咨询机构，就只能涉及咨询服务、能力建设、治理优化等');
    requirements.push('7. 特别注意：民间组织不等于医疗机构，不要默认生成医疗相关问题');

    // 添加随机性要求
    requirements.push(`\n🎲 随机性要求（生成ID：${context.generationId}）：`);
    requirements.push('1. 请确保每次生成的问题都有所不同，避免重复或过于相似');
    requirements.push('2. 结合当前时间戳和生成ID确保问题的独特性');
    requirements.push('3. 每个问题都应该有独特的表达方式和评估角度');

    return requirements.join('\n');
  }

  /**
   * 初始化提示词模板
   */
  private initializePromptTemplates(): void {
    this.promptTemplates = new Map();

    // SF维度模板
    this.promptTemplates.set('SF_standard', {
      id: 'SF_standard',
      name: 'SF维度标准模板',
      dimension: 'SF',
      template: `基于以下组织画像，请生成{questionCount}道针对性的SF维度（战略聚焦度）评估问题：

{personalizedContext}

{angleGuidance}

{diversityRequirements}

请确保问题能够有效评估组织在战略聚焦方面的能力特征。`,
      applicableProfiles: ['all'],
      variables: ['personalizedContext', 'questionCount', 'angleGuidance', 'diversityRequirements'],
      complexity: 'basic'
    });

    // IT维度模板
    this.promptTemplates.set('IT_standard', {
      id: 'IT_standard',
      name: 'IT维度标准模板',
      dimension: 'IT',
      template: `基于以下组织画像，请生成{questionCount}道针对性的IT维度（团队协同度）评估问题：

{personalizedContext}

{angleGuidance}

{diversityRequirements}

请确保问题能够有效评估组织在团队协同方面的能力特征。`,
      applicableProfiles: ['all'],
      variables: ['personalizedContext', 'questionCount', 'angleGuidance', 'diversityRequirements'],
      complexity: 'basic'
    });

    // MV维度模板
    this.promptTemplates.set('MV_standard', {
      id: 'MV_standard',
      name: 'MV维度标准模板',
      dimension: 'MV',
      template: `基于以下组织画像，请生成{questionCount}道针对性的MV维度（价值导向度）评估问题：

{personalizedContext}

{angleGuidance}

{diversityRequirements}

请确保问题能够有效评估组织在价值导向方面的能力特征。`,
      applicableProfiles: ['all'],
      variables: ['personalizedContext', 'questionCount', 'angleGuidance', 'diversityRequirements'],
      complexity: 'basic'
    });

    // AD维度模板
    this.promptTemplates.set('AD_standard', {
      id: 'AD_standard',
      name: 'AD维度标准模板',
      dimension: 'AD',
      template: `基于以下组织画像，请生成{questionCount}道针对性的AD维度（能力发展度）评估问题：

{personalizedContext}

{angleGuidance}

{diversityRequirements}

请确保问题能够有效评估组织在能力发展方面的能力特征。`,
      applicableProfiles: ['all'],
      variables: ['personalizedContext', 'questionCount', 'angleGuidance', 'diversityRequirements'],
      complexity: 'basic'
    });
  }

  /**
   * 获取基础模板
   */
  private getBasicTemplate(dimension: OCTIDimension): string {
    const dimensionNames = {
      SF: '战略聚焦度',
      IT: '团队协同度',
      MV: '价值导向度',
      AD: '能力发展度'
    };

    return `请基于组织画像生成{questionCount}道${dimensionNames[dimension]}评估问题。

{personalizedContext}

{diversityRequirements}

请确保问题针对性强，能够有效评估组织在该维度的能力特征。`;
  }
}
