/**
 * OCTI智能评估系统 - 评估角度矩阵
 * 
 * 定义不同维度的评估角度，支持上下文感知的角度选择
 */

import {
  EvaluationAngle,
  EnhancedOrganizationProfile,
  GenerationContext
} from '@/types/enhanced-organization-profile';

type OCTIDimension = 'SF' | 'IT' | 'MV' | 'AD';

/**
 * 评估角度矩阵管理器
 */
export class EvaluationAngleMatrix {
  private angles: Map<OCTIDimension, EvaluationAngle[]> = new Map();

  constructor() {
    this.initializeAngles();
  }

  /**
   * 获取最优评估角度组合
   */
  getOptimalAngles(
    dimension: OCTIDimension,
    profile: EnhancedOrganizationProfile,
    context: GenerationContext
  ): EvaluationAngle[] {
    const availableAngles = this.angles.get(dimension) || [];
    
    console.log(`🎯 为${dimension}维度选择评估角度，可用角度: ${availableAngles.length}个`);

    // 基于组织特征筛选适用角度
    const applicableAngles = availableAngles.filter(angle => 
      this.isAngleApplicable(angle, profile, context)
    );

    console.log(`✅ 筛选出适用角度: ${applicableAngles.length}个`);

    // 随机选择不重复的角度组合
    const selectedAngles = this.selectDiverseAngles(applicableAngles, context.questionCount);

    console.log(`🎲 最终选择角度: ${selectedAngles.map(a => a.name).join(', ')}`);

    return selectedAngles;
  }

  /**
   * 判断角度是否适用于当前组织
   */
  private isAngleApplicable(
    angle: EvaluationAngle,
    profile: EnhancedOrganizationProfile,
    context: GenerationContext
  ): boolean {
    // 检查上下文标签匹配
    const hasMatchingContext = angle.applicableContexts.some(ctx => 
      profile.contextTags.includes(ctx) || 
      this.isContextuallyRelevant(ctx, profile)
    );

    // 检查复杂度匹配
    const complexityMatch = this.isComplexityAppropriate(angle, profile);

    // 检查避免模式
    const notInAvoidancePattern = !context.avoidancePatterns.some(pattern => 
      angle.description.toLowerCase().includes(pattern.toLowerCase())
    );

    return hasMatchingContext && complexityMatch && notInAvoidancePattern;
  }

  /**
   * 检查上下文相关性
   */
  private isContextuallyRelevant(context: string, profile: EnhancedOrganizationProfile): boolean {
    const contextMappings: Record<string, (profile: EnhancedOrganizationProfile) => boolean> = {
      'mature_org': (p) => p.developmentStage === '成熟期',
      'growth_stage': (p) => p.developmentStage === '成长期',
      'startup_stage': (p) => p.developmentStage === '初创期',
      'large_scale': (p) => ['大型', '超大型'].includes(p.organizationScale),
      'small_scale': (p) => ['微型', '小型'].includes(p.organizationScale),
      'resource_constrained': (p) => p.detailedCharacteristics.resourceProfile.fundingStability === 'uncertain',
      'well_funded': (p) => p.detailedCharacteristics.resourceProfile.fundingStability === 'stable',
      'high_innovation': (p) => p.detailedCharacteristics.operationalProfile.innovationOrientation === 'pioneer',
      'collaborative_culture': (p) => p.organizationalCulture === '协作共享',
      'mission_driven': (p) => p.organizationalCulture === '使命驱动',
      'advocacy_focused': (p) => p.operatingModel === '倡导型',
      'service_focused': (p) => p.operatingModel === '直接服务',
      'foundation_type': (p) => p.organizationType === '基金会'
    };

    const checker = contextMappings[context];
    return checker ? checker(profile) : false;
  }

  /**
   * 检查复杂度是否合适
   */
  private isComplexityAppropriate(angle: EvaluationAngle, profile: EnhancedOrganizationProfile): boolean {
    const orgComplexity = this.calculateOrganizationComplexity(profile);
    
    const complexityMapping = {
      'basic': 1,
      'intermediate': 2,
      'advanced': 3
    };

    const angleComplexity = complexityMapping[angle.complexityLevel];
    
    // 允许组织复杂度±1的角度
    return Math.abs(orgComplexity - angleComplexity) <= 1;
  }

  /**
   * 计算组织复杂度
   */
  private calculateOrganizationComplexity(profile: EnhancedOrganizationProfile): number {
    let complexity = 1; // 基础复杂度

    // 基于规模调整
    const scaleMapping = {
      '微型': 0,
      '小型': 0,
      '中型': 1,
      '大型': 2,
      '超大型': 2
    };
    complexity += scaleMapping[profile.organizationScale as keyof typeof scaleMapping] || 1;

    // 基于发展阶段调整
    const stageMapping = {
      '初创期': 0,
      '成长期': 1,
      '成熟期': 2,
      '转型期': 2,
      '扩张期': 1
    };
    complexity += stageMapping[profile.developmentStage as keyof typeof stageMapping] || 1;

    return Math.min(Math.max(complexity / 2, 1), 3); // 归一化到1-3
  }

  /**
   * 选择多样化的角度组合
   */
  private selectDiverseAngles(angles: EvaluationAngle[], count: number): EvaluationAngle[] {
    if (angles.length <= count) {
      return angles;
    }

    // 按权重和复杂度排序
    const sortedAngles = [...angles].sort((a, b) => {
      // 优先选择权重高的角度
      if (a.weight !== b.weight) {
        return b.weight - a.weight;
      }
      // 其次考虑复杂度多样性
      return Math.random() - 0.5; // 随机化
    });

    // 确保复杂度分布均匀
    const selected: EvaluationAngle[] = [];
    const complexityGroups = {
      basic: sortedAngles.filter(a => a.complexityLevel === 'basic'),
      intermediate: sortedAngles.filter(a => a.complexityLevel === 'intermediate'),
      advanced: sortedAngles.filter(a => a.complexityLevel === 'advanced')
    };

    // 轮流从不同复杂度组选择
    const groups = Object.values(complexityGroups).filter(group => group.length > 0);
    let groupIndex = 0;

    while (selected.length < count && groups.some(group => group.length > 0)) {
      const currentGroup = groups[groupIndex % groups.length];
      if (currentGroup.length > 0) {
        const angle = currentGroup.shift()!;
        selected.push(angle);
      }
      groupIndex++;
    }

    return selected;
  }

  /**
   * 初始化所有维度的评估角度
   */
  private initializeAngles(): void {
    this.angles = new Map();

    // SF维度：战略聚焦度
    this.angles.set('SF', [
      {
        id: 'strategic_planning',
        name: '战略规划视角',
        description: '从长期战略规划和目标设定角度评估组织的聚焦程度',
        applicableContexts: ['mature_org', 'growth_stage', 'large_scale'],
        questionStyles: ['scenario_analysis', 'priority_ranking', 'strategic_choice'],
        complexityLevel: 'advanced',
        weight: 0.9
      },
      {
        id: 'resource_allocation',
        name: '资源配置视角',
        description: '从资源分配效率和聚焦度角度评估组织能力',
        applicableContexts: ['resource_constrained', 'growth_stage', 'small_scale'],
        questionStyles: ['trade_off_analysis', 'efficiency_measurement', 'allocation_choice'],
        complexityLevel: 'intermediate',
        weight: 0.8
      },
      {
        id: 'competitive_positioning',
        name: '竞争定位视角',
        description: '从行业竞争地位和差异化角度评估组织聚焦',
        applicableContexts: ['mature_org', 'large_scale', 'high_innovation'],
        questionStyles: ['comparative_analysis', 'positioning_assessment', 'differentiation_choice'],
        complexityLevel: 'advanced',
        weight: 0.7
      },
      {
        id: 'service_specialization',
        name: '服务专业化视角',
        description: '从服务专业化程度和深度角度评估聚焦能力',
        applicableContexts: ['service_focused', 'mature_org', 'foundation_type'],
        questionStyles: ['depth_assessment', 'specialization_measurement', 'expertise_evaluation'],
        complexityLevel: 'intermediate',
        weight: 0.8
      },
      {
        id: 'impact_concentration',
        name: '影响力集中视角',
        description: '从影响力集中度和效果角度评估战略聚焦',
        applicableContexts: ['advocacy_focused', 'large_scale', 'mission_driven'],
        questionStyles: ['impact_analysis', 'concentration_measurement', 'effectiveness_assessment'],
        complexityLevel: 'advanced',
        weight: 0.9
      }
    ]);

    // IT维度：团队协同度
    this.angles.set('IT', [
      {
        id: 'decision_making',
        name: '决策机制视角',
        description: '从决策制定过程和参与度角度评估团队协同',
        applicableContexts: ['collaborative_culture', 'large_scale', 'mature_org'],
        questionStyles: ['process_analysis', 'participation_assessment', 'mechanism_evaluation'],
        complexityLevel: 'intermediate',
        weight: 0.9
      },
      {
        id: 'communication_flow',
        name: '沟通流程视角',
        description: '从内部沟通效率和透明度角度评估协同能力',
        applicableContexts: ['large_scale', 'growth_stage', 'collaborative_culture'],
        questionStyles: ['flow_analysis', 'efficiency_measurement', 'transparency_assessment'],
        complexityLevel: 'basic',
        weight: 0.8
      },
      {
        id: 'knowledge_sharing',
        name: '知识共享视角',
        description: '从知识管理和经验传承角度评估团队协同',
        applicableContexts: ['mature_org', 'large_scale', 'high_innovation'],
        questionStyles: ['sharing_assessment', 'knowledge_evaluation', 'learning_measurement'],
        complexityLevel: 'advanced',
        weight: 0.7
      },
      {
        id: 'stakeholder_coordination',
        name: '利益相关者协调视角',
        description: '从外部利益相关者协调角度评估协同能力',
        applicableContexts: ['advocacy_focused', 'foundation_type', 'large_scale'],
        questionStyles: ['coordination_analysis', 'relationship_assessment', 'engagement_evaluation'],
        complexityLevel: 'advanced',
        weight: 0.8
      },
      {
        id: 'team_dynamics',
        name: '团队动力视角',
        description: '从团队凝聚力和协作文化角度评估协同度',
        applicableContexts: ['small_scale', 'collaborative_culture', 'mission_driven'],
        questionStyles: ['dynamics_assessment', 'culture_evaluation', 'cohesion_measurement'],
        complexityLevel: 'basic',
        weight: 0.9
      }
    ]);

    // MV维度：价值导向度
    this.angles.set('MV', [
      {
        id: 'mission_alignment',
        name: '使命一致性视角',
        description: '从使命驱动和价值一致性角度评估组织导向',
        applicableContexts: ['mission_driven', 'foundation_type', 'advocacy_focused'],
        questionStyles: ['alignment_assessment', 'consistency_evaluation', 'mission_measurement'],
        complexityLevel: 'intermediate',
        weight: 0.9
      },
      {
        id: 'value_creation',
        name: '价值创造视角',
        description: '从社会价值创造和影响力角度评估价值导向',
        applicableContexts: ['service_focused', 'mature_org', 'large_scale'],
        questionStyles: ['value_analysis', 'impact_assessment', 'creation_evaluation'],
        complexityLevel: 'advanced',
        weight: 0.8
      },
      {
        id: 'stakeholder_value',
        name: '利益相关者价值视角',
        description: '从多元利益相关者价值平衡角度评估导向',
        applicableContexts: ['collaborative_culture', 'advocacy_focused', 'large_scale'],
        questionStyles: ['balance_analysis', 'stakeholder_assessment', 'value_distribution'],
        complexityLevel: 'advanced',
        weight: 0.7
      },
      {
        id: 'sustainability_focus',
        name: '可持续发展视角',
        description: '从长期可持续性和责任角度评估价值导向',
        applicableContexts: ['mature_org', 'well_funded', 'foundation_type'],
        questionStyles: ['sustainability_assessment', 'responsibility_evaluation', 'longevity_analysis'],
        complexityLevel: 'advanced',
        weight: 0.8
      },
      {
        id: 'ethical_foundation',
        name: '伦理基础视角',
        description: '从伦理原则和道德标准角度评估价值导向',
        applicableContexts: ['mission_driven', 'advocacy_focused', 'collaborative_culture'],
        questionStyles: ['ethical_assessment', 'principle_evaluation', 'moral_measurement'],
        complexityLevel: 'intermediate',
        weight: 0.9
      }
    ]);

    // AD维度：能力发展度
    this.angles.set('AD', [
      {
        id: 'learning_adaptation',
        name: '学习适应视角',
        description: '从组织学习和环境适应角度评估发展能力',
        applicableContexts: ['high_innovation', 'growth_stage', 'startup_stage'],
        questionStyles: ['learning_assessment', 'adaptation_evaluation', 'flexibility_measurement'],
        complexityLevel: 'intermediate',
        weight: 0.9
      },
      {
        id: 'capacity_building',
        name: '能力建设视角',
        description: '从内部能力建设和发展角度评估组织发展',
        applicableContexts: ['growth_stage', 'resource_constrained', 'small_scale'],
        questionStyles: ['capacity_analysis', 'development_assessment', 'building_evaluation'],
        complexityLevel: 'basic',
        weight: 0.8
      },
      {
        id: 'innovation_development',
        name: '创新发展视角',
        description: '从创新能力和发展潜力角度评估组织发展',
        applicableContexts: ['high_innovation', 'startup_stage', 'collaborative_culture'],
        questionStyles: ['innovation_assessment', 'creativity_evaluation', 'potential_measurement'],
        complexityLevel: 'advanced',
        weight: 0.7
      },
      {
        id: 'systematic_improvement',
        name: '系统改进视角',
        description: '从系统性改进和流程优化角度评估发展能力',
        applicableContexts: ['mature_org', 'large_scale', 'well_funded'],
        questionStyles: ['system_analysis', 'improvement_assessment', 'optimization_evaluation'],
        complexityLevel: 'advanced',
        weight: 0.8
      },
      {
        id: 'resilience_development',
        name: '韧性发展视角',
        description: '从组织韧性和抗风险能力角度评估发展度',
        applicableContexts: ['resource_constrained', 'mature_org', 'advocacy_focused'],
        questionStyles: ['resilience_assessment', 'risk_evaluation', 'stability_measurement'],
        complexityLevel: 'intermediate',
        weight: 0.9
      }
    ]);
  }
}
