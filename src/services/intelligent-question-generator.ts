/**
 * OCTI智能问题生成器
 *
 * 基于组织画像和question_design_prompt.json配置，
 * 真正动态生成个性化的28道智能题目
 */

import { Question } from '@/components/questionnaire/question-renderer';

// 增强的组织画像接口
export interface OrganizationProfile {
  // 第一优先级：用户明确输入
  userSpecifiedFocusArea?: string; // 主要关注领域（用户填写）

  // 第二优先级：系统推断
  organizationType: string;
  serviceArea: string[]; // 推断的服务领域
  organizationScale: string;
  developmentStage: string;
  operatingModel: string;
  impactPositioning: string;
  organizationalCulture: string;

  // 补充信息
  mission?: string;
  challenges?: string[];
  goals?: string[];
}

// 智能生成配置
interface IntelligentGenerationConfig {
  version: string;
  questionsPerDimension: number;
  totalIntelligentQuestions: number;
  contextualAdaptation: Record<string, Record<string, string>>;
  nonprofitFocusAreas: string[];
}

// 维度类型
type OCTIDimension = 'SF' | 'IT' | 'MV' | 'AD';

/**
 * 智能问题生成器类
 */
export class IntelligentQuestionGenerator {
  private config: IntelligentGenerationConfig;

  constructor() {
    // 从question_design_prompt.json加载配置
    this.config = {
      version: '4.0.0',
      questionsPerDimension: 7,
      totalIntelligentQuestions: 28,
      contextualAdaptation: {
        serviceArea: {
          教育: '关注教育公平、学习成果、师资发展、教育创新',
          环保: '关注环境保护、可持续发展、生态修复、绿色倡导',
          扶贫: '关注贫困减缓、能力建设、可持续脱贫、社区发展',
          医疗: '关注健康促进、医疗可及性、疾病预防、健康教育',
          养老: '关注老年关怀、养老服务、代际关系、老龄化应对',
          儿童: '关注儿童保护、儿童发展、教育支持、权益维护',
          公益咨询: '关注公益机构能力建设、战略规划、治理优化、专业咨询服务',
          社会服务: '关注综合性社会服务、社区发展、公共服务、社会治理',
          文化艺术: '关注文化传承、艺术推广、创意产业、文化教育',
          科技创新: '关注科技普及、数字公益、创新孵化、技术赋能',
        },
        developmentStage: {
          初创期: '关注基础能力建设、团队组建、资源获取、项目启动',
          成长期: '关注规模扩张、流程优化、品牌建设、影响力提升',
          成熟期: '关注可持续发展、创新转型、深度影响、行业引领',
          转型期: '关注战略调整、模式创新、能力重构、风险管控',
        },
        operatingModel: {
          直接服务: '关注服务质量、受益者满意度、服务创新、规模效应',
          资助型: '关注资助策略、项目筛选、监督评估、资源配置',
          倡导型: '关注政策影响、公众参与、议题设置、联盟建设',
        },
      },
      nonprofitFocusAreas: [
        '使命驱动特性',
        '利益相关者管理',
        '社会影响力测量',
        '透明度与问责',
        '志愿者管理',
        '资源筹集能力',
        '可持续发展',
        '治理结构',
      ],
    };
  }

  /**
   * 获取主要关注领域（按优先级）
   */
  private getPrimaryFocusArea(profile: OrganizationProfile): string {
    // 1. 优先使用用户明确填写的关注领域
    if (profile.userSpecifiedFocusArea?.trim()) {
      console.log(`✅ 使用用户指定的关注领域: ${profile.userSpecifiedFocusArea}`);
      return profile.userSpecifiedFocusArea.trim();
    }

    // 2. 其次使用推断的服务领域
    if (profile.serviceArea?.length > 0) {
      console.log(`📊 使用推断的服务领域: ${profile.serviceArea[0]}`);
      return profile.serviceArea[0];
    }

    // 3. 最后使用默认配置
    console.log(`⚠️ 使用默认服务领域: 社会服务`);
    return '社会服务';
  }

  /**
   * 基于组织画像生成28道智能题目
   */
  async generateIntelligentQuestions(profile: OrganizationProfile): Promise<Question[]> {
    // 获取主要关注领域（按优先级）
    const primaryFocusArea = this.getPrimaryFocusArea(profile);
    console.log(`🎯 智能问题生成将围绕核心领域: ${primaryFocusArea}`);

    const dimensions: OCTIDimension[] = ['SF', 'IT', 'MV', 'AD'];
    const allQuestions: Question[] = [];

    for (const dimension of dimensions) {
      const dimensionQuestions = await this.generateDimensionQuestions(
        dimension,
        profile,
        this.config.questionsPerDimension
      );
      allQuestions.push(...dimensionQuestions);
    }

    return allQuestions;
  }

  /**
   * 为特定维度生成题目
   */
  private async generateDimensionQuestions(
    dimension: OCTIDimension,
    profile: OrganizationProfile,
    count: number
  ): Promise<Question[]> {
    const contextualPrompt = this.buildContextualPrompt(dimension, profile);

    // 直接尝试LLM生成，如果失败则降级到模板生成
    try {
      console.log(`🤖 为${dimension}维度调用LLM生成${count}道智能题目...`);
      return await this.generateQuestionsWithLLM(dimension, profile, count, contextualPrompt);
    } catch (error) {
      console.warn(`⚠️ ${dimension}维度LLM生成失败，降级到模板生成:`, error);
      return this.generateQuestionsFromTemplate(dimension, profile, count);
    }
  }

  /**
   * 使用LLM生成题目（通过增强API调用）
   */
  private async generateQuestionsWithLLM(
    dimension: OCTIDimension,
    profile: OrganizationProfile,
    count: number,
    contextualPrompt: string
  ): Promise<Question[]> {
    // 创建AbortController用于超时控制
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 360000); // 6分钟超时（方案C需要更多时间，比LLM超时稍长）

    try {
      console.log(`🚀 调用方案C增强API生成${dimension}维度问题...`);

      const response = await fetch('/api/questionnaire/generate-intelligent', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          dimension,
          profile,
          count,
          contextualPrompt,
          generationOptions: {
            previousAttempts: 0,
            avoidancePatterns: [],
            preferredStyles: []
          }
        }),
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ error: 'Unknown error' }));
        throw new Error(errorData.error || `增强API调用失败: ${response.status}`);
      }

      const result = await response.json();

      if (!result.success) {
        throw new Error(result.error || '方案C智能问题生成失败');
      }

      const questions = result.data.questions || [];
      const enhancedProfile = result.data.enhancedProfile;
      const generationStats = result.data.generationStats;

      console.log(`✅ 方案C ${dimension}维度生成成功:`, questions.length, '道题目');
      console.log(`📊 生成统计:`, {
        uniquenessScore: generationStats?.uniquenessScore?.toFixed(3),
        diversityScore: generationStats?.diversityScore?.toFixed(3),
        generationTime: generationStats?.generationTime + 'ms'
      });

      // 转换为标准Question格式
      return questions.map((q: any) => ({
        id: q.id,
        type: q.type,
        source: 'AI_GENERATED' as const,
        category: q.category,
        subCategory: '上下文感知生成',
        title: q.text,
        description: `基于增强组织画像智能生成的${dimension}维度评估问题（指纹：${enhancedProfile?.uniquenessFingerprint || 'unknown'}）`,
        options: q.options,
        required: true,
        order: q.order,
        metadata: q.metadata
      }));
    } catch (error) {
      clearTimeout(timeoutId);
      if (error instanceof Error && error.name === 'AbortError') {
        throw new Error(`${dimension}维度题目生成超时`);
      }
      throw error;
    }
  }

  /**
   * 构建上下文化提示词（优先使用用户指定的关注领域）
   */
  private buildContextualPrompt(dimension: OCTIDimension, profile: OrganizationProfile): string {
    // 获取主要关注领域（按优先级）
    const primaryFocusArea = this.getPrimaryFocusArea(profile);

    // 获取领域特定的上下文
    const focusAreaContext = this.config.contextualAdaptation?.serviceArea?.[primaryFocusArea] ||
      `专注于${primaryFocusArea}领域的专业服务和能力建设`;

    const stageContext =
      this.config.contextualAdaptation?.developmentStage?.[profile.developmentStage] || '组织发展';
    const modelContext =
      this.config.contextualAdaptation?.operatingModel?.[profile.operatingModel] || '综合运营';

    // 判断关注领域来源
    const focusAreaSource = profile.userSpecifiedFocusArea ? '用户明确指定' : '系统推断';

    return `
你是一位专业的公益机构组织能力评估专家，正在为以下公益机构设计${dimension}维度的评估问题：

🎯 核心关注领域：${primaryFocusArea} (${focusAreaSource})
📋 领域特点：${focusAreaContext}

机构背景信息（仅作参考，不能覆盖核心关注领域）：
- 组织类型：${profile.organizationType || '公益组织'}
- 组织规模：${profile.organizationScale || '中型'}
- 发展阶段：${profile.developmentStage || '成长期'} (${stageContext})
- 运营模式：${profile.operatingModel || '综合服务型'} (${modelContext})
- 影响力定位：${profile.impactPositioning || '区域影响'}
- 组织文化：${profile.organizationalCulture || '使命驱动'}
- 使命：${profile.mission || '致力于社会公益事业'}

⚠️ 严格要求：
1. 所有问题必须100%围绕"${primaryFocusArea}"领域设计
2. 问题内容、案例、场景都必须与该领域直接相关
3. 绝对不能涉及其他不相关的服务领域（如医疗、教育等，除非与${primaryFocusArea}直接相关）
4. 体现${primaryFocusArea}领域的专业特点和独特挑战
5. 考虑该领域的行业标准和最佳实践

请基于"${primaryFocusArea}"领域的专业特点，生成针对性的${dimension}维度评估问题。
问题应该：
1. 体现${primaryFocusArea}领域的专业术语和概念
2. 符合该发展阶段在${primaryFocusArea}领域的关注重点
3. 适应该组织规模在${primaryFocusArea}领域的管理复杂度
4. 针对${primaryFocusArea}领域的具体挑战和目标
5. 考虑${primaryFocusArea}领域的使命驱动特性
6. 反映${primaryFocusArea}领域的独特性和影响力测量需求

🎯 多样性要求：
- 每个问题都应该从${primaryFocusArea}领域的不同评估角度切入
- 问题表达方式要有变化，避免千篇一律
- 可以结合${primaryFocusArea}领域的具体场景和案例
- 考虑不同的时间维度：过去、现在、未来
- 体现不同的管理层次：执行、管理、战略
`;
  }

  /**
   * 基于模板生成题目（模拟LLM生成）
   */
  public generateQuestionsFromTemplate(
    dimension: OCTIDimension,
    profile: OrganizationProfile,
    count: number
  ): Question[] {
    const templates = this.getQuestionTemplates(dimension);
    const questions: Question[] = [];

    // 增加随机性：打乱模板顺序
    const shuffledTemplates = [...templates].sort(() => Math.random() - 0.5);

    for (let i = 0; i < count; i++) {
      const template = shuffledTemplates[i % shuffledTemplates.length];
      const question = this.personalizeQuestion(template, profile, dimension, i + 1);
      questions.push(question);
    }

    return questions;
  }

  /**
   * 获取维度特定的题目模板
   */
  private getQuestionTemplates(dimension: OCTIDimension): any[] {
    const templates = {
      SF: [
        {
          title: '作为{organizationType}，您的组织在{serviceArea}领域的专业化程度如何？',
          description: '评估组织在特定服务领域的专业深度和聚焦程度。',
          type: 'SCALE',
          options: { min: 1, max: 5, labels: ['很低', '较低', '一般', '较高', '很高'] },
        },
        {
          title: '在{developmentStage}阶段，您的组织如何平衡资源投入与社会影响力？',
          description: '了解组织在当前发展阶段的资源配置策略。',
          type: 'SINGLE_CHOICE',
          options: [
            { text: '优先投入核心项目，确保深度影响', value: 'depth_focus' },
            { text: '适度分散投入，扩大服务覆盖', value: 'breadth_focus' },
            { text: '根据资源状况灵活调整', value: 'flexible' },
            { text: '主要依据捐赠者意愿决定', value: 'donor_driven' },
            { text: '还没有明确的配置策略', value: 'unclear' },
          ],
        },
      ],
      IT: [
        {
          title: '作为{operatingModel}的公益机构，您如何协调不同利益相关者的需求？',
          description: '评估组织在多利益相关者环境中的协调能力。',
          type: 'MULTIPLE_CHOICE',
          options: [
            { text: '建立定期沟通机制', value: 'regular_communication' },
            { text: '设立利益相关者代表制度', value: 'representation_system' },
            { text: '通过透明的决策流程', value: 'transparent_process' },
            { text: '依靠个人关系维护', value: 'personal_relations' },
            { text: '主要关注主要资助方', value: 'major_funders' },
          ],
        },
      ],
      MV: [
        {
          title: '您的组织如何确保{serviceArea}服务始终与公益使命保持一致？',
          description: '了解组织在使命驱动方面的实践和机制。',
          type: 'SINGLE_CHOICE',
          options: [
            { text: '建立了使命导向的决策框架', value: 'mission_framework' },
            { text: '定期评估项目与使命的契合度', value: 'regular_assessment' },
            { text: '通过培训强化使命认知', value: 'training' },
            { text: '主要依靠领导者的价值引导', value: 'leadership' },
            { text: '还没有系统的保障机制', value: 'no_system' },
          ],
        },
      ],
      AD: [
        {
          title: '面对{serviceArea}领域的政策变化，您的组织通常如何应对？',
          description: '评估组织对外部环境变化的适应能力。',
          type: 'SINGLE_CHOICE',
          options: [
            { text: '提前研究政策趋势，主动调整', value: 'proactive' },
            { text: '密切关注变化，快速响应', value: 'responsive' },
            { text: '等待明确信号后再调整', value: 'reactive' },
            { text: '主要依靠合作伙伴指导', value: 'partner_guided' },
            { text: '较少关注政策变化', value: 'limited_attention' },
          ],
        },
      ],
    };

    return templates[dimension] || [];
  }

  /**
   * 个性化题目内容
   */
  private personalizeQuestion(
    template: any,
    profile: OrganizationProfile,
    dimension: OCTIDimension,
    order: number
  ): Question {
    let title = template.title;
    let description = template.description;

    // 安全获取服务领域
    const serviceArea = Array.isArray(profile.serviceArea)
      ? profile.serviceArea[0]
      : profile.serviceArea || '公益';

    // 替换占位符
    const replacements = {
      '{organizationType}': profile.organizationType || '公益组织',
      '{serviceArea}': serviceArea,
      '{organizationScale}': profile.organizationScale || '中型',
      '{developmentStage}': profile.developmentStage || '成长期',
      '{operatingModel}': profile.operatingModel || '直接服务',
      '{impactPositioning}': profile.impactPositioning || '区域影响',
      '{organizationalCulture}': profile.organizationalCulture || '使命驱动',
    };

    Object.entries(replacements).forEach(([key, value]) => {
      title = title.replace(new RegExp(key, 'g'), value);
      description = description.replace(new RegExp(key, 'g'), value);
    });

    // 添加随机性标识
    const randomId = Math.floor(Math.random() * 1000);

    return {
      id: `${dimension}_I${order.toString().padStart(3, '0')}_${randomId}`,
      type: template.type,
      source: 'AI_GENERATED',
      category: `${dimension} - ${this.getDimensionName(dimension)}`,
      subCategory: '智能生成',
      title,
      description: `基于您的${profile.organizationType}背景生成：${description}`,
      options: template.options,
      required: true,
      order: this.getBaseOrder(dimension) + order,
    };
  }

  /**
   * 获取维度名称
   */
  private getDimensionName(dimension: OCTIDimension): string {
    const names = {
      SF: '战略与财务',
      IT: '影响力与透明度',
      MV: '使命与价值观',
      AD: '适应性与发展',
    };
    return names[dimension];
  }

  /**
   * 获取维度基础序号
   */
  private getBaseOrder(dimension: OCTIDimension): number {
    const baseOrders = { SF: 100, IT: 200, MV: 300, AD: 400 };
    return baseOrders[dimension];
  }
}

/**
 * 创建智能问题生成器实例
 */
export const intelligentQuestionGenerator = new IntelligentQuestionGenerator();
