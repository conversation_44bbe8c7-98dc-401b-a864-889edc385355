/**
 * OCTI智能评估系统 - LLM API客户端
 *
 * 支持多个LLM服务提供商的统一接口
 * 支持多API Key负载均衡和故障转移
 */

import { apiKeyManager } from '../api-key-manager';

export interface LLMMessage {
  role: 'system' | 'user' | 'assistant';
  content: string;
}

export interface LLMRequest {
  model: string;
  messages: LLMMessage[];
  temperature?: number;
  max_tokens?: number;
  response_format?: { type: 'json_object' };
}

export interface LLMResponse {
  choices: Array<{
    message: {
      content: string;
      role: string;
    };
  }>;
  usage?: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}

/**
 * LLM API客户端
 */
export class LLMClient {
  private provider: 'deepseek' | 'minimax';
  private baseUrl: string;
  private useMultipleKeys: boolean;

  constructor(provider: 'deepseek' | 'minimax' = 'minimax', useMultipleKeys: boolean = true) {
    this.provider = provider;
    this.useMultipleKeys = useMultipleKeys;

    if (provider === 'deepseek') {
      this.baseUrl = 'https://api.deepseek.com/v1';
      // DeepSeek暂不支持多密钥，使用单密钥
      if (!process.env.DEEPSEEK_API_KEY) {
        throw new Error('缺少DEEPSEEK_API_KEY环境变量');
      }
    } else {
      this.baseUrl = 'https://api.minimax.chat/v1';
      // MiniMax支持多密钥模式
      if (useMultipleKeys) {
        const availableKeys = apiKeyManager.getAvailableCount();
        if (availableKeys === 0) {
          throw new Error('缺少有效的MINIMAX API密钥，请配置MINIMAX_API_KEY_1等环境变量');
        }
        console.log(`🚀 MiniMax客户端启用多密钥模式，可用密钥: ${availableKeys}个`);
      } else {
        if (!process.env.MINIMAX_API_KEY) {
          throw new Error('缺少MINIMAX_API_KEY环境变量');
        }
      }
    }
  }

  /**
   * 调用LLM API（支持多密钥负载均衡）
   */
  async call(request: LLMRequest): Promise<LLMResponse> {
    // 获取API密钥
    let apiKey: string;
    let keyId: string = 'single_key';

    if (this.provider === 'minimax' && this.useMultipleKeys) {
      // MiniMax多密钥模式
      const keyInfo = apiKeyManager.getNextApiKey();
      if (!keyInfo) {
        throw new Error('没有可用的MiniMax API密钥');
      }
      apiKey = keyInfo.key;
      keyId = keyInfo.keyId;
    } else if (this.provider === 'deepseek') {
      // DeepSeek单密钥模式
      apiKey = process.env.DEEPSEEK_API_KEY || '';
    } else {
      // MiniMax单密钥模式
      apiKey = process.env.MINIMAX_API_KEY || '';
    }

    try {
      console.log(`🤖 调用LLM API (${keyId}):`, {
        model: request.model,
        messages: request.messages.length,
        temperature: request.temperature,
      });

      // 根据提供商调整请求格式
      const requestBody =
        this.provider === 'minimax'
          ? {
              model: request.model || 'MiniMax-M1',
              messages: request.messages,
              temperature: request.temperature || 0.7,
              max_tokens: request.max_tokens || 12000, // 增加MiniMax的token限制
              top_p: 0.9,
              stream: false,
              // MiniMax不支持response_format参数，移除它
              ...(request.response_format ? {} : {}),
            }
          : {
              model: request.model || 'deepseek-reasoner',
              messages: request.messages,
              temperature: request.temperature || 0.3,
              max_tokens: request.max_tokens || 8000, // 增加DeepSeek的token限制
              // DeepSeek支持response_format参数
              ...(request.response_format && { response_format: request.response_format }),
            };

      // 根据提供商使用不同的端点
      const endpoint =
        this.provider === 'minimax'
          ? `${this.baseUrl}/text/chatcompletion_v2`
          : `${this.baseUrl}/chat/completions`;

      // 创建带超时的fetch请求
      const controller = new AbortController();
      // 根据模型设置不同的超时时间（专业版分析需要更长时间）
      const timeout = this.provider === 'deepseek' ? 480000 : 420000; // DeepSeek 8分钟，MiniMax 7分钟
      const timeoutId = setTimeout(() => controller.abort(), timeout);

      try {
        const response = await fetch(endpoint, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${apiKey}`,
          },
          body: JSON.stringify(requestBody),
          signal: controller.signal,
        });

        clearTimeout(timeoutId);

        if (!response.ok) {
          const errorText = await response.text();
          const errorMessage = `LLM API错误 (${response.status}): ${errorText}`;

          // 记录失败
          if (this.provider === 'minimax' && this.useMultipleKeys) {
            apiKeyManager.recordFailure(keyId, errorMessage);
          }

          throw new Error(errorMessage);
        }

        const result = await response.json();
        console.log(`✅ LLM API调用成功 (${keyId})`);

        // 记录成功
        if (this.provider === 'minimax' && this.useMultipleKeys) {
          apiKeyManager.recordSuccess(keyId);
        }

        return result;
      } catch (error: any) {
        clearTimeout(timeoutId);

        let errorMessage: string;
        if (error.name === 'AbortError') {
          const timeoutMinutes = this.provider === 'deepseek' ? '8分钟' : '7分钟';
          errorMessage = `LLM API调用超时（${timeoutMinutes}）`;
        } else {
          errorMessage = error.message || 'LLM API调用失败';
        }

        // 记录失败
        if (this.provider === 'minimax' && this.useMultipleKeys) {
          apiKeyManager.recordFailure(keyId, errorMessage);
        }

        throw new Error(errorMessage);
      }
    } catch (error) {
      console.error(`❌ LLM API调用失败 (${keyId}):`, error);
      throw error;
    }
  }

  /**
   * 解析JSON响应（带容错处理）
   */
  static parseJSONResponse(content: string): any {
    try {
      // 尝试直接解析
      return JSON.parse(content);
    } catch (e1) {
      try {
        // 尝试提取JSON代码块
        const jsonMatch = content.match(/```json\s*([\s\S]*?)\s*```/);
        if (jsonMatch && jsonMatch[1]) {
          return JSON.parse(jsonMatch[1]);
        }
      } catch (e2) {
        // 尝试修复常见JSON错误
        try {
          let fixed = content;
          // 移除尾随逗号
          fixed = fixed.replace(/,\s*}/g, '}');
          fixed = fixed.replace(/,\s*]/g, ']');
          // 修复未引用的键
          fixed = fixed.replace(/(\w+):/g, '"$1":');
          return JSON.parse(fixed);
        } catch (e3) {
          console.error('JSON解析失败:', content);
          throw new Error('无法解析LLM返回的JSON格式');
        }
      }
    }
    // 如果所有解析方法都失败，返回null
    return null;
  }
}

/**
 * 创建LLM客户端实例
 */
export function createLLMClient(
  provider: 'deepseek' | 'minimax' = 'deepseek',
  useMultipleKeys: boolean = true
): LLMClient {
  return new LLMClient(provider, useMultipleKeys);
}
