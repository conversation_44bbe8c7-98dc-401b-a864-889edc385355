/**
 * OCTI智能评估系统 - 组织画像分析器
 * 
 * 深度分析组织画像，提取细化特征，生成独特性指纹
 */

import crypto from 'crypto';
import { OrganizationProfile } from './intelligent-question-generator';
import {
  EnhancedOrganizationProfile,
  DetailedCharacteristics,
  ResourceProfile,
  OperationalProfile,
  ImpactProfile,
  DevelopmentProfile
} from '@/types/enhanced-organization-profile';

/**
 * 组织画像分析器
 */
export class OrganizationProfileAnalyzer {
  private readonly analysisVersion = '1.0.0';

  /**
   * 深度分析组织画像，提取细化特征
   */
  async analyzeProfile(basicProfile: OrganizationProfile): Promise<EnhancedOrganizationProfile> {
    console.log('🔍 开始深度分析组织画像...');

    // 1. 基于基础信息推断细化特征
    const detailedCharacteristics = this.inferDetailedCharacteristics(basicProfile);

    // 2. 生成上下文标签
    const contextTags = this.generateContextTags(basicProfile, detailedCharacteristics);

    // 3. 计算独特性指纹
    const uniquenessFingerprint = this.calculateUniquenessFingerprint(basicProfile, detailedCharacteristics);

    const enhancedProfile: EnhancedOrganizationProfile = {
      ...basicProfile,
      detailedCharacteristics,
      contextTags,
      uniquenessFingerprint,
      analyzedAt: new Date().toISOString(),
      analysisVersion: this.analysisVersion
    };

    console.log('✅ 组织画像分析完成:', {
      uniquenessFingerprint,
      contextTags: contextTags.length,
      characteristics: Object.keys(detailedCharacteristics).length
    });

    return enhancedProfile;
  }

  /**
   * 推断细化特征
   */
  private inferDetailedCharacteristics(profile: OrganizationProfile): DetailedCharacteristics {
    return {
      resourceProfile: this.inferResourceProfile(profile),
      operationalProfile: this.inferOperationalProfile(profile),
      impactProfile: this.inferImpactProfile(profile),
      developmentProfile: this.inferDevelopmentProfile(profile)
    };
  }

  /**
   * 推断资源特征
   */
  private inferResourceProfile(profile: OrganizationProfile): ResourceProfile {
    // 基于组织类型和规模推断资源特征
    const fundingStability = this.inferFundingStability(profile);
    const volunteerDependency = this.inferVolunteerDependency(profile);
    const technicalCapacity = this.inferTechnicalCapacity(profile);
    const partnershipNetwork = this.inferPartnershipNetwork(profile);

    return {
      fundingStability,
      volunteerDependency,
      technicalCapacity,
      partnershipNetwork
    };
  }

  /**
   * 推断运营特征
   */
  private inferOperationalProfile(profile: OrganizationProfile): OperationalProfile {
    const decisionMakingStyle = this.inferDecisionMakingStyle(profile);
    const innovationOrientation = this.inferInnovationOrientation(profile);
    const riskTolerance = this.inferRiskTolerance(profile);
    const changeAdaptability = this.inferChangeAdaptability(profile);

    return {
      decisionMakingStyle,
      innovationOrientation,
      riskTolerance,
      changeAdaptability
    };
  }

  /**
   * 推断影响力特征
   */
  private inferImpactProfile(profile: OrganizationProfile): ImpactProfile {
    const measurementMaturity = this.inferMeasurementMaturity(profile);
    const stakeholderEngagement = this.inferStakeholderEngagement(profile);
    const transparencyLevel = this.inferTransparencyLevel(profile);
    const advocacyCapacity = this.inferAdvocacyCapacity(profile);

    return {
      measurementMaturity,
      stakeholderEngagement,
      transparencyLevel,
      advocacyCapacity
    };
  }

  /**
   * 推断发展特征
   */
  private inferDevelopmentProfile(profile: OrganizationProfile): DevelopmentProfile {
    const growthAmbition = this.inferGrowthAmbition(profile);
    const capacityGaps = this.inferCapacityGaps(profile);
    const strategicPriorities = this.inferStrategicPriorities(profile);
    const challengeAreas = this.inferChallengeAreas(profile);

    return {
      growthAmbition,
      capacityGaps,
      strategicPriorities,
      challengeAreas
    };
  }

  /**
   * 生成上下文标签
   */
  private generateContextTags(
    profile: OrganizationProfile,
    characteristics: DetailedCharacteristics
  ): string[] {
    const tags: string[] = [];

    // 基础组合标签
    tags.push(`${profile.organizationType}_${profile.developmentStage}`);
    
    // 服务领域标签
    if (Array.isArray(profile.serviceArea)) {
      profile.serviceArea.forEach(area => tags.push(`${area}_focused`));
    } else {
      tags.push(`${profile.serviceArea}_focused`);
    }

    // 规模和影响力标签
    tags.push(`${profile.organizationScale}_scale`);
    tags.push(`${profile.impactPositioning}_impact`);

    // 运营模式标签
    tags.push(`${profile.operatingModel}_model`);

    // 特征标签
    tags.push(`${characteristics.operationalProfile.decisionMakingStyle}_decision`);
    tags.push(`${characteristics.resourceProfile.fundingStability}_funding`);
    tags.push(`${characteristics.impactProfile.measurementMaturity}_measurement`);
    tags.push(`${characteristics.developmentProfile.growthAmbition}_growth`);

    // 文化标签
    tags.push(`${profile.organizationalCulture}_culture`);

    return tags;
  }

  /**
   * 计算独特性指纹
   */
  private calculateUniquenessFingerprint(
    profile: OrganizationProfile,
    characteristics: DetailedCharacteristics
  ): string {
    // 创建用于指纹计算的数据对象
    const fingerprintData = {
      profile: {
        organizationType: profile.organizationType,
        serviceArea: profile.serviceArea,
        organizationScale: profile.organizationScale,
        developmentStage: profile.developmentStage,
        operatingModel: profile.operatingModel,
        impactPositioning: profile.impactPositioning,
        organizationalCulture: profile.organizationalCulture
      },
      characteristics,
      timestamp: Math.floor(Date.now() / (1000 * 60 * 60)), // 按小时取整，确保短期内相同
      randomSeed: Math.floor(Math.random() * 1000) // 添加随机性
    };

    // 计算MD5哈希
    const fingerprint = crypto
      .createHash('md5')
      .update(JSON.stringify(fingerprintData))
      .digest('hex');

    // 取前12位作为指纹
    return fingerprint.substring(0, 12);
  }

  // 以下是各种推断方法的实现
  private inferFundingStability(profile: OrganizationProfile): 'stable' | 'fluctuating' | 'uncertain' {
    if (profile.organizationType === '基金会' || profile.developmentStage === '成熟期') {
      return 'stable';
    }
    if (profile.developmentStage === '初创期' || profile.organizationScale === '微型') {
      return 'uncertain';
    }
    return 'fluctuating';
  }

  private inferVolunteerDependency(profile: OrganizationProfile): 'high' | 'medium' | 'low' {
    if (profile.organizationType === '社会团体' || profile.organizationScale === '微型') {
      return 'high';
    }
    if (profile.organizationType === '基金会' || profile.organizationScale === '大型') {
      return 'low';
    }
    return 'medium';
  }

  private inferTechnicalCapacity(profile: OrganizationProfile): 'advanced' | 'moderate' | 'basic' {
    if (profile.organizationScale === '大型' || profile.organizationScale === '超大型') {
      return 'advanced';
    }
    if (profile.developmentStage === '初创期' || profile.organizationScale === '微型') {
      return 'basic';
    }
    return 'moderate';
  }

  private inferPartnershipNetwork(profile: OrganizationProfile): 'extensive' | 'moderate' | 'limited' {
    if (profile.impactPositioning === '国际影响' || profile.organizationScale === '超大型') {
      return 'extensive';
    }
    if (profile.impactPositioning === '本地影响' || profile.organizationScale === '微型') {
      return 'limited';
    }
    return 'moderate';
  }

  private inferDecisionMakingStyle(profile: OrganizationProfile): 'centralized' | 'distributed' | 'collaborative' {
    if (profile.organizationScale === '微型' || profile.organizationScale === '小型') {
      return 'centralized';
    }
    if (profile.organizationalCulture === '协作共享') {
      return 'collaborative';
    }
    return 'distributed';
  }

  private inferInnovationOrientation(profile: OrganizationProfile): 'pioneer' | 'follower' | 'conservative' {
    if (profile.organizationalCulture === '创新导向' || profile.developmentStage === '初创期') {
      return 'pioneer';
    }
    if (profile.developmentStage === '成熟期' || profile.organizationalCulture === '专业严谨') {
      return 'conservative';
    }
    return 'follower';
  }

  private inferRiskTolerance(profile: OrganizationProfile): 'high' | 'medium' | 'low' {
    if (profile.developmentStage === '初创期' || profile.organizationalCulture === '创新导向') {
      return 'high';
    }
    if (profile.developmentStage === '成熟期' || profile.organizationType === '基金会') {
      return 'low';
    }
    return 'medium';
  }

  private inferChangeAdaptability(profile: OrganizationProfile): 'agile' | 'moderate' | 'stable' {
    if (profile.organizationScale === '微型' || profile.organizationalCulture === '草根活力') {
      return 'agile';
    }
    if (profile.organizationScale === '大型' || profile.organizationType === '政府机构') {
      return 'stable';
    }
    return 'moderate';
  }

  private inferMeasurementMaturity(profile: OrganizationProfile): 'advanced' | 'developing' | 'basic' {
    if (profile.organizationScale === '大型' || profile.organizationType === '基金会') {
      return 'advanced';
    }
    if (profile.developmentStage === '初创期') {
      return 'basic';
    }
    return 'developing';
  }

  private inferStakeholderEngagement(profile: OrganizationProfile): 'proactive' | 'responsive' | 'passive' {
    if (profile.operatingModel === '倡导型' || profile.organizationalCulture === '协作共享') {
      return 'proactive';
    }
    if (profile.operatingModel === '直接服务') {
      return 'responsive';
    }
    return 'passive';
  }

  private inferTransparencyLevel(profile: OrganizationProfile): 'high' | 'medium' | 'low' {
    if (profile.organizationType === '基金会' || profile.impactPositioning === '国际影响') {
      return 'high';
    }
    if (profile.organizationScale === '微型') {
      return 'low';
    }
    return 'medium';
  }

  private inferAdvocacyCapacity(profile: OrganizationProfile): 'strong' | 'moderate' | 'weak' {
    if (profile.operatingModel === '倡导型' || profile.impactPositioning === '国际影响') {
      return 'strong';
    }
    if (profile.operatingModel === '直接服务' && profile.organizationScale === '微型') {
      return 'weak';
    }
    return 'moderate';
  }

  private inferGrowthAmbition(profile: OrganizationProfile): 'aggressive' | 'steady' | 'conservative' {
    if (profile.developmentStage === '扩张期' || profile.organizationalCulture === '创新导向') {
      return 'aggressive';
    }
    if (profile.developmentStage === '成熟期') {
      return 'conservative';
    }
    return 'steady';
  }

  private inferCapacityGaps(profile: OrganizationProfile): string[] {
    const gaps: string[] = [];
    
    if (profile.organizationScale === '微型' || profile.developmentStage === '初创期') {
      gaps.push('资金筹集', '团队建设', '项目管理');
    }
    
    if (profile.developmentStage === '成长期') {
      gaps.push('流程标准化', '品牌建设', '影响力测量');
    }
    
    return gaps;
  }

  private inferStrategicPriorities(profile: OrganizationProfile): string[] {
    const priorities: string[] = [];
    
    if (profile.developmentStage === '初创期') {
      priorities.push('生存发展', '服务质量', '资源获取');
    } else if (profile.developmentStage === '成长期') {
      priorities.push('规模扩张', '影响力提升', '可持续发展');
    } else if (profile.developmentStage === '成熟期') {
      priorities.push('创新转型', '深度影响', '行业引领');
    }
    
    return priorities;
  }

  private inferChallengeAreas(profile: OrganizationProfile): string[] {
    const challenges: string[] = [];
    
    if (profile.organizationScale === '微型') {
      challenges.push('资源限制', '人才短缺', '影响力有限');
    }
    
    if (Array.isArray(profile.serviceArea) && profile.serviceArea.length > 1) {
      challenges.push('资源分散', '专业化不足');
    }
    
    return challenges;
  }
}
