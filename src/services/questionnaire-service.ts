/**
 * OCTI问卷服务
 *
 * 整合预设题目和智能生成题目，创建完整的60道题目问卷
 */

import { Question } from '@/components/questionnaire/question-renderer';
import { getPresetQuestions } from '@/data/questionnaire-bank';
import {
  OrganizationProfile,
  intelligentQuestionGenerator,
} from './intelligent-question-generator';

/**
 * 从组织画像答案中提取组织特征（优先使用用户明确输入）
 */
export function extractOrganizationProfile(): OrganizationProfile {
  // 从localStorage获取组织画像答案
  const profileAnswers = localStorage.getItem('profileAnswers');
  // 获取用户直接输入的数据
  const userInputs = localStorage.getItem('userInputs');
  const organizationInfo = localStorage.getItem('octi_organization_info');

  let userSpecifiedFocusArea: string | undefined;

  // 优先从组织信息中获取主要关注领域
  if (organizationInfo) {
    try {
      const orgInfo = JSON.parse(organizationInfo);
      userSpecifiedFocusArea = orgInfo.mainFocus;
      if (userSpecifiedFocusArea?.trim()) {
        console.log(`✅ 从组织信息中检测到用户指定的关注领域: ${userSpecifiedFocusArea}`);
      }
    } catch (error) {
      console.warn('解析组织信息失败:', error);
    }
  }

  // 如果组织信息中没有，再从其他用户输入中获取
  if (!userSpecifiedFocusArea && userInputs) {
    try {
      const inputs = JSON.parse(userInputs);
      userSpecifiedFocusArea = inputs.focusArea || inputs.serviceArea || inputs.mainFocusArea;
      if (userSpecifiedFocusArea) {
        console.log(`✅ 从用户输入中检测到指定的关注领域: ${userSpecifiedFocusArea}`);
      }
    } catch (error) {
      console.warn('解析用户输入失败:', error);
    }
  }

  if (!profileAnswers) {
    // 如果没有画像数据，返回默认配置
    return {
      userSpecifiedFocusArea, // 用户明确填写的关注领域
      organizationType: '公益组织',
      serviceArea: ['社会服务'], // 改为更通用的社会服务
      organizationScale: '中型',
      developmentStage: '成长期',
      operatingModel: '直接服务',
      impactPositioning: '区域影响',
      organizationalCulture: '使命驱动',
    };
  }

  try {
    const answers = JSON.parse(profileAnswers);

    // 根据10道画像题目的答案映射组织特征
    const mappedProfile = mapAnswersToProfile(answers);

    // 将用户明确输入的关注领域添加到映射结果中
    mappedProfile.userSpecifiedFocusArea = userSpecifiedFocusArea;

    return mappedProfile;
  } catch (error) {
    console.error('解析组织画像数据失败:', error);
    return {
      userSpecifiedFocusArea, // 即使解析失败，也要保留用户输入
      organizationType: '公益组织',
      serviceArea: ['社会服务'], // 改为更通用的社会服务
      organizationScale: '中型',
      developmentStage: '成长期',
      operatingModel: '直接服务',
      impactPositioning: '区域影响',
      organizationalCulture: '使命驱动',
    };
  }
}

/**
 * 将画像答案映射为组织特征
 */
function mapAnswersToProfile(answers: Record<number, string>): OrganizationProfile {
  // 根据10道画像题目的答案选项映射组织特征
  const profile: OrganizationProfile = {
    userSpecifiedFocusArea: undefined, // 将在调用处设置
    organizationType: '公益组织',
    serviceArea: ['社会服务'], // 改为更通用的社会服务
    organizationScale: '中型',
    developmentStage: '成长期',
    operatingModel: '直接服务',
    impactPositioning: '区域影响',
    organizationalCulture: '使命驱动',
  };

  // 问题1：组织发展阶段与团队规模
  const q1Answer = answers[1];
  if (q1Answer) {
    const stageMapping: Record<string, string> = {
      A: '初创期',
      B: '探索期',
      C: '成长期',
      D: '成熟期',
      E: '转型期',
      F: '分化期',
    };
    profile.developmentStage = stageMapping[q1Answer] || '成长期';

    // 同时映射组织规模
    const scaleMapping: Record<string, string> = {
      A: '微型',
      B: '小型',
      C: '中型',
      D: '大型',
      E: '大型',
      F: '中型',
    };
    profile.organizationScale = scaleMapping[q1Answer] || '中型';
  }

  // 问题2：核心业务模式与价值创造
  const q2Answer = answers[2];
  if (q2Answer) {
    const modelMapping: Record<string, string> = {
      A: '直接服务',
      B: '倡导型',
      C: '平台型',
      D: '资助型',
      E: '资助型',
      F: '创新型',
    };
    profile.operatingModel = modelMapping[q2Answer] || '直接服务';
  }

  // 问题3：资源获取与资金结构
  const q3Answer = answers[3];
  if (q3Answer) {
    const typeMapping: Record<string, string> = {
      A: '政府机构',
      B: '基金会',
      C: '公益组织',
      D: '社会企业',
      E: '公益组织',
      F: '社会企业',
    };
    profile.organizationType = typeMapping[q3Answer] || '公益组织';
  }

  // 问题5：目标客户与服务对象结构 - 影响力定位
  const q5Answer = answers[5];
  if (q5Answer) {
    const impactMapping: Record<string, string> = {
      A: '本地影响',
      B: '区域影响',
      C: '全国影响',
      D: '本地影响',
      E: '全国影响',
      F: '区域影响',
    };
    profile.impactPositioning = impactMapping[q5Answer] || '区域影响';
  }

  // 问题7：能力建设与人才发展 - 组织文化
  const q7Answer = answers[7];
  if (q7Answer) {
    const cultureMapping: Record<string, string> = {
      A: '个人驱动',
      B: '协作共享',
      C: '专业严谨',
      D: '创新导向',
      E: '学习型',
      F: '使命驱动',
    };
    profile.organizationalCulture = cultureMapping[q7Answer] || '使命驱动';
  }

  // 根据其他答案推断服务领域
  profile.serviceArea = inferServiceAreas(answers);

  return profile;
}

/**
 * 根据答案推断服务领域
 */
function inferServiceAreas(answers: Record<number, string>): string[] {
  // 根据组织类型推断可能的服务领域
  const organizationType = answers[3]; // 问题3是组织类型相关

  // 基于组织类型的服务领域映射
  const typeToServiceMapping: Record<string, string[]> = {
    A: ['社会服务'], // 政府机构
    B: ['公益咨询', '资助服务'], // 基金会
    C: ['社会服务', '公益咨询'], // 公益组织
    D: ['社会服务', '创新服务'], // 社会企业
    E: ['社会服务'], // 其他公益组织
    F: ['创新服务', '社会服务'], // 其他社会企业
  };

  // 如果有明确的组织类型映射，使用映射结果
  if (organizationType && typeToServiceMapping[organizationType]) {
    return typeToServiceMapping[organizationType];
  }

  // 默认为社会服务，避免默认到医疗等特定领域
  return ['社会服务'];
}

/**
 * 生成完整的混合问卷
 */
export async function generateHybridQuestionnaire(): Promise<{
  questions: Question[];
  stats: {
    total: number;
    preset: number;
    intelligent: number;
    byDimension: Record<string, number>;
  };
  profile: OrganizationProfile;
}> {
  try {
    // 1. 获取组织画像
    const profile = extractOrganizationProfile();

    // 2. 获取32道预设题目
    const presetQuestions = getPresetQuestions();

    // 3. 基于组织画像生成28道智能题目
    const intelligentQuestions =
      await intelligentQuestionGenerator.generateIntelligentQuestions(profile);

    // 4. 合并所有题目并排序
    const allQuestions = [...presetQuestions, ...intelligentQuestions].sort(
      (a, b) => a.order - b.order
    );

    // 5. 计算统计信息
    const stats = {
      total: allQuestions.length,
      preset: presetQuestions.length,
      intelligent: intelligentQuestions.length,
      byDimension: {
        SF: allQuestions.filter(q => q.category.startsWith('SF')).length,
        IT: allQuestions.filter(q => q.category.startsWith('IT')).length,
        MV: allQuestions.filter(q => q.category.startsWith('MV')).length,
        AD: allQuestions.filter(q => q.category.startsWith('AD')).length,
      },
    };

    return {
      questions: allQuestions,
      stats,
      profile,
    };
  } catch (error) {
    console.error('生成混合问卷失败:', error);

    // 降级到预设题目
    const presetQuestions = getPresetQuestions();
    const profile = extractOrganizationProfile();

    return {
      questions: presetQuestions,
      stats: {
        total: presetQuestions.length,
        preset: presetQuestions.length,
        intelligent: 0,
        byDimension: {
          SF: presetQuestions.filter(q => q.category.startsWith('SF')).length,
          IT: presetQuestions.filter(q => q.category.startsWith('IT')).length,
          MV: presetQuestions.filter(q => q.category.startsWith('MV')).length,
          AD: presetQuestions.filter(q => q.category.startsWith('AD')).length,
        },
      },
      profile,
    };
  }
}

/**
 * 验证问卷完整性
 */
export function validateQuestionnaire(questions: Question[]): {
  isValid: boolean;
  errors: string[];
  warnings: string[];
} {
  const errors: string[] = [];
  const warnings: string[] = [];

  // 检查总题目数
  if (questions.length !== 60) {
    errors.push(`题目总数应为60道，当前为${questions.length}道`);
  }

  // 检查维度分布
  const dimensionCounts = {
    SF: questions.filter(q => q.category.startsWith('SF')).length,
    IT: questions.filter(q => q.category.startsWith('IT')).length,
    MV: questions.filter(q => q.category.startsWith('MV')).length,
    AD: questions.filter(q => q.category.startsWith('AD')).length,
  };

  Object.entries(dimensionCounts).forEach(([dimension, count]) => {
    if (count !== 15) {
      errors.push(`${dimension}维度应有15道题目，当前为${count}道`);
    }
  });

  // 检查智能生成题目
  const intelligentQuestions = questions.filter(q => q.source === 'AI_GENERATED');
  if (intelligentQuestions.length !== 28) {
    warnings.push(`智能生成题目应为28道，当前为${intelligentQuestions.length}道`);
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  };
}
