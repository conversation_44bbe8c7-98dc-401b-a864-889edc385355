/**
 * OCTI智能评估系统 - 语义相似度检测器
 * 
 * 检测新生成问题与历史问题的相似度，避免重复生成
 */

import {
  SimilarityCheckResult,
  QuestionSimilarity
} from '@/types/enhanced-organization-profile';

type OCTIDimension = 'SF' | 'IT' | 'MV' | 'AD';

/**
 * 生成的问题接口
 */
interface GeneratedQuestion {
  id: string;
  text: string;
  type: string;
  options: any;
  category: string;
  dimension: string;
  weight: number;
}

/**
 * 语义相似度检测器
 */
export class SemanticSimilarityDetector {
  private questionHistory: Map<string, GeneratedQuestion[]>;
  private similarityThreshold: number = 0.7;
  private maxHistorySize: number = 100; // 每个维度最多保存100个历史问题

  constructor() {
    this.questionHistory = new Map();
  }

  /**
   * 检查新生成问题与历史问题的相似度
   */
  async checkSimilarity(
    newQuestions: GeneratedQuestion[],
    dimension: OCTIDimension,
    profileFingerprint: string
  ): Promise<SimilarityCheckResult> {
    console.log(`🔍 开始相似度检查，新问题数量: ${newQuestions.length}`);

    const historyKey = `${dimension}_${profileFingerprint}`;
    const historicalQuestions = this.questionHistory.get(historyKey) || [];

    console.log(`📚 历史问题数量: ${historicalQuestions.length}`);

    if (historicalQuestions.length === 0) {
      // 没有历史问题，直接返回
      return {
        hasSimilarQuestions: false,
        similarities: [],
        uniqueQuestions: newQuestions,
        averageSimilarity: 0,
        maxSimilarity: 0
      };
    }

    const similarities: QuestionSimilarity[] = [];
    const similarityScores: number[] = [];

    // 检查每个新问题与历史问题的相似度
    for (const newQuestion of newQuestions) {
      for (const historicalQuestion of historicalQuestions) {
        const similarity = await this.calculateSimilarity(newQuestion, historicalQuestion);
        similarityScores.push(similarity);

        if (similarity > this.similarityThreshold) {
          similarities.push({
            newQuestion,
            similarQuestion: historicalQuestion,
            similarity,
            reason: this.analyzeSimilarityReason(newQuestion, historicalQuestion, similarity)
          });
        }
      }
    }

    // 筛选出独特的问题
    const uniqueQuestions = newQuestions.filter(newQ => 
      !similarities.some(sim => sim.newQuestion.id === newQ.id)
    );

    const averageSimilarity = similarityScores.length > 0
      ? similarityScores.reduce((sum: number, score: number) => sum + score, 0) / similarityScores.length
      : 0;

    const maxSimilarity = similarityScores.length > 0 
      ? Math.max(...similarityScores) 
      : 0;

    console.log(`📊 相似度检查结果: 相似问题${similarities.length}个, 独特问题${uniqueQuestions.length}个`);
    console.log(`📈 平均相似度: ${averageSimilarity.toFixed(3)}, 最高相似度: ${maxSimilarity.toFixed(3)}`);

    return {
      hasSimilarQuestions: similarities.length > 0,
      similarities,
      uniqueQuestions,
      averageSimilarity,
      maxSimilarity
    };
  }

  /**
   * 计算两个问题的语义相似度
   */
  private async calculateSimilarity(q1: GeneratedQuestion, q2: GeneratedQuestion): Promise<number> {
    // 方法1：基于关键词重叠度 (40%)
    const keywordSimilarity = this.calculateKeywordSimilarity(q1.text, q2.text);

    // 方法2：基于结构相似度 (30%)
    const structuralSimilarity = this.calculateStructuralSimilarity(q1, q2);

    // 方法3：基于语义向量相似度 (30%) - 简化版本
    const semanticSimilarity = this.calculateSemanticSimilarity(q1.text, q2.text);

    // 综合相似度
    const totalSimilarity = (
      keywordSimilarity * 0.4 + 
      structuralSimilarity * 0.3 + 
      semanticSimilarity * 0.3
    );

    return Math.min(Math.max(totalSimilarity, 0), 1); // 确保在0-1范围内
  }

  /**
   * 计算关键词相似度
   */
  private calculateKeywordSimilarity(text1: string, text2: string): number {
    // 提取关键词
    const keywords1 = this.extractKeywords(text1);
    const keywords2 = this.extractKeywords(text2);

    if (keywords1.length === 0 || keywords2.length === 0) {
      return 0;
    }

    // 计算交集
    const intersection = keywords1.filter(word => keywords2.includes(word));
    const union = [...new Set([...keywords1, ...keywords2])];

    // Jaccard相似度
    return intersection.length / union.length;
  }

  /**
   * 提取关键词
   */
  private extractKeywords(text: string): string[] {
    // 移除标点符号，转换为小写，分词
    const cleanText = text.replace(/[^\u4e00-\u9fa5a-zA-Z0-9\s]/g, '').toLowerCase();
    const words = cleanText.split(/\s+/).filter(word => word.length > 1);

    // 移除停用词
    const stopWords = new Set([
      '的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都', '一', '一个', '上', '也', '很', '到', '说', '要', '去', '你', '会', '着', '没有', '看', '好', '自己', '这',
      'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'have', 'has', 'had', 'do', 'does', 'did'
    ]);

    return words.filter(word => !stopWords.has(word) && word.length > 1);
  }

  /**
   * 计算结构相似度
   */
  private calculateStructuralSimilarity(q1: GeneratedQuestion, q2: GeneratedQuestion): number {
    let similarity = 0;

    // 问题类型相似度 (40%)
    if (q1.type === q2.type) {
      similarity += 0.4;
    }

    // 选项数量相似度 (30%)
    const options1 = Array.isArray(q1.options) ? q1.options.length : 0;
    const options2 = Array.isArray(q2.options) ? q2.options.length : 0;
    
    if (options1 > 0 && options2 > 0) {
      const optionSimilarity = 1 - Math.abs(options1 - options2) / Math.max(options1, options2);
      similarity += optionSimilarity * 0.3;
    }

    // 问题长度相似度 (30%)
    const length1 = q1.text.length;
    const length2 = q2.text.length;
    
    if (length1 > 0 && length2 > 0) {
      const lengthSimilarity = 1 - Math.abs(length1 - length2) / Math.max(length1, length2);
      similarity += lengthSimilarity * 0.3;
    }

    return similarity;
  }

  /**
   * 计算语义相似度（简化版本）
   */
  private calculateSemanticSimilarity(text1: string, text2: string): number {
    // 简化的语义相似度计算
    // 基于字符级别的编辑距离
    const distance = this.calculateEditDistance(text1, text2);
    const maxLength = Math.max(text1.length, text2.length);
    
    if (maxLength === 0) return 1;
    
    return 1 - (distance / maxLength);
  }

  /**
   * 计算编辑距离（Levenshtein距离）
   */
  private calculateEditDistance(str1: string, str2: string): number {
    const matrix = [];
    const len1 = str1.length;
    const len2 = str2.length;

    // 初始化矩阵
    for (let i = 0; i <= len1; i++) {
      matrix[i] = [i];
    }
    for (let j = 0; j <= len2; j++) {
      matrix[0][j] = j;
    }

    // 填充矩阵
    for (let i = 1; i <= len1; i++) {
      for (let j = 1; j <= len2; j++) {
        if (str1[i - 1] === str2[j - 1]) {
          matrix[i][j] = matrix[i - 1][j - 1];
        } else {
          matrix[i][j] = Math.min(
            matrix[i - 1][j] + 1,     // 删除
            matrix[i][j - 1] + 1,     // 插入
            matrix[i - 1][j - 1] + 1  // 替换
          );
        }
      }
    }

    return matrix[len1][len2];
  }

  /**
   * 分析相似度原因
   */
  private analyzeSimilarityReason(
    newQuestion: GeneratedQuestion,
    similarQuestion: GeneratedQuestion,
    similarity: number
  ): string {
    const reasons = [];

    // 检查类型相似
    if (newQuestion.type === similarQuestion.type) {
      reasons.push('问题类型相同');
    }

    // 检查关键词重叠
    const keywords1 = this.extractKeywords(newQuestion.text);
    const keywords2 = this.extractKeywords(similarQuestion.text);
    const commonKeywords = keywords1.filter(word => keywords2.includes(word));
    
    if (commonKeywords.length > 2) {
      reasons.push(`关键词重叠: ${commonKeywords.slice(0, 3).join(', ')}`);
    }

    // 检查长度相似
    const lengthDiff = Math.abs(newQuestion.text.length - similarQuestion.text.length);
    if (lengthDiff < 20) {
      reasons.push('问题长度相近');
    }

    return reasons.length > 0 ? reasons.join('; ') : `高相似度(${similarity.toFixed(3)})`;
  }

  /**
   * 更新问题历史记录
   */
  updateHistory(
    questions: GeneratedQuestion[],
    dimension: OCTIDimension,
    profileFingerprint: string
  ): void {
    const historyKey = `${dimension}_${profileFingerprint}`;
    const existing = this.questionHistory.get(historyKey) || [];

    // 添加新问题到历史记录
    const updated = [...existing, ...questions];

    // 保留最近的问题（按时间戳或ID排序）
    const trimmed = updated.slice(-this.maxHistorySize);

    this.questionHistory.set(historyKey, trimmed);

    console.log(`📝 更新历史记录: ${historyKey}, 当前数量: ${trimmed.length}`);
  }

  /**
   * 清理历史记录
   */
  clearHistory(dimension?: OCTIDimension, profileFingerprint?: string): void {
    if (dimension && profileFingerprint) {
      const historyKey = `${dimension}_${profileFingerprint}`;
      this.questionHistory.delete(historyKey);
      console.log(`🗑️ 清理历史记录: ${historyKey}`);
    } else {
      this.questionHistory.clear();
      console.log('🗑️ 清理所有历史记录');
    }
  }

  /**
   * 获取历史统计
   */
  getHistoryStats(): Record<string, number> {
    const stats: Record<string, number> = {};
    
    for (const [key, questions] of this.questionHistory.entries()) {
      stats[key] = questions.length;
    }

    return stats;
  }

  /**
   * 设置相似度阈值
   */
  setSimilarityThreshold(threshold: number): void {
    this.similarityThreshold = Math.min(Math.max(threshold, 0), 1);
    console.log(`🎯 设置相似度阈值: ${this.similarityThreshold}`);
  }
}
