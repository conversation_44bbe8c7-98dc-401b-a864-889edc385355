/**
 * OCTI智能评估系统 - 方案C测试文件
 * 
 * 测试上下文感知的差异化生成功能
 */

import { OrganizationProfileAnalyzer } from '@/services/organization-profile-analyzer';
import { ContextualPromptGenerator } from '@/services/contextual-prompt-generator';
import { SemanticSimilarityDetector } from '@/services/semantic-similarity-detector';
import { EvaluationAngleMatrix } from '@/services/evaluation-angle-matrix';
import { OrganizationProfile } from '@/services/intelligent-question-generator';

/**
 * 测试组织画像分析器
 */
async function testProfileAnalyzer() {
  console.log('🧪 测试组织画像分析器...');
  
  const analyzer = new OrganizationProfileAnalyzer();
  
  const testProfile: OrganizationProfile = {
    organizationType: '社会服务机构',
    serviceArea: ['教育', '儿童保护'],
    organizationScale: '中型',
    developmentStage: '成长期',
    operatingModel: '直接服务',
    impactPositioning: '地区影响',
    organizationalCulture: '协作共享'
  };

  const enhancedProfile = await analyzer.analyzeProfile(testProfile);
  
  console.log('✅ 增强画像生成成功:');
  console.log('- 独特性指纹:', enhancedProfile.uniquenessFingerprint);
  console.log('- 上下文标签:', enhancedProfile.contextTags.slice(0, 5));
  console.log('- 资金稳定性:', enhancedProfile.detailedCharacteristics.resourceProfile.fundingStability);
  console.log('- 决策风格:', enhancedProfile.detailedCharacteristics.operationalProfile.decisionMakingStyle);
  
  return enhancedProfile;
}

/**
 * 测试评估角度矩阵
 */
function testAngleMatrix() {
  console.log('\n🧪 测试评估角度矩阵...');
  
  const matrix = new EvaluationAngleMatrix();
  
  const testProfile = {
    organizationType: '基金会',
    serviceArea: ['环境保护'],
    organizationScale: '大型',
    developmentStage: '成熟期',
    operatingModel: '资助型',
    impactPositioning: '国际影响',
    organizationalCulture: '专业严谨',
    detailedCharacteristics: {
      resourceProfile: {
        fundingStability: 'stable' as const,
        volunteerDependency: 'medium' as const,
        technicalCapacity: 'advanced' as const,
        partnershipNetwork: 'extensive' as const
      },
      operationalProfile: {
        decisionMakingStyle: 'centralized' as const,
        innovationOrientation: 'conservative' as const,
        riskTolerance: 'medium' as const,
        changeAdaptability: 'moderate' as const
      },
      impactProfile: {
        measurementMaturity: 'advanced' as const,
        stakeholderEngagement: 'proactive' as const,
        transparencyLevel: 'high' as const,
        advocacyCapacity: 'strong' as const
      },
      developmentProfile: {
        growthAmbition: 'steady' as const,
        capacityGaps: ['技术能力', '人才培养'],
        strategicPriorities: ['可持续发展', '影响力扩大'],
        challengeAreas: ['资金筹集', '人才留存']
      }
    },
    contextTags: ['基金会_成熟期', '环境保护_focused', 'large_scale', 'well_funded'],
    uniquenessFingerprint: 'test123456',
    analyzedAt: new Date().toISOString(),
    analysisVersion: '1.0.0'
  };

  const context = {
    dimension: 'SF',
    questionCount: 7,
    previousAttempts: 0,
    avoidancePatterns: [],
    preferredStyles: [],
    generationId: 'test_generation_001',
    timestamp: Date.now()
  };

  const angles = matrix.getOptimalAngles('SF', testProfile, context);
  
  console.log('✅ 角度选择成功:');
  angles.forEach(angle => {
    console.log(`- ${angle.name}: ${angle.complexityLevel} (权重: ${angle.weight})`);
  });
  
  return angles;
}

/**
 * 测试上下文提示词生成器
 */
async function testPromptGenerator() {
  console.log('\n🧪 测试上下文提示词生成器...');
  
  const generator = new ContextualPromptGenerator();
  const analyzer = new OrganizationProfileAnalyzer();
  
  const testProfile: OrganizationProfile = {
    organizationType: '社会团体',
    serviceArea: ['社区发展'],
    organizationScale: '小型',
    developmentStage: '初创期',
    operatingModel: '倡导型',
    impactPositioning: '本地影响',
    organizationalCulture: '草根活力'
  };

  const enhancedProfile = await analyzer.analyzeProfile(testProfile);
  
  const context = {
    dimension: 'IT',
    questionCount: 5,
    previousAttempts: 0,
    avoidancePatterns: [],
    preferredStyles: [],
    generationId: 'test_generation_002',
    timestamp: Date.now()
  };

  const prompt = generator.generateContextualPrompt('IT', enhancedProfile, context);
  
  console.log('✅ 提示词生成成功:');
  console.log('- 提示词长度:', prompt.length, '字符');
  console.log('- 包含组织类型:', prompt.includes(enhancedProfile.organizationType));
  console.log('- 包含独特性指纹:', prompt.includes(enhancedProfile.uniquenessFingerprint));
  
  return prompt;
}

/**
 * 测试语义相似度检测器
 */
async function testSimilarityDetector() {
  console.log('\n🧪 测试语义相似度检测器...');
  
  const detector = new SemanticSimilarityDetector();
  
  const testQuestions = [
    {
      id: 'SF_I001',
      text: '您的组织在制定战略规划时，如何确保资源配置的聚焦性？',
      type: 'single_choice',
      options: ['选项1', '选项2', '选项3', '选项4'],
      category: 'SF_INTELLIGENT',
      dimension: 'SF',
      weight: 1
    },
    {
      id: 'SF_I002',
      text: '在战略规划过程中，您的组织如何平衡资源分配的集中度？',
      type: 'single_choice',
      options: ['选项A', '选项B', '选项C', '选项D'],
      category: 'SF_INTELLIGENT',
      dimension: 'SF',
      weight: 1
    },
    {
      id: 'SF_I003',
      text: '您的组织在团队协作方面有哪些具体的管理措施？',
      type: 'multiple_choice',
      options: ['措施1', '措施2', '措施3', '措施4'],
      category: 'SF_INTELLIGENT',
      dimension: 'SF',
      weight: 1
    }
  ];

  const similarityCheck = await detector.checkSimilarity(
    testQuestions,
    'SF',
    'test_fingerprint_123'
  );
  
  console.log('✅ 相似度检测完成:');
  console.log('- 检测到相似问题:', similarityCheck.hasSimilarQuestions);
  console.log('- 相似问题数量:', similarityCheck.similarities.length);
  console.log('- 独特问题数量:', similarityCheck.uniqueQuestions.length);
  console.log('- 平均相似度:', similarityCheck.averageSimilarity.toFixed(3));
  
  // 更新历史记录
  detector.updateHistory(testQuestions, 'SF', 'test_fingerprint_123');
  
  // 再次检测（应该发现相似问题）
  const secondCheck = await detector.checkSimilarity(
    testQuestions.slice(0, 2), // 使用前两个相似的问题
    'SF',
    'test_fingerprint_123'
  );
  
  console.log('- 第二次检测相似问题:', secondCheck.hasSimilarQuestions);
  console.log('- 第二次相似度:', secondCheck.maxSimilarity.toFixed(3));
  
  return similarityCheck;
}

/**
 * 运行所有测试
 */
async function runAllTests() {
  console.log('🚀 开始方案C功能测试...\n');
  
  try {
    // 测试1: 组织画像分析器
    const enhancedProfile = await testProfileAnalyzer();
    
    // 测试2: 评估角度矩阵
    const angles = testAngleMatrix();
    
    // 测试3: 上下文提示词生成器
    const prompt = await testPromptGenerator();
    
    // 测试4: 语义相似度检测器
    const similarityResult = await testSimilarityDetector();
    
    console.log('\n🎉 所有测试完成！方案C核心功能正常工作。');
    
    return {
      enhancedProfile,
      angles,
      prompt: prompt.substring(0, 200) + '...',
      similarityResult
    };
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
    throw error;
  }
}

// 导出测试函数
export {
  testProfileAnalyzer,
  testAngleMatrix,
  testPromptGenerator,
  testSimilarityDetector,
  runAllTests
};

// 如果直接运行此文件，执行所有测试
if (require.main === module) {
  runAllTests()
    .then(results => {
      console.log('\n📊 测试结果摘要:');
      console.log('- 增强画像指纹:', results.enhancedProfile.uniquenessFingerprint);
      console.log('- 选择角度数量:', results.angles.length);
      console.log('- 提示词预览:', results.prompt);
      console.log('- 相似度检测:', results.similarityResult.hasSimilarQuestions ? '发现相似' : '全部独特');
    })
    .catch(error => {
      console.error('测试执行失败:', error);
      process.exit(1);
    });
}
