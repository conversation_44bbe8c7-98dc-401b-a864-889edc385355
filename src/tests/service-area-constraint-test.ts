/**
 * OCTI智能评估系统 - 服务领域约束测试
 * 
 * 测试修复后的服务领域约束和中国本土化功能
 */

import { OrganizationProfile } from '@/services/intelligent-question-generator';

/**
 * 测试不同服务领域的约束效果
 */
export async function testServiceAreaConstraints() {
  console.log('🧪 开始测试服务领域约束...\n');

  // 测试用例1: 环保机构
  const environmentProfile: OrganizationProfile = {
    organizationType: '社会服务机构',
    serviceArea: ['环境保护'],
    organizationScale: '中型',
    developmentStage: '成长期',
    operatingModel: '直接服务',
    impactPositioning: '地区影响',
    organizationalCulture: '使命驱动'
  };

  // 测试用例2: 教育机构
  const educationProfile: OrganizationProfile = {
    organizationType: '社会团体',
    serviceArea: ['教育'],
    organizationScale: '小型',
    developmentStage: '成熟期',
    operatingModel: '直接服务',
    impactPositioning: '本地影响',
    organizationalCulture: '专业严谨'
  };

  // 测试用例3: 医疗健康机构
  const healthProfile: OrganizationProfile = {
    organizationType: '基金会',
    serviceArea: ['医疗健康'],
    organizationScale: '大型',
    developmentStage: '成熟期',
    operatingModel: '资助型',
    impactPositioning: '国际影响',
    organizationalCulture: '创新导向'
  };

  const testCases = [
    { name: '环保机构', profile: environmentProfile, expectedKeywords: ['环保', '环境', '生态', '污染', '绿色'], forbiddenKeywords: ['医疗', '教育', '健康', '学校', '医院'] },
    { name: '教育机构', profile: educationProfile, expectedKeywords: ['教育', '学校', '学生', '教学', '课程'], forbiddenKeywords: ['医疗', '环保', '健康', '污染', '治疗'] },
    { name: '医疗健康机构', profile: healthProfile, expectedKeywords: ['医疗', '健康', '患者', '治疗', '医院'], forbiddenKeywords: ['环保', '教育', '学校', '污染', '生态'] }
  ];

  for (const testCase of testCases) {
    console.log(`\n=== 测试 ${testCase.name} ===`);
    
    try {
      // 测试SF维度
      const response = await fetch('http://localhost:3000/api/questionnaire/generate-intelligent', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          dimension: 'SF',
          profile: testCase.profile,
          count: 3,
          generationOptions: {
            previousAttempts: 0,
            avoidancePatterns: [],
            preferredStyles: []
          }
        }),
      });

      if (!response.ok) {
        console.error(`❌ ${testCase.name} API调用失败:`, response.status);
        continue;
      }

      const result = await response.json();

      if (!result.success) {
        console.error(`❌ ${testCase.name} 生成失败:`, result.error);
        continue;
      }

      const questions = result.data.questions || [];
      console.log(`✅ ${testCase.name} 生成成功: ${questions.length}道题目`);

      // 检查服务领域约束
      let hasExpectedKeywords = false;
      let hasForbiddenKeywords = false;
      let hasChineseTools = false;
      let hasForeignTools = false;

      const allText = questions.map((q: any) => q.text + ' ' + (q.options || []).join(' ')).join(' ');

      // 检查期望关键词
      for (const keyword of testCase.expectedKeywords) {
        if (allText.includes(keyword)) {
          hasExpectedKeywords = true;
          break;
        }
      }

      // 检查禁止关键词
      for (const keyword of testCase.forbiddenKeywords) {
        if (allText.includes(keyword)) {
          hasForbiddenKeywords = true;
          console.log(`⚠️ 发现禁止关键词: ${keyword}`);
          break;
        }
      }

      // 检查中国工具
      const chineseTools = ['微信', '钉钉', '腾讯', '飞书', '石墨', '金山', '企业微信'];
      for (const tool of chineseTools) {
        if (allText.includes(tool)) {
          hasChineseTools = true;
          break;
        }
      }

      // 检查国外工具
      const foreignTools = ['Slack', 'Zoom', 'Google', 'Microsoft Teams', 'Trello', 'Asana'];
      for (const tool of foreignTools) {
        if (allText.includes(tool)) {
          hasForeignTools = true;
          console.log(`⚠️ 发现国外工具: ${tool}`);
          break;
        }
      }

      // 输出检查结果
      console.log(`  - 包含期望关键词: ${hasExpectedKeywords ? '✅' : '❌'}`);
      console.log(`  - 避免禁止关键词: ${hasForbiddenKeywords ? '❌' : '✅'}`);
      console.log(`  - 使用中国工具: ${hasChineseTools ? '✅' : '⚠️ 未检测到'}`);
      console.log(`  - 避免国外工具: ${hasForeignTools ? '❌' : '✅'}`);

      // 显示生成的问题示例
      console.log(`  - 问题示例: ${questions[0]?.text?.substring(0, 50)}...`);

      // 计算约束遵守率
      const constraintScore = [
        hasExpectedKeywords,
        !hasForbiddenKeywords,
        !hasForeignTools
      ].filter(Boolean).length / 3;

      console.log(`  - 约束遵守率: ${(constraintScore * 100).toFixed(1)}%`);

    } catch (error) {
      console.error(`❌ ${testCase.name} 测试失败:`, error);
    }
  }

  console.log('\n🎉 服务领域约束测试完成！');
}

/**
 * 测试中国本土化工具约束
 */
export async function testLocalizationConstraints() {
  console.log('\n🧪 开始测试中国本土化约束...\n');

  const testProfile: OrganizationProfile = {
    organizationType: '社会服务机构',
    serviceArea: ['社区发展'],
    organizationScale: '中型',
    developmentStage: '成长期',
    operatingModel: '直接服务',
    impactPositioning: '地区影响',
    organizationalCulture: '协作共享'
  };

  try {
    // 测试IT维度（更容易涉及工具）
    const response = await fetch('http://localhost:3000/api/questionnaire/generate-intelligent', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        dimension: 'IT',
        profile: testProfile,
        count: 5,
        generationOptions: {
          previousAttempts: 0,
          avoidancePatterns: [],
          preferredStyles: []
        }
      }),
    });

    if (!response.ok) {
      console.error('❌ 本土化测试API调用失败:', response.status);
      return;
    }

    const result = await response.json();

    if (!result.success) {
      console.error('❌ 本土化测试生成失败:', result.error);
      return;
    }

    const questions = result.data.questions || [];
    console.log(`✅ IT维度生成成功: ${questions.length}道题目`);

    const allText = questions.map((q: any) => q.text + ' ' + (q.options || []).join(' ')).join(' ');

    // 检查中国工具
    const chineseTools = ['微信', '钉钉', '腾讯会议', '飞书', '石墨文档', '金山文档', '企业微信', '腾讯文档'];
    const foundChineseTools = chineseTools.filter(tool => allText.includes(tool));

    // 检查国外工具
    const foreignTools = ['Slack', 'Zoom', 'Google Workspace', 'Microsoft Teams', 'Trello', 'Asana', 'Notion'];
    const foundForeignTools = foreignTools.filter(tool => allText.includes(tool));

    console.log('=== 本土化检查结果 ===');
    console.log(`发现中国工具: ${foundChineseTools.length > 0 ? foundChineseTools.join(', ') : '无'}`);
    console.log(`发现国外工具: ${foundForeignTools.length > 0 ? foundForeignTools.join(', ') : '无'}`);

    const localizationScore = foundForeignTools.length === 0 ? 100 : 
      Math.max(0, 100 - (foundForeignTools.length * 20));

    console.log(`本土化评分: ${localizationScore}%`);

    // 显示问题示例
    console.log('\n问题示例:');
    questions.slice(0, 2).forEach((q: any, index: number) => {
      console.log(`${index + 1}. ${q.text}`);
    });

  } catch (error) {
    console.error('❌ 本土化测试失败:', error);
  }
}

/**
 * 运行所有约束测试
 */
export async function runConstraintTests() {
  console.log('🚀 开始运行约束测试...\n');
  
  try {
    await testServiceAreaConstraints();
    await testLocalizationConstraints();
    
    console.log('\n🎉 所有约束测试完成！');
    console.log('\n💡 如果测试结果显示约束遵守率较低，说明需要进一步优化提示词。');
    
  } catch (error) {
    console.error('❌ 约束测试执行失败:', error);
  }
}

// 如果直接运行此文件，执行所有测试
if (require.main === module) {
  runConstraintTests()
    .then(() => {
      console.log('测试执行完成');
      process.exit(0);
    })
    .catch(error => {
      console.error('测试执行失败:', error);
      process.exit(1);
    });
}
