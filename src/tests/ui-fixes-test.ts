/**
 * OCTI智能评估系统 - UI修复测试
 * 
 * 测试夜间模式输入框和智能问题生成前端渲染修复
 */

/**
 * 测试夜间模式输入框可见性
 */
export async function testDarkModeInputVisibility() {
  console.log('🌙 开始测试夜间模式输入框可见性...\n');

  try {
    // 检查页面是否可访问
    const response = await fetch('http://localhost:3000/assessment/start');
    
    if (!response.ok) {
      console.error('❌ 无法访问评估开始页面:', response.status);
      return false;
    }

    const html = await response.text();

    // 检查是否使用了正确的CSS类
    const hasOctiInputClass = html.includes('octi-input');
    const hasTextForegroundClass = html.includes('text-foreground');
    const hasOldGrayClasses = html.includes('text-gray-700') || html.includes('border-gray-300');

    console.log('=== 夜间模式输入框检查结果 ===');
    console.log(`使用octi-input类: ${hasOctiInputClass ? '✅' : '❌'}`);
    console.log(`使用text-foreground类: ${hasTextForegroundClass ? '✅' : '❌'}`);
    console.log(`避免硬编码灰色类: ${!hasOldGrayClasses ? '✅' : '❌'}`);

    const score = [hasOctiInputClass, hasTextForegroundClass, !hasOldGrayClasses]
      .filter(Boolean).length / 3;

    console.log(`夜间模式兼容性评分: ${(score * 100).toFixed(1)}%`);

    return score >= 0.8;

  } catch (error) {
    console.error('❌ 夜间模式测试失败:', error);
    return false;
  }
}

/**
 * 测试智能问题生成前端渲染
 */
export async function testIntelligentQuestionRendering() {
  console.log('\n🤖 开始测试智能问题生成前端渲染...\n');

  try {
    // 模拟组织画像数据
    const testProfile = {
      organizationType: '社会服务机构',
      serviceArea: ['环境保护'],
      organizationScale: '中型',
      developmentStage: '成长期',
      operatingModel: '直接服务',
      impactPositioning: '地区影响',
      organizationalCulture: '使命驱动'
    };

    console.log('1. 测试后台智能问题生成API...');

    // 测试后台生成API
    const backgroundResponse = await fetch('http://localhost:3000/api/questionnaire/generate-background', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ profile: testProfile }),
    });

    if (!backgroundResponse.ok) {
      console.error('❌ 后台生成API调用失败:', backgroundResponse.status);
      return false;
    }

    const backgroundResult = await backgroundResponse.json();

    if (!backgroundResult.success) {
      console.error('❌ 后台生成失败:', backgroundResult.error);
      return false;
    }

    const intelligentQuestions = backgroundResult.data.questions;
    console.log(`✅ 后台生成成功: ${intelligentQuestions.length}道智能题目`);

    // 检查生成的题目质量
    const dimensionCounts = {
      SF: intelligentQuestions.filter((q: any) => q.dimension === 'SF').length,
      IT: intelligentQuestions.filter((q: any) => q.dimension === 'IT').length,
      MV: intelligentQuestions.filter((q: any) => q.dimension === 'MV').length,
      AD: intelligentQuestions.filter((q: any) => q.dimension === 'AD').length,
    };

    console.log('2. 智能题目维度分布:');
    Object.entries(dimensionCounts).forEach(([dim, count]) => {
      console.log(`   ${dim}维度: ${count}题`);
    });

    const totalIntelligent = Object.values(dimensionCounts).reduce((a, b) => a + b, 0);
    console.log(`   总计: ${totalIntelligent}题`);

    // 检查题目内容质量
    console.log('\n3. 题目内容质量检查:');
    
    const sampleQuestion = intelligentQuestions[0];
    const hasValidStructure = sampleQuestion && 
      sampleQuestion.id && 
      sampleQuestion.text && 
      sampleQuestion.options && 
      sampleQuestion.dimension;

    console.log(`   题目结构完整: ${hasValidStructure ? '✅' : '❌'}`);

    // 检查是否包含环保相关内容（基于测试画像）
    const questionTexts = intelligentQuestions.map((q: any) => q.text + ' ' + (q.options || []).join(' ')).join(' ');
    const hasEnvironmentKeywords = /环保|环境|生态|污染|绿色/.test(questionTexts);
    const hasIrrelevantKeywords = /医疗|健康|教育|学校/.test(questionTexts);

    console.log(`   包含环保关键词: ${hasEnvironmentKeywords ? '✅' : '❌'}`);
    console.log(`   避免无关关键词: ${!hasIrrelevantKeywords ? '✅' : '❌'}`);

    // 检查中国本土化
    const hasChineseTools = /微信|钉钉|腾讯|飞书|石墨/.test(questionTexts);
    const hasForeignTools = /Slack|Zoom|Google|Microsoft Teams/.test(questionTexts);

    console.log(`   使用中国工具: ${hasChineseTools ? '✅' : '⚠️ 未检测到'}`);
    console.log(`   避免国外工具: ${!hasForeignTools ? '✅' : '❌'}`);

    // 计算总体质量评分
    const qualityChecks = [
      hasValidStructure,
      hasEnvironmentKeywords,
      !hasIrrelevantKeywords,
      !hasForeignTools,
      totalIntelligent >= 20 // 至少生成20道题
    ];

    const qualityScore = qualityChecks.filter(Boolean).length / qualityChecks.length;
    console.log(`\n智能题目质量评分: ${(qualityScore * 100).toFixed(1)}%`);

    return qualityScore >= 0.8;

  } catch (error) {
    console.error('❌ 智能问题生成测试失败:', error);
    return false;
  }
}

/**
 * 测试前端状态更新
 */
export async function testFrontendStateUpdate() {
  console.log('\n📊 开始测试前端状态更新逻辑...\n');

  try {
    // 检查问卷页面是否可访问
    const response = await fetch('http://localhost:3000/assessment/questionnaire/test-assessment');
    
    if (!response.ok) {
      console.error('❌ 无法访问问卷页面:', response.status);
      return false;
    }

    const html = await response.text();

    // 检查是否包含状态更新相关的代码
    const hasSetQuestionnaire = html.includes('setQuestionnaire') || html.includes('questionnaire');
    const hasQuestionUpdate = html.includes('setQuestions') || html.includes('questions');
    const hasDimensionStats = html.includes('dimensions') && html.includes('totalQuestions');

    console.log('=== 前端状态更新检查结果 ===');
    console.log(`包含问卷状态管理: ${hasSetQuestionnaire ? '✅' : '❌'}`);
    console.log(`包含题目状态管理: ${hasQuestionUpdate ? '✅' : '❌'}`);
    console.log(`包含维度统计: ${hasDimensionStats ? '✅' : '❌'}`);

    const stateScore = [hasSetQuestionnaire, hasQuestionUpdate, hasDimensionStats]
      .filter(Boolean).length / 3;

    console.log(`前端状态管理评分: ${(stateScore * 100).toFixed(1)}%`);

    return stateScore >= 0.8;

  } catch (error) {
    console.error('❌ 前端状态更新测试失败:', error);
    return false;
  }
}

/**
 * 运行所有UI修复测试
 */
export async function runUIFixTests() {
  console.log('🚀 开始运行UI修复测试...\n');
  
  const results = {
    darkMode: false,
    intelligentGeneration: false,
    frontendUpdate: false,
  };

  try {
    // 测试夜间模式
    results.darkMode = await testDarkModeInputVisibility();
    
    // 测试智能问题生成
    results.intelligentGeneration = await testIntelligentQuestionRendering();
    
    // 测试前端状态更新
    results.frontendUpdate = await testFrontendStateUpdate();

    // 输出总结
    console.log('\n=== 🎉 UI修复测试总结 ===');
    console.log(`夜间模式输入框: ${results.darkMode ? '✅ 通过' : '❌ 失败'}`);
    console.log(`智能问题生成: ${results.intelligentGeneration ? '✅ 通过' : '❌ 失败'}`);
    console.log(`前端状态更新: ${results.frontendUpdate ? '✅ 通过' : '❌ 失败'}`);

    const passedTests = Object.values(results).filter(Boolean).length;
    const totalTests = Object.keys(results).length;
    const overallScore = (passedTests / totalTests) * 100;

    console.log(`\n总体通过率: ${passedTests}/${totalTests} (${overallScore.toFixed(1)}%)`);

    if (overallScore >= 80) {
      console.log('🎉 UI修复测试整体通过！');
    } else {
      console.log('⚠️ 部分UI修复需要进一步优化');
    }

    return results;
    
  } catch (error) {
    console.error('❌ UI修复测试执行失败:', error);
    return results;
  }
}

// 如果直接运行此文件，执行所有测试
if (require.main === module) {
  runUIFixTests()
    .then((results) => {
      console.log('UI修复测试执行完成');
      const allPassed = Object.values(results).every(Boolean);
      process.exit(allPassed ? 0 : 1);
    })
    .catch(error => {
      console.error('UI修复测试执行失败:', error);
      process.exit(1);
    });
}
