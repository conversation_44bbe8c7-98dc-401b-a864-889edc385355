/**
 * OCTI智能评估系统 - 增强的组织画像类型定义
 * 
 * 支持方案C的上下文感知差异化生成
 */

import { OrganizationProfile } from '@/services/intelligent-question-generator';

/**
 * 资源特征配置
 */
export interface ResourceProfile {
  fundingStability: 'stable' | 'fluctuating' | 'uncertain';
  volunteerDependency: 'high' | 'medium' | 'low';
  technicalCapacity: 'advanced' | 'moderate' | 'basic';
  partnershipNetwork: 'extensive' | 'moderate' | 'limited';
}

/**
 * 运营特征配置
 */
export interface OperationalProfile {
  decisionMakingStyle: 'centralized' | 'distributed' | 'collaborative';
  innovationOrientation: 'pioneer' | 'follower' | 'conservative';
  riskTolerance: 'high' | 'medium' | 'low';
  changeAdaptability: 'agile' | 'moderate' | 'stable';
}

/**
 * 影响力特征配置
 */
export interface ImpactProfile {
  measurementMaturity: 'advanced' | 'developing' | 'basic';
  stakeholderEngagement: 'proactive' | 'responsive' | 'passive';
  transparencyLevel: 'high' | 'medium' | 'low';
  advocacyCapacity: 'strong' | 'moderate' | 'weak';
}

/**
 * 发展特征配置
 */
export interface DevelopmentProfile {
  growthAmbition: 'aggressive' | 'steady' | 'conservative';
  capacityGaps: string[];
  strategicPriorities: string[];
  challengeAreas: string[];
}

/**
 * 详细特征集合
 */
export interface DetailedCharacteristics {
  resourceProfile: ResourceProfile;
  operationalProfile: OperationalProfile;
  impactProfile: ImpactProfile;
  developmentProfile: DevelopmentProfile;
}

/**
 * 增强的组织画像接口
 */
export interface EnhancedOrganizationProfile extends OrganizationProfile {
  // 新增：细化特征
  detailedCharacteristics: DetailedCharacteristics;
  
  // 新增：上下文标签
  contextTags: string[];
  
  // 新增：独特性指纹
  uniquenessFingerprint: string;
  
  // 新增：分析时间戳
  analyzedAt: string;
  
  // 新增：分析版本
  analysisVersion: string;
}

/**
 * 生成上下文
 */
export interface GenerationContext {
  dimension: string;
  questionCount: number;
  previousAttempts: number;
  avoidancePatterns: string[];
  preferredStyles: string[];
  generationId: string;
  timestamp: number;
}

/**
 * 问题相似度结果
 */
export interface QuestionSimilarity {
  newQuestion: any;
  similarQuestion: any;
  similarity: number;
  reason: string;
}

/**
 * 相似度检查结果
 */
export interface SimilarityCheckResult {
  hasSimilarQuestions: boolean;
  similarities: QuestionSimilarity[];
  uniqueQuestions: any[];
  averageSimilarity: number;
  maxSimilarity: number;
}

/**
 * 评估角度定义
 */
export interface EvaluationAngle {
  id: string;
  name: string;
  description: string;
  applicableContexts: string[];
  questionStyles: string[];
  complexityLevel: 'basic' | 'intermediate' | 'advanced';
  weight: number;
}

/**
 * 提示词模板
 */
export interface PromptTemplate {
  id: string;
  name: string;
  dimension: string;
  template: string;
  applicableProfiles: string[];
  variables: string[];
  complexity: 'basic' | 'intermediate' | 'advanced';
}

/**
 * 生成统计
 */
export interface GenerationStats {
  attempts: number;
  uniquenessScore: number;
  diversityScore: number;
  averageComplexity: number;
  angleDistribution: Record<string, number>;
  generationTime: number;
}
