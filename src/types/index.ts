/**
 * OCTI智能评估系统 - 核心类型定义
 *
 * 定义系统中使用的所有核心类型和接口
 */

// ============================================================================
// 基础类型定义
// ============================================================================

/**
 * 通用响应类型
 */
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
  code?: number;
}

/**
 * 分页参数
 */
export interface PaginationParams {
  page: number;
  pageSize: number;
  total?: number;
}

/**
 * 分页响应
 */
export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination: {
    page: number;
    pageSize: number;
    total: number;
    totalPages: number;
  };
}

// ============================================================================
// 组织相关类型
// ============================================================================

/**
 * 组织类型枚举
 */
export enum OrganizationType {
  NGO = 'ngo',
  FOUNDATION = 'foundation',
  SOCIAL_ENTERPRISE = 'social_enterprise',
  CHARITY = 'charity',
  COMMUNITY_ORG = 'community_org',
  OTHER = 'other',
}

/**
 * 组织规模枚举
 */
export enum OrganizationScale {
  SMALL = 'small', // 1-10人
  MEDIUM = 'medium', // 11-50人
  LARGE = 'large', // 51-200人
  EXTRA_LARGE = 'extra_large', // 200人以上
}

/**
 * 组织发展阶段枚举
 */
export enum DevelopmentStage {
  STARTUP = 'startup', // 初创期
  GROWTH = 'growth', // 成长期
  MATURE = 'mature', // 成熟期
  TRANSFORMATION = 'transformation', // 转型期
}

/**
 * 组织基本信息
 */
export interface Organization {
  id: string;
  name: string;
  type: OrganizationType;
  scale: OrganizationScale;
  developmentStage: DevelopmentStage;
  foundedYear: number;
  location: string;
  website?: string;
  description: string;
  focusAreas: string[];
  createdAt: Date;
  updatedAt: Date;
}

/**
 * 组织画像数据
 */
export interface OrganizationProfile {
  organizationId: string;
  basicInfo: {
    mission: string;
    vision: string;
    values: string[];
    targetBeneficiaries: string[];
  };
  operationalInfo: {
    mainServices: string[];
    geographicScope: string[];
    partnershipTypes: string[];
    fundingSources: string[];
  };
  capacityInfo: {
    coreCompetencies: string[];
    challengeAreas: string[];
    developmentPriorities: string[];
  };
  createdAt: Date;
  updatedAt: Date;
}

// ============================================================================
// 评估相关类型
// ============================================================================

/**
 * 评估状态枚举
 */
export enum AssessmentStatus {
  DRAFT = 'draft',
  PROFILE_COLLECTION = 'profile_collection',
  QUESTIONNAIRE_GENERATION = 'questionnaire_generation',
  IN_PROGRESS = 'in_progress',
  ANALYSIS = 'analysis',
  COMPLETED = 'completed',
  ARCHIVED = 'archived',
}

/**
 * 评估类型枚举
 */
export enum AssessmentType {
  STANDARD = 'standard', // 标准版
  PROFESSIONAL = 'professional', // 专业版
}

/**
 * 评估记录
 */
export interface Assessment {
  id: string;
  organizationId: string;
  type: AssessmentType;
  status: AssessmentStatus;
  title: string;
  description?: string;
  startedAt: Date;
  completedAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}

// ============================================================================
// 问卷相关类型
// ============================================================================

/**
 * 问题类型枚举
 */
export enum QuestionType {
  SINGLE_CHOICE = 'single_choice',
  MULTIPLE_CHOICE = 'multiple_choice',
  SCALE = 'scale',
  TEXT = 'text',
  BOOLEAN = 'boolean',
}

/**
 * 问题来源枚举
 */
export enum QuestionSource {
  PRESET = 'preset', // 预设题目
  AI_GENERATED = 'ai_generated', // AI生成题目
}

/**
 * 问题选项
 */
export interface QuestionOption {
  id: string;
  text: string;
  value: string | number;
  order: number;
}

/**
 * 问题定义
 */
export interface Question {
  id: string;
  type: QuestionType;
  source: QuestionSource;
  category: string;
  title: string;
  description?: string;
  options?: QuestionOption[];
  required: boolean;
  order: number;
  metadata?: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * 问卷定义
 */
export interface Questionnaire {
  id: string;
  assessmentId: string;
  title: string;
  description?: string;
  questions: Question[];
  totalQuestions: number;
  presetQuestions: number;
  aiGeneratedQuestions: number;
  estimatedDuration: number; // 预估完成时间（分钟）
  createdAt: Date;
  updatedAt: Date;
}

/**
 * 用户回答
 */
export interface Response {
  id: string;
  questionId: string;
  assessmentId: string;
  answer: string | number | string[] | boolean;
  confidence?: number; // 回答置信度 0-1
  timeSpent?: number; // 回答耗时（秒）
  createdAt: Date;
  updatedAt: Date;
}

// ============================================================================
// 分析相关类型
// ============================================================================

/**
 * 能力维度枚举
 */
export enum CapabilityDimension {
  GOVERNANCE = 'governance', // 治理能力
  STRATEGY = 'strategy', // 战略能力
  OPERATIONS = 'operations', // 运营能力
  FINANCE = 'finance', // 财务能力
  HUMAN_RESOURCES = 'human_resources', // 人力资源能力
  TECHNOLOGY = 'technology', // 技术能力
  PARTNERSHIPS = 'partnerships', // 合作能力
  IMPACT = 'impact', // 影响力
}

/**
 * 能力评分
 */
export interface CapabilityScore {
  dimension: CapabilityDimension;
  score: number; // 0-100分
  level: 'beginner' | 'developing' | 'proficient' | 'advanced' | 'expert';
  confidence: number; // 置信度 0-1
  strengths: string[];
  weaknesses: string[];
  recommendations: string[];
}

/**
 * 分析结果
 */
export interface AnalysisResult {
  id: string;
  assessmentId: string;
  overallScore: number;
  capabilityScores: CapabilityScore[];
  keyFindings: string[];
  recommendations: string[];
  developmentPriorities: string[];
  nextSteps: string[];
  confidence: number;
  analysisModel: 'minimax' | 'deepseek' | 'hybrid';
  createdAt: Date;
  updatedAt: Date;
}

// ============================================================================
// 报告相关类型
// ============================================================================

/**
 * 报告类型枚举
 */
export enum ReportType {
  SUMMARY = 'summary', // 摘要报告
  DETAILED = 'detailed', // 详细报告
  COMPARISON = 'comparison', // 对比报告
  DEVELOPMENT = 'development', // 发展建议报告
}

/**
 * 报告格式枚举
 */
export enum ReportFormat {
  PDF = 'pdf',
  HTML = 'html',
  JSON = 'json',
}

/**
 * 报告定义
 */
export interface Report {
  id: string;
  assessmentId: string;
  type: ReportType;
  format: ReportFormat;
  title: string;
  content: Record<string, any>;
  generatedAt: Date;
  downloadUrl?: string;
  expiresAt?: Date;
}

// ============================================================================
// 智能体相关类型
// ============================================================================

/**
 * 智能体类型枚举
 */
export enum AgentType {
  QUESTION_DESIGNER = 'question_designer',
  ORGANIZATION_MENTOR = 'organization_mentor',
}

/**
 * 智能体配置
 */
export interface AgentConfig {
  id: string;
  type: AgentType;
  name: string;
  version: string;
  description: string;
  config: Record<string, any>;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * 智能体执行上下文
 */
export interface AgentContext {
  sessionId: string;
  organizationProfile?: OrganizationProfile;
  assessment?: Assessment;
  questionnaire?: Questionnaire;
  responses?: Response[];
  metadata?: Record<string, any>;
}

/**
 * 智能体执行结果
 */
export interface AgentResult<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  metadata?: Record<string, any>;
  executionTime: number;
  tokensUsed?: number;
}

// ============================================================================
// 配置相关类型
// ============================================================================

/**
 * 系统配置
 */
export interface SystemConfig {
  llm: {
    minimax: {
      apiKey: string;
      baseUrl: string;
      model: string;
      maxTokens: number;
    };
    deepseek: {
      apiKey: string;
      baseUrl: string;
      model: string;
      maxTokens: number;
    };
  };
  database: {
    url: string;
    maxConnections: number;
  };
  redis: {
    url: string;
    ttl: number;
  };
  security: {
    jwtSecret: string;
    encryptionKey: string;
  };
}

// ============================================================================
// 工具类型
// ============================================================================

/**
 * 深度可选类型
 */
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};

/**
 * 深度必需类型
 */
export type DeepRequired<T> = {
  [P in keyof T]-?: T[P] extends object ? DeepRequired<T[P]> : T[P];
};

/**
 * 选择性必需类型
 */
export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>;

/**
 * 选择性可选类型
 */
export type OptionalFields<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;
